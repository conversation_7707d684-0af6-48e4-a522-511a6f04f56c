package com.sgs.customerbiz.dbstorages.mybatis.extmodel.dto;

import com.sgs.framework.core.common.PrintFriendliness;

import java.util.List;
import java.util.Map;

public class SearchTrfInfoDTO extends PrintFriendliness {
    // 是否已开单
    private Boolean hasOrder = false;
    //是否关联已经建立TF-Order的数据 且 boundStatus=0的数据 ,非SL目前是这个逻辑
    private Boolean hasConnect = false;
    private Long trfId;

    private String labCode;
    private Integer refSystemId;

    private List<String> trfNos;
    private List<String> packageBarcodes;
    private String orderNo;
    private String createDate;
    private String endDate;
    private String trfStartDate;
    private String trfEndDate;
    private String status;
    private List<String> createBys;
    private Map<String, List<String>> requestMaps;

    private List<Integer> actionStatus;
    private Integer searchType;

    public Integer getSearchType() {
        return searchType;
    }

    public void setSearchType(Integer searchType) {
        this.searchType = searchType;
    }

    public List<Integer> getActionStatus() {
        return actionStatus;
    }

    public void setActionStatus(List<Integer> actionStatus) {
        this.actionStatus = actionStatus;
    }

    public Boolean getHasConnect() {
        return hasConnect;
    }

    public void setHasConnect(Boolean hasConnect) {
        this.hasConnect = hasConnect;
    }

    public Long getTrfId() {
        return trfId;
    }

    public void setTrfId(Long trfId) {
        this.trfId = trfId;
    }

    public Boolean getHasOrder() {
        return hasOrder;
    }

    public void setHasOrder(Boolean hasOrder) {
        this.hasOrder = hasOrder;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public Integer getRefSystemId() {
        return refSystemId;
    }

    public void setRefSystemId(Integer refSystemId) {
        this.refSystemId = refSystemId;
    }

    public List<String> getTrfNos() {
        return trfNos;
    }

    public void setTrfNos(List<String> trfNos) {
        this.trfNos = trfNos;
    }

    public List<String> getPackageBarcodes() {
        return packageBarcodes;
    }

    public void setPackageBarcodes(List<String> packageBarcodes) {
        this.packageBarcodes = packageBarcodes;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getTrfStartDate() {
        return trfStartDate;
    }

    public void setTrfStartDate(String trfStartDate) {
        this.trfStartDate = trfStartDate;
    }

    public String getTrfEndDate() {
        return trfEndDate;
    }

    public void setTrfEndDate(String trfEndDate) {
        this.trfEndDate = trfEndDate;
    }

    public List<String> getCreateBys() {
        return createBys;
    }

    public void setCreateBys(List<String> createBys) {
        this.createBys = createBys;
    }

    public Map<String, List<String>> getRequestMaps() {
        return requestMaps;
    }

    public void setRequestMaps(Map<String, List<String>> requestMaps) {
        this.requestMaps = requestMaps;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
