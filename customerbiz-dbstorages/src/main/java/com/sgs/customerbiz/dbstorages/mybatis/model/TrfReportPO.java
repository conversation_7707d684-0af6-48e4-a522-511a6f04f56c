package com.sgs.customerbiz.dbstorages.mybatis.model;

import com.sgs.customerbiz.model.trf.enums.CustomerConfirmReportEnum;

import java.util.Date;

public class TrfReportPO {
    /**
     * id BIGINT(19) 必填<br>
     * 
     */
    private Long id;

    /**
     * trf_id BIGINT(19) 必填<br>
     * 
     */
    private Long trfId;

    /**
     * lab_id INTEGER(10)<br>
     * 
     */
    private Integer labId;

    /**
     * system_id INTEGER(10)<br>
     * 调用系统id
     */
    private Integer systemId;

    /**
     * order_id VARCHAR(100)<br>
     * 
     */
    private String orderId;

    /**
     * order_no VARCHAR(100)<br>
     * 
     */
    private String orderNo;

    /**
     * report_id VARCHAR(100)<br>
     * 
     */
    private String reportId;

    /**
     * report_no VARCHAR(50)<br>
     * 报告号
     */
    private String reportNo;

    /**
     * origin_report_no VARCHAR(50)<br>
     * originReportNo
     */
    private String originReportNo;

    /**
     * report_status INTEGER(10)<br>
     * 报告状态
     */
    private Integer reportStatus;

    /**
     * report_due_date TIMESTAMP(19)<br>
     * 
     */
    private Date reportDueDate;

    /**
     * review_conclusion VARCHAR(5000)<br>
     * reviewConclusion
     */
    private String reviewConclusion;

    /**
     * comments VARCHAR(5000)<br>
     * 评论
     */
    private String comments;

    /**
     * delivery_flag INTEGER(10)<br>
     * 发送标记
     */
    private Integer deliveryFlag;

    /**
     * active_indicator TINYINT(3) 默认值[1]<br>
     * 有效无效标记：0: inactive, 1: active
     */
    private Integer activeIndicator;

    /**
     * created_by VARCHAR(50) 默认值[system]<br>
     * 创建人
     */
    private String createdBy;

    /**
     * created_date TIMESTAMP(19)<br>
     * 创建时间
     */
    private Date createdDate;

    /**
     * modified_by VARCHAR(50) 默认值[system]<br>
     * 修改人
     */
    private String modifiedBy;

    /**
     * modified_date TIMESTAMP(19)<br>
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * customer_confirm_status VARCHAR(20)<br>
     * 
     */
    private CustomerConfirmReportEnum customerConfirmStatus;

    /**
     * id BIGINT(19) 必填<br>
     * 获得 
     */
    public Long getId() {
        return id;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 设置 
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * trf_id BIGINT(19) 必填<br>
     * 获得 
     */
    public Long getTrfId() {
        return trfId;
    }

    /**
     * trf_id BIGINT(19) 必填<br>
     * 设置 
     */
    public void setTrfId(Long trfId) {
        this.trfId = trfId;
    }

    /**
     * lab_id INTEGER(10)<br>
     * 获得 
     */
    public Integer getLabId() {
        return labId;
    }

    /**
     * lab_id INTEGER(10)<br>
     * 设置 
     */
    public void setLabId(Integer labId) {
        this.labId = labId;
    }

    /**
     * system_id INTEGER(10)<br>
     * 获得 调用系统id
     */
    public Integer getSystemId() {
        return systemId;
    }

    /**
     * system_id INTEGER(10)<br>
     * 设置 调用系统id
     */
    public void setSystemId(Integer systemId) {
        this.systemId = systemId;
    }

    /**
     * order_id VARCHAR(100)<br>
     * 获得 
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * order_id VARCHAR(100)<br>
     * 设置 
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    /**
     * order_no VARCHAR(100)<br>
     * 获得 
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * order_no VARCHAR(100)<br>
     * 设置 
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**
     * report_id VARCHAR(100)<br>
     * 获得 
     */
    public String getReportId() {
        return reportId;
    }

    /**
     * report_id VARCHAR(100)<br>
     * 设置 
     */
    public void setReportId(String reportId) {
        this.reportId = reportId == null ? null : reportId.trim();
    }

    /**
     * report_no VARCHAR(50)<br>
     * 获得 报告号
     */
    public String getReportNo() {
        return reportNo;
    }

    /**
     * report_no VARCHAR(50)<br>
     * 设置 报告号
     */
    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    /**
     * origin_report_no VARCHAR(50)<br>
     * 获得 originReportNo
     */
    public String getOriginReportNo() {
        return originReportNo;
    }

    /**
     * origin_report_no VARCHAR(50)<br>
     * 设置 originReportNo
     */
    public void setOriginReportNo(String originReportNo) {
        this.originReportNo = originReportNo == null ? null : originReportNo.trim();
    }

    /**
     * report_status INTEGER(10)<br>
     * 获得 报告状态
     */
    public Integer getReportStatus() {
        return reportStatus;
    }

    /**
     * report_status INTEGER(10)<br>
     * 设置 报告状态
     */
    public void setReportStatus(Integer reportStatus) {
        this.reportStatus = reportStatus;
    }

    /**
     * report_due_date TIMESTAMP(19)<br>
     * 获得 
     */
    public Date getReportDueDate() {
        return reportDueDate;
    }

    /**
     * report_due_date TIMESTAMP(19)<br>
     * 设置 
     */
    public void setReportDueDate(Date reportDueDate) {
        this.reportDueDate = reportDueDate;
    }

    /**
     * review_conclusion VARCHAR(5000)<br>
     * 获得 reviewConclusion
     */
    public String getReviewConclusion() {
        return reviewConclusion;
    }

    /**
     * review_conclusion VARCHAR(5000)<br>
     * 设置 reviewConclusion
     */
    public void setReviewConclusion(String reviewConclusion) {
        this.reviewConclusion = reviewConclusion == null ? null : reviewConclusion.trim();
    }

    /**
     * comments VARCHAR(5000)<br>
     * 获得 评论
     */
    public String getComments() {
        return comments;
    }

    /**
     * comments VARCHAR(5000)<br>
     * 设置 评论
     */
    public void setComments(String comments) {
        this.comments = comments == null ? null : comments.trim();
    }

    /**
     * delivery_flag INTEGER(10)<br>
     * 获得 发送标记
     */
    public Integer getDeliveryFlag() {
        return deliveryFlag;
    }

    /**
     * delivery_flag INTEGER(10)<br>
     * 设置 发送标记
     */
    public void setDeliveryFlag(Integer deliveryFlag) {
        this.deliveryFlag = deliveryFlag;
    }

    /**
     * active_indicator TINYINT(3) 默认值[1]<br>
     * 获得 有效无效标记：0: inactive, 1: active
     */
    public Integer getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * active_indicator TINYINT(3) 默认值[1]<br>
     * 设置 有效无效标记：0: inactive, 1: active
     */
    public void setActiveIndicator(Integer activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * created_by VARCHAR(50) 默认值[system]<br>
     * 获得 创建人
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * created_by VARCHAR(50) 默认值[system]<br>
     * 设置 创建人
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 获得 创建时间
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 设置 创建时间
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * modified_by VARCHAR(50) 默认值[system]<br>
     * 获得 修改人
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * modified_by VARCHAR(50) 默认值[system]<br>
     * 设置 修改人
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 获得 修改时间
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 设置 修改时间
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * customer_confirm_status VARCHAR(20)<br>
     * 获得 
     */
    public CustomerConfirmReportEnum getCustomerConfirmStatus() {
        return customerConfirmStatus;
    }

    /**
     * customer_confirm_status VARCHAR(20)<br>
     * 设置 
     */
    public void setCustomerConfirmStatus(CustomerConfirmReportEnum customerConfirmStatus) {
        this.customerConfirmStatus = customerConfirmStatus == null ? null : customerConfirmStatus;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", trfId=").append(trfId);
        sb.append(", labId=").append(labId);
        sb.append(", systemId=").append(systemId);
        sb.append(", orderId=").append(orderId);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", reportId=").append(reportId);
        sb.append(", reportNo=").append(reportNo);
        sb.append(", originReportNo=").append(originReportNo);
        sb.append(", reportStatus=").append(reportStatus);
        sb.append(", reportDueDate=").append(reportDueDate);
        sb.append(", reviewConclusion=").append(reviewConclusion);
        sb.append(", comments=").append(comments);
        sb.append(", deliveryFlag=").append(deliveryFlag);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", customerConfirmStatus=").append(customerConfirmStatus);
        sb.append("]");
        return sb.toString();
    }

    public static TrfReportPO reduceByLeft(TrfReportPO left, TrfReportPO right) {
        return left;
    }
}