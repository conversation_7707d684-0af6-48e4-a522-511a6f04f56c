package com.sgs.customerbiz.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.List;

public class CfgTodoListRefSystemIdExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public CfgTodoListRefSystemIdExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNull() {
            addCriterion("parent_id is null");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNotNull() {
            addCriterion("parent_id is not null");
            return (Criteria) this;
        }

        public Criteria andParentIdEqualTo(Integer value) {
            addCriterion("parent_id =", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotEqualTo(Integer value) {
            addCriterion("parent_id <>", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThan(Integer value) {
            addCriterion("parent_id >", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("parent_id >=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThan(Integer value) {
            addCriterion("parent_id <", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThanOrEqualTo(Integer value) {
            addCriterion("parent_id <=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdIn(List<Integer> values) {
            addCriterion("parent_id in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotIn(List<Integer> values) {
            addCriterion("parent_id not in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdBetween(Integer value1, Integer value2) {
            addCriterion("parent_id between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("parent_id not between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andBuCodeIsNull() {
            addCriterion("bu_code is null");
            return (Criteria) this;
        }

        public Criteria andBuCodeIsNotNull() {
            addCriterion("bu_code is not null");
            return (Criteria) this;
        }

        public Criteria andBuCodeEqualTo(String value) {
            addCriterion("bu_code =", value, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeNotEqualTo(String value) {
            addCriterion("bu_code <>", value, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeGreaterThan(String value) {
            addCriterion("bu_code >", value, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("bu_code >=", value, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeLessThan(String value) {
            addCriterion("bu_code <", value, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeLessThanOrEqualTo(String value) {
            addCriterion("bu_code <=", value, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeLike(String value) {
            addCriterion("bu_code like", value, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeNotLike(String value) {
            addCriterion("bu_code not like", value, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeIn(List<String> values) {
            addCriterion("bu_code in", values, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeNotIn(List<String> values) {
            addCriterion("bu_code not in", values, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeBetween(String value1, String value2) {
            addCriterion("bu_code between", value1, value2, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeNotBetween(String value1, String value2) {
            addCriterion("bu_code not between", value1, value2, "buCode");
            return (Criteria) this;
        }

        public Criteria andKeyOfPageIsNull() {
            addCriterion("key_of_page is null");
            return (Criteria) this;
        }

        public Criteria andKeyOfPageIsNotNull() {
            addCriterion("key_of_page is not null");
            return (Criteria) this;
        }

        public Criteria andKeyOfPageEqualTo(String value) {
            addCriterion("key_of_page =", value, "keyOfPage");
            return (Criteria) this;
        }

        public Criteria andKeyOfPageNotEqualTo(String value) {
            addCriterion("key_of_page <>", value, "keyOfPage");
            return (Criteria) this;
        }

        public Criteria andKeyOfPageGreaterThan(String value) {
            addCriterion("key_of_page >", value, "keyOfPage");
            return (Criteria) this;
        }

        public Criteria andKeyOfPageGreaterThanOrEqualTo(String value) {
            addCriterion("key_of_page >=", value, "keyOfPage");
            return (Criteria) this;
        }

        public Criteria andKeyOfPageLessThan(String value) {
            addCriterion("key_of_page <", value, "keyOfPage");
            return (Criteria) this;
        }

        public Criteria andKeyOfPageLessThanOrEqualTo(String value) {
            addCriterion("key_of_page <=", value, "keyOfPage");
            return (Criteria) this;
        }

        public Criteria andKeyOfPageLike(String value) {
            addCriterion("key_of_page like", value, "keyOfPage");
            return (Criteria) this;
        }

        public Criteria andKeyOfPageNotLike(String value) {
            addCriterion("key_of_page not like", value, "keyOfPage");
            return (Criteria) this;
        }

        public Criteria andKeyOfPageIn(List<String> values) {
            addCriterion("key_of_page in", values, "keyOfPage");
            return (Criteria) this;
        }

        public Criteria andKeyOfPageNotIn(List<String> values) {
            addCriterion("key_of_page not in", values, "keyOfPage");
            return (Criteria) this;
        }

        public Criteria andKeyOfPageBetween(String value1, String value2) {
            addCriterion("key_of_page between", value1, value2, "keyOfPage");
            return (Criteria) this;
        }

        public Criteria andKeyOfPageNotBetween(String value1, String value2) {
            addCriterion("key_of_page not between", value1, value2, "keyOfPage");
            return (Criteria) this;
        }

        public Criteria andAliasIsNull() {
            addCriterion("`alias` is null");
            return (Criteria) this;
        }

        public Criteria andAliasIsNotNull() {
            addCriterion("`alias` is not null");
            return (Criteria) this;
        }

        public Criteria andAliasEqualTo(String value) {
            addCriterion("`alias` =", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasNotEqualTo(String value) {
            addCriterion("`alias` <>", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasGreaterThan(String value) {
            addCriterion("`alias` >", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasGreaterThanOrEqualTo(String value) {
            addCriterion("`alias` >=", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasLessThan(String value) {
            addCriterion("`alias` <", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasLessThanOrEqualTo(String value) {
            addCriterion("`alias` <=", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasLike(String value) {
            addCriterion("`alias` like", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasNotLike(String value) {
            addCriterion("`alias` not like", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasIn(List<String> values) {
            addCriterion("`alias` in", values, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasNotIn(List<String> values) {
            addCriterion("`alias` not in", values, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasBetween(String value1, String value2) {
            addCriterion("`alias` between", value1, value2, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasNotBetween(String value1, String value2) {
            addCriterion("`alias` not between", value1, value2, "alias");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdIsNull() {
            addCriterion("ref_system_id is null");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdIsNotNull() {
            addCriterion("ref_system_id is not null");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdEqualTo(Integer value) {
            addCriterion("ref_system_id =", value, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdNotEqualTo(Integer value) {
            addCriterion("ref_system_id <>", value, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdGreaterThan(Integer value) {
            addCriterion("ref_system_id >", value, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("ref_system_id >=", value, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdLessThan(Integer value) {
            addCriterion("ref_system_id <", value, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdLessThanOrEqualTo(Integer value) {
            addCriterion("ref_system_id <=", value, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdIn(List<Integer> values) {
            addCriterion("ref_system_id in", values, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdNotIn(List<Integer> values) {
            addCriterion("ref_system_id not in", values, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdBetween(Integer value1, Integer value2) {
            addCriterion("ref_system_id between", value1, value2, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdNotBetween(Integer value1, Integer value2) {
            addCriterion("ref_system_id not between", value1, value2, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andDisabledIsNull() {
            addCriterion("disabled is null");
            return (Criteria) this;
        }

        public Criteria andDisabledIsNotNull() {
            addCriterion("disabled is not null");
            return (Criteria) this;
        }

        public Criteria andDisabledEqualTo(Integer value) {
            addCriterion("disabled =", value, "disabled");
            return (Criteria) this;
        }

        public Criteria andDisabledNotEqualTo(Integer value) {
            addCriterion("disabled <>", value, "disabled");
            return (Criteria) this;
        }

        public Criteria andDisabledGreaterThan(Integer value) {
            addCriterion("disabled >", value, "disabled");
            return (Criteria) this;
        }

        public Criteria andDisabledGreaterThanOrEqualTo(Integer value) {
            addCriterion("disabled >=", value, "disabled");
            return (Criteria) this;
        }

        public Criteria andDisabledLessThan(Integer value) {
            addCriterion("disabled <", value, "disabled");
            return (Criteria) this;
        }

        public Criteria andDisabledLessThanOrEqualTo(Integer value) {
            addCriterion("disabled <=", value, "disabled");
            return (Criteria) this;
        }

        public Criteria andDisabledIn(List<Integer> values) {
            addCriterion("disabled in", values, "disabled");
            return (Criteria) this;
        }

        public Criteria andDisabledNotIn(List<Integer> values) {
            addCriterion("disabled not in", values, "disabled");
            return (Criteria) this;
        }

        public Criteria andDisabledBetween(Integer value1, Integer value2) {
            addCriterion("disabled between", value1, value2, "disabled");
            return (Criteria) this;
        }

        public Criteria andDisabledNotBetween(Integer value1, Integer value2) {
            addCriterion("disabled not between", value1, value2, "disabled");
            return (Criteria) this;
        }

        public Criteria andSeqNumberIsNull() {
            addCriterion("seq_number is null");
            return (Criteria) this;
        }

        public Criteria andSeqNumberIsNotNull() {
            addCriterion("seq_number is not null");
            return (Criteria) this;
        }

        public Criteria andSeqNumberEqualTo(Integer value) {
            addCriterion("seq_number =", value, "seqNumber");
            return (Criteria) this;
        }

        public Criteria andSeqNumberNotEqualTo(Integer value) {
            addCriterion("seq_number <>", value, "seqNumber");
            return (Criteria) this;
        }

        public Criteria andSeqNumberGreaterThan(Integer value) {
            addCriterion("seq_number >", value, "seqNumber");
            return (Criteria) this;
        }

        public Criteria andSeqNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("seq_number >=", value, "seqNumber");
            return (Criteria) this;
        }

        public Criteria andSeqNumberLessThan(Integer value) {
            addCriterion("seq_number <", value, "seqNumber");
            return (Criteria) this;
        }

        public Criteria andSeqNumberLessThanOrEqualTo(Integer value) {
            addCriterion("seq_number <=", value, "seqNumber");
            return (Criteria) this;
        }

        public Criteria andSeqNumberIn(List<Integer> values) {
            addCriterion("seq_number in", values, "seqNumber");
            return (Criteria) this;
        }

        public Criteria andSeqNumberNotIn(List<Integer> values) {
            addCriterion("seq_number not in", values, "seqNumber");
            return (Criteria) this;
        }

        public Criteria andSeqNumberBetween(Integer value1, Integer value2) {
            addCriterion("seq_number between", value1, value2, "seqNumber");
            return (Criteria) this;
        }

        public Criteria andSeqNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("seq_number not between", value1, value2, "seqNumber");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}