<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfReportMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.customerbiz.dbstorages.mybatis.model.TrfReportPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="trf_id" property="trfId" jdbcType="BIGINT" />
    <result column="lab_id" property="labId" jdbcType="INTEGER" />
    <result column="system_id" property="systemId" jdbcType="INTEGER" />
    <result column="order_id" property="orderId" jdbcType="VARCHAR" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="report_id" property="reportId" jdbcType="VARCHAR" />
    <result column="report_no" property="reportNo" jdbcType="VARCHAR" />
    <result column="origin_report_no" property="originReportNo" jdbcType="VARCHAR" />
    <result column="report_status" property="reportStatus" jdbcType="INTEGER" />
    <result column="report_due_date" property="reportDueDate" jdbcType="TIMESTAMP" />
    <result column="review_conclusion" property="reviewConclusion" jdbcType="VARCHAR" />
    <result column="comments" property="comments" jdbcType="VARCHAR" />
    <result column="delivery_flag" property="deliveryFlag" jdbcType="INTEGER" />
    <result column="active_indicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="customer_confirm_status" property="customerConfirmStatus" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, trf_id, lab_id, system_id, order_id, order_no, report_id, report_no, origin_report_no, 
    report_status, report_due_date, review_conclusion, comments, delivery_flag, active_indicator, 
    created_by, created_date, modified_by, modified_date, customer_confirm_status
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfReportExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_trf_report
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_trf_report
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_trf_report
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfReportExample" >
    delete from tb_trf_report
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfReportPO" >
    insert into tb_trf_report (id, trf_id, lab_id, 
      system_id, order_id, order_no, 
      report_id, report_no, origin_report_no, 
      report_status, report_due_date, review_conclusion, 
      comments, delivery_flag, active_indicator, 
      created_by, created_date, modified_by, 
      modified_date, customer_confirm_status)
    values (#{id,jdbcType=BIGINT}, #{trfId,jdbcType=BIGINT}, #{labId,jdbcType=INTEGER}, 
      #{systemId,jdbcType=INTEGER}, #{orderId,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, 
      #{reportId,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{originReportNo,jdbcType=VARCHAR}, 
      #{reportStatus,jdbcType=INTEGER}, #{reportDueDate,jdbcType=TIMESTAMP}, #{reviewConclusion,jdbcType=VARCHAR}, 
      #{comments,jdbcType=VARCHAR}, #{deliveryFlag,jdbcType=INTEGER}, #{activeIndicator,jdbcType=TINYINT}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, 
      #{modifiedDate,jdbcType=TIMESTAMP}, #{customerConfirmStatus,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfReportPO" >
    insert into tb_trf_report
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="trfId != null" >
        trf_id,
      </if>
      <if test="labId != null" >
        lab_id,
      </if>
      <if test="systemId != null" >
        system_id,
      </if>
      <if test="orderId != null" >
        order_id,
      </if>
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="reportId != null" >
        report_id,
      </if>
      <if test="reportNo != null" >
        report_no,
      </if>
      <if test="originReportNo != null" >
        origin_report_no,
      </if>
      <if test="reportStatus != null" >
        report_status,
      </if>
      <if test="reportDueDate != null" >
        report_due_date,
      </if>
      <if test="reviewConclusion != null" >
        review_conclusion,
      </if>
      <if test="comments != null" >
        comments,
      </if>
      <if test="deliveryFlag != null" >
        delivery_flag,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
      <if test="customerConfirmStatus != null" >
        customer_confirm_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="trfId != null" >
        #{trfId,jdbcType=BIGINT},
      </if>
      <if test="labId != null" >
        #{labId,jdbcType=INTEGER},
      </if>
      <if test="systemId != null" >
        #{systemId,jdbcType=INTEGER},
      </if>
      <if test="orderId != null" >
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportId != null" >
        #{reportId,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="originReportNo != null" >
        #{originReportNo,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null" >
        #{reportStatus,jdbcType=INTEGER},
      </if>
      <if test="reportDueDate != null" >
        #{reportDueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reviewConclusion != null" >
        #{reviewConclusion,jdbcType=VARCHAR},
      </if>
      <if test="comments != null" >
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="deliveryFlag != null" >
        #{deliveryFlag,jdbcType=INTEGER},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="customerConfirmStatus != null" >
        #{customerConfirmStatus,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfReportExample" resultType="java.lang.Integer" >
    select count(*) from tb_trf_report
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_trf_report
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.trfId != null" >
        trf_id = #{record.trfId,jdbcType=BIGINT},
      </if>
      <if test="record.labId != null" >
        lab_id = #{record.labId,jdbcType=INTEGER},
      </if>
      <if test="record.systemId != null" >
        system_id = #{record.systemId,jdbcType=INTEGER},
      </if>
      <if test="record.orderId != null" >
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null" >
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportId != null" >
        report_id = #{record.reportId,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null" >
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.originReportNo != null" >
        origin_report_no = #{record.originReportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportStatus != null" >
        report_status = #{record.reportStatus,jdbcType=INTEGER},
      </if>
      <if test="record.reportDueDate != null" >
        report_due_date = #{record.reportDueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reviewConclusion != null" >
        review_conclusion = #{record.reviewConclusion,jdbcType=VARCHAR},
      </if>
      <if test="record.comments != null" >
        comments = #{record.comments,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryFlag != null" >
        delivery_flag = #{record.deliveryFlag,jdbcType=INTEGER},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.customerConfirmStatus != null" >
        customer_confirm_status = #{record.customerConfirmStatus,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_trf_report
    set id = #{record.id,jdbcType=BIGINT},
      trf_id = #{record.trfId,jdbcType=BIGINT},
      lab_id = #{record.labId,jdbcType=INTEGER},
      system_id = #{record.systemId,jdbcType=INTEGER},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      report_id = #{record.reportId,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      origin_report_no = #{record.originReportNo,jdbcType=VARCHAR},
      report_status = #{record.reportStatus,jdbcType=INTEGER},
      report_due_date = #{record.reportDueDate,jdbcType=TIMESTAMP},
      review_conclusion = #{record.reviewConclusion,jdbcType=VARCHAR},
      comments = #{record.comments,jdbcType=VARCHAR},
      delivery_flag = #{record.deliveryFlag,jdbcType=INTEGER},
      active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      customer_confirm_status = #{record.customerConfirmStatus,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfReportPO" >
    update tb_trf_report
    <set >
      <if test="trfId != null" >
        trf_id = #{trfId,jdbcType=BIGINT},
      </if>
      <if test="labId != null" >
        lab_id = #{labId,jdbcType=INTEGER},
      </if>
      <if test="systemId != null" >
        system_id = #{systemId,jdbcType=INTEGER},
      </if>
      <if test="orderId != null" >
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null" >
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportId != null" >
        report_id = #{reportId,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="originReportNo != null" >
        origin_report_no = #{originReportNo,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null" >
        report_status = #{reportStatus,jdbcType=INTEGER},
      </if>
      <if test="reportDueDate != null" >
        report_due_date = #{reportDueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reviewConclusion != null" >
        review_conclusion = #{reviewConclusion,jdbcType=VARCHAR},
      </if>
      <if test="comments != null" >
        comments = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="deliveryFlag != null" >
        delivery_flag = #{deliveryFlag,jdbcType=INTEGER},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="customerConfirmStatus != null" >
        customer_confirm_status = #{customerConfirmStatus,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfReportPO" >
    update tb_trf_report
    set trf_id = #{trfId,jdbcType=BIGINT},
      lab_id = #{labId,jdbcType=INTEGER},
      system_id = #{systemId,jdbcType=INTEGER},
      order_id = #{orderId,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      report_id = #{reportId,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      origin_report_no = #{originReportNo,jdbcType=VARCHAR},
      report_status = #{reportStatus,jdbcType=INTEGER},
      report_due_date = #{reportDueDate,jdbcType=TIMESTAMP},
      review_conclusion = #{reviewConclusion,jdbcType=VARCHAR},
      comments = #{comments,jdbcType=VARCHAR},
      delivery_flag = #{deliveryFlag,jdbcType=INTEGER},
      active_indicator = #{activeIndicator,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      customer_confirm_status = #{customerConfirmStatus,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_trf_report
      (`id`,`trf_id`,`lab_id`,
      `system_id`,`order_id`,`order_no`,
      `report_id`,`report_no`,`origin_report_no`,
      `report_status`,`report_due_date`,`review_conclusion`,
      `comments`,`delivery_flag`,`active_indicator`,
      `created_by`,`created_date`,`modified_by`,
      `modified_date`,`customer_confirm_status`)
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.trfId, jdbcType=BIGINT},#{ item.labId, jdbcType=INTEGER},
      #{ item.systemId, jdbcType=INTEGER},#{ item.orderId, jdbcType=VARCHAR},#{ item.orderNo, jdbcType=VARCHAR},
      #{ item.reportId, jdbcType=VARCHAR},#{ item.reportNo, jdbcType=VARCHAR},#{ item.originReportNo, jdbcType=VARCHAR},
      #{ item.reportStatus, jdbcType=INTEGER},#{ item.reportDueDate, jdbcType=TIMESTAMP},#{ item.reviewConclusion, jdbcType=VARCHAR},
      #{ item.comments, jdbcType=VARCHAR},#{ item.deliveryFlag, jdbcType=INTEGER},#{ item.activeIndicator, jdbcType=TINYINT},
      #{ item.createdBy, jdbcType=VARCHAR},#{ item.createdDate, jdbcType=TIMESTAMP},#{ item.modifiedBy, jdbcType=VARCHAR},
      #{ item.modifiedDate, jdbcType=TIMESTAMP},#{ item.customerConfirmStatus, jdbcType=VARCHAR}) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_trf_report 
      <set>
        <if test="item.trfId != null"> 
          `trf_id` = #{item.trfId, jdbcType = BIGINT},
        </if> 
        <if test="item.labId != null"> 
          `lab_id` = #{item.labId, jdbcType = INTEGER},
        </if> 
        <if test="item.systemId != null"> 
          `system_id` = #{item.systemId, jdbcType = INTEGER},
        </if> 
        <if test="item.orderId != null"> 
          `order_id` = #{item.orderId, jdbcType = VARCHAR},
        </if> 
        <if test="item.orderNo != null"> 
          `order_no` = #{item.orderNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportId != null"> 
          `report_id` = #{item.reportId, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportNo != null"> 
          `report_no` = #{item.reportNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.originReportNo != null"> 
          `origin_report_no` = #{item.originReportNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportStatus != null"> 
          `report_status` = #{item.reportStatus, jdbcType = INTEGER},
        </if> 
        <if test="item.reportDueDate != null"> 
          `report_due_date` = #{item.reportDueDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.reviewConclusion != null"> 
          `review_conclusion` = #{item.reviewConclusion, jdbcType = VARCHAR},
        </if> 
        <if test="item.comments != null"> 
          `comments` = #{item.comments, jdbcType = VARCHAR},
        </if> 
        <if test="item.deliveryFlag != null"> 
          `delivery_flag` = #{item.deliveryFlag, jdbcType = INTEGER},
        </if> 
        <if test="item.activeIndicator != null"> 
          `active_indicator` = #{item.activeIndicator, jdbcType = TINYINT},
        </if> 
        <if test="item.createdBy != null"> 
          `created_by` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.createdDate != null"> 
          `created_date` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `modified_by` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `modified_date` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.customerConfirmStatus != null"> 
          `customer_confirm_status` = #{item.customerConfirmStatus, jdbcType = VARCHAR},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
    <update id="updateReportAsInvalid">
      update tb_trf_report
      set active_indicator = 0,
          modified_date = now()
    where trf_id in (
        select id from tb_trf where ref_system_id = #{refSystemId} and trf_no in
        <foreach collection="trfNos" item="trfNo" open="(" separator="," close=")">
          #{trfNo}
        </foreach>
      )
      <if test="orderNo != null">
        and `order_no` = #{orderNo}
      </if>
    </update>
</mapper>