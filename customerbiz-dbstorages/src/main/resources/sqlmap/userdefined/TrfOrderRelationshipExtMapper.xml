<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfOrderRelationshipExtMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.customerbiz.dbstorages.mybatis.model.TrfOrderRelationshipPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="trf_id" property="trfId" jdbcType="BIGINT" />
    <result column="order_id" property="orderId" jdbcType="VARCHAR" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="service_start_date" property="serviceStartDate" jdbcType="TIMESTAMP" />
    <result column="system_id" property="systemId" jdbcType="INTEGER" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="modified_time" property="modifiedTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <select id="queryBoundTrfInfo" resultType="com.sgs.customerbiz.facade.model.dto.BoundTrfRelDTO">
    SELECT
    tt.id
    ,tt.trf_no as trfNo
    ,tt.sgs_trf_no as sgsTrfNo
    ,trf.ObjectNo
    ,trf.content
    ,tt.lab_code as labCode
    ,tt.status as trfStatus
    ,tt.ref_system_id AS refSystemId
    <if test="searchType == null or searchType==1 ">
    ,boundTrf.id AS BoundId
    ,boundTrf.order_no AS orderNo
    ,boundTrf.order_id AS orderId
    ,boundTrf.created_date AS createdDate
    ,boundTrf.created_by AS createdBy
    </if>
    <if test="searchType==0 ">
      ,trf.CreatedDate AS createdDate
      ,trf.CreatedBy AS createdBy
    </if>
    ,rea.created_date as cancelDate
    ,rea.created_by as cancelBy
    ,rea.change_subtype as cancelReason
    ,rea.change_remark as cancelReasonContent
    ,tt.system_id as systemId
    FROM tb_customer_trf_info trf
    INNER join tb_trf tt on trf.trfNo = tt.trf_no and trf.refSystemId = tt.ref_system_id and tt.active_indicator = 1 and (tt.source != 2 or tt.source is null)
    <if test="searchType == null or searchType==1 ">
    LEFT JOIN tb_trf_order boundTrf ON boundTrf.trf_no = tt.trf_no and boundTrf.ref_system_id = tt.ref_system_id and boundTrf.bound_status=1
    </if>
    left join tb_trf_log rea on tt.trf_no = rea.trf_no and tt.ref_system_id = rea.ref_system_id and rea.change_type=2
    LEFT JOIN tb_trf_un_display ud ON tt.id = ud.trf_id
    WHERE 1 = 1
    <include refid="query_condition" />
  </select>

  <select id="queryUnBoundTrfInfo" resultType="com.sgs.customerbiz.facade.model.dto.BoundTrfRelDTO">
    SELECT DISTINCT
    trf.id
    ,trf.trfNo
    ,trf.content
    ,trf.labCode
    ,trf.TrfStatus
    FROM tb_customer_trf_info trf
    INNER join tb_trf tt on trf.trfNo = tt.trf_no
    LEFT JOIN tb_trf_order boundTrf ON boundTrf.trf_no = tt.trf_no and boundTrf.ref_system_id = tt.ref_system_id AND boundTrf.bound_status = 1
    WHERE boundTrf.id IS NULL AND tt.status != 9
    <include refid="query_condition" />
  </select>

  <sql id="query_condition">
    <if test="hasOrder">
      AND boundTrf.bound_status = 1
    </if>
    <if test="status!=null and status>0">
      and tt.status = #{status}
    </if>
    <if test="labCode != null and labCode != '' ">
      AND tt.lab_code = #{labCode}
    </if>
    <if test="refSystemId != null and refSystemId != '' ">
      AND tt.ref_system_id = #{refSystemId}
    </if>
    <if test="actionStatus != null">
      and tt.status in
      <foreach collection="actionStatus" item="status" separator="," open="(" close=")">
        #{status}
      </foreach>
    </if>

    <if test="trfNos != null and trfNos.size() > 0 ">
      AND trf.trfNo in
      <foreach collection="trfNos" item="trfNo" separator="," open="(" close=")">
        #{trfNo}
      </foreach>
    </if>
    <if test="packageBarcodes != null and packageBarcodes.size() > 0 ">
      AND trf.ObjectNo in
      <foreach collection="packageBarcodes" item="item" separator="," open="(" close=")">
        #{item}
      </foreach>
    </if>
    <if test="searchType == null or searchType==1 ">
      <if test="orderNo != null and orderNo != '' ">
        AND boundTrf.order_no = #{orderNo}
      </if>
    </if>
    <if test="createDate != null and createDate != '' ">
      <if test="searchType == null or searchType==1 ">
        AND boundTrf.created_date &gt;= #{createDate}
      </if>
      <if test="searchType != null and searchType==0 ">
        AND trf.CreatedDate &gt;= #{createDate}
      </if>
    </if>
    <if test="endDate != null and endDate != '' ">
      <if test="searchType == null or searchType==1 ">
        AND boundTrf.created_date &lt;= #{endDate}
      </if>
      <if test="searchType != null and searchType==0 ">
        AND trf.CreatedDate &lt;= #{endDate}
      </if>
    </if>
    <if test="trfStartDate != null and trfStartDate != '' ">
      AND trf.TrfDate &gt;= #{trfStartDate}
    </if>
    <if test="trfEndDate != null and trfEndDate != '' ">
      AND trf.TrfDate &lt;= #{trfEndDate}
    </if>
    <if test="createBys != null and createBys.size() > 0">
      <if test="searchType == null or searchType==1 ">
        AND boundTrf.created_by in
        <foreach collection="createBys" item="createBy" separator="," open="(" close=")">
          #{createBy}
        </foreach>
      </if>
      <if test="searchType != null and searchType==0 ">
        AND trf.CreatedBy in
        <foreach collection="createBys" item="createBy" separator="," open="(" close=")">
          #{createBy}
        </foreach>
      </if>

    </if>
    <if test="requestMaps != null and requestMaps.size() > 0">
      <foreach collection="requestMaps.entrySet()" index="key" item="val">
        AND (
        trf.Content is NOT null and
        trf.Content != '' and
        <foreach collection="val" item="item" separator="OR">
          trf.`Content` -> '$.${key}' = #{item}
        </foreach>
        )
      </foreach>
    </if>
    AND tt.active_indicator = 1
    AND ud.trf_id is null
    ORDER BY
    trf.CreatedDate DESC
  </sql>


  <select id="getBoundTrfInfoList" resultType="com.sgs.customerbiz.facade.model.dto.BoundTrfRelDTO">
    SELECT distinct
      boundTrf.order_no AS orderNo
      ,boundTrf.order_id AS orderId
    FROM tb_trf tt
    Inner join tb_trf_order boundTrf ON boundTrf.trf_no = tt.trf_no and boundTrf.ref_system_id = tt.ref_system_id
    WHERE tt.trf_no IN
    <foreach collection="trfNos" item="trfNo" separator="," open="(" close=")">
      #{trfNo}
    </foreach>
    AND boundTrf.bound_status = #{boundStatus}
  </select>


</mapper>
