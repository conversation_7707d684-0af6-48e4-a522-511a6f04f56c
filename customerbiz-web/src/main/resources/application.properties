spring.profiles.active=test
spring.application.name=customerbiz
sgs.trace.application.system=SCI
sgs.trace.dynamic.aop.config=execution(public * com.sgs.customerbiz.web.controllers..*Controller.*(..)))
logging.config=classpath:logback.xml
#spring.mvc.favicon.enabled=false
server.servlet.context-path=/customerbiz/api
liteflow.rule-source=config/flow.el.xml
#message-source
spring.messages.basename=i18n/messages
#-1 no expried
spring.messages.cache-seconds= -1
spring.messages.encoding=UTF-8

# Buffer output such that it is only flushed periodically.
server.tomcat.accesslog.buffered=true
# Directory in which log files are created. Can be relative to the tomcat base dir or absolute.
server.tomcat.accesslog.directory=/usr/local/applogs/customerbiz.iapi.sgs.net
# Enable access log.
server.tomcat.accesslog.enabled=true
# Date format to place in log file name.
server.tomcat.accesslog.file-date-format=.yyyy-MM-dd
# Format pattern for access logs.
server.tomcat.accesslog.pattern=%h %l %u %t "%r" %s %b %D %F %{X-FORWARDED-FOR}i
# Log file name prefix.
server.tomcat.accesslog.prefix=access_log
# Defer inclusion of the date stamp in the file name until rotate time.
server.tomcat.accesslog.rename-on-rotate=false
# Set request attributes for IP address, Hostname, protocol and port used for the request.
server.tomcat.accesslog.request-attributes-enabled=false
# Enable access log rotation.
server.tomcat.accesslog.rotate=true
# Log file name suffix.
server.tomcat.accesslog.suffix=.log
# Log file name max-days
server.tomcat.accesslog.max-days=15

# \uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u0534\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD
datasource.validationQuery=SELECT 'x'
# \uFFFD\uFFFD\u02BC\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u04F4\uFFFD\u0421
datasource.initialSize=20
datasource.maxActive=100
datasource.maxIdle=10
datasource.minIdle=5
# \uFFFD\uFFFD\uFFFD\u00FB\uFFFD\u0221\uFFFD\uFFFD\uFFFD\u04F5\u0234\uFFFD\uFFFD\uFFFD\u02B1\uFFFD\uFFFD\u02B1\uFFFD\uFFFD
datasource.maxWait=60000
# \uFFFD\uFFFD\uFFFD\u00FC\uFFFD\uFFFD\uFFFD\uFFFD\u00F2\u017D\uFFFD\uFFFD\uFFFD\u04BB\uFFFD\u03BC\uFFFD\u28EC\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u04AA\uFFFD\u0631\u0575\u013F\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u04E3\uFFFD\uFFFD\uFFFD\u03BB\uFFFD\u01FA\uFFFD\uFFFD\uFFFD
datasource.timeBetweenEvictionRunsMillis=60000
# \uFFFD\uFFFD\uFFFD\uFFFD\u04BB\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u06B3\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u0421\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u02B1\uFFFD\u48EC\uFFFD\uFFFD\u03BB\uFFFD\u01FA\uFFFD\uFFFD\uFFFD
datasource.minEvictableIdleTimeMillis=300000
datasource.maxPoolPreparedStatementPerConnectionSize=20
datasource.testWhileIdle=true
datasource.testOnBorrow=false
datasource.testOnReturn=false
datasource.poolPreparedStatements=true
datasource.queryTimeout=30
datasource.transactionQueryTimeout=30

management.endpoint.shutdown.enabled=false
management.endpoints.web.exposure.include=shutdown, prometheus

scheduled.whitelist=10018,10020,10021,10022,10023,10024,10025,10026,10028,10029
sgs.trace.kafka.servers=10.168.136.31:9092,10.168.136.28:9092
spring.kafka.producer.properties.interceptor.classes=com.sgs.tools.traceability.interceptor.KafkaProducerInterceptor
spring.kafka.consumer.properties.interceptor.classes=com.sgs.tools.traceability.interceptor.KafkaConsumerInterceptor
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer


api.request.recorder.exclusion-methods[0].method-name=customizedValidation
api.request.recorder.exclusion-methods[0].system-id=62
