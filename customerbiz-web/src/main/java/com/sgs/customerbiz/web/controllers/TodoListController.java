package com.sgs.customerbiz.web.controllers;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.sgs.customerbiz.facade.ICustomerTodoListFacade;
import com.sgs.customerbiz.facade.model.todolist.dto.TrfTodoDTO;
import com.sgs.customerbiz.facade.model.todolist.req.*;
import com.sgs.customerbiz.facade.model.todolist.rsp.CheckTrfInfoRsp;
import com.sgs.customerbiz.facade.model.todolist.rsp.GetTrfDetailPrintRsp;
import com.sgs.framework.core.base.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.ws.rs.PathParam;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/todoList")
@Api(value = "/todoList", tags = "Todo List")
public class TodoListController {

    @Autowired
    private ICustomerTodoListFacade iTodoListFacade;

    /**
     * @param queryRefSystemId
     * @return
     */
    @PostMapping("/refSystemId")
    @ApiOperation(value = "refSystemId")
    public BaseResponse refSystemId(@RequestBody QueryRefSystemId queryRefSystemId) {
        return iTodoListFacade.refSystemId(queryRefSystemId);
    }

    /**
     * @param reqObject
     * @return
     */
    @PostMapping("/trfTodoList")
    @ApiOperation(value = "trfTodoList")
    public BaseResponse trfTodoList(@RequestBody TrfTodoReq reqObject) {
        return iTodoListFacade.trfTodoList(reqObject);
    }

    /**
     * @param reqObject
     * @return
     */
    @PostMapping("/importTrfNo")
    @ApiOperation(value = "importTrfNo")
    public BaseResponse importTrfNo(@RequestBody JsonNode reqObject) {
        return iTodoListFacade.importTrfNo(reqObject);
    }

    @Deprecated
    @PostMapping("/updateTrfInfo")
    @ApiOperation(value = "updateTrfInfo")
    public BaseResponse updateTrfInfo(@RequestBody UpdateTrfInfoReq reqObject) {
        return iTodoListFacade.updateTrfInfo(reqObject);
    }

    /**
     * @return
     */
    @PostMapping("/getTrfInfoList")
    @ApiOperation(value = "getTrfInfoList")
    public BaseResponse getTrfInfoList(@RequestBody JsonNode reqObject) {
        return iTodoListFacade.getTrfInfoList(reqObject);
    }

    @Deprecated
    @PostMapping("/getTrfInfo")
    @ApiOperation(value = "getTrfInfo")
    public BaseResponse getTrfInfo(@RequestBody TrfInfoReq reqObject) {
        return iTodoListFacade.getTrfInfo(reqObject);
    }

    /**
     * @return
     */
    @PostMapping("/checkTrfInfo")
    @ApiOperation(value = "checkTrfInfo")
    public BaseResponse<CheckTrfInfoRsp> checkTrfInfo(@RequestBody CheckTrfInfoReq reqObject) {
        return iTodoListFacade.checkTrfInfo(reqObject);
    }

    @Deprecated
    @PostMapping("/updateTrfBoundStatus")
    @ApiOperation(value = "updateTrfBoundStatus")
    public BaseResponse updateTrfBoundStatus(@RequestBody TrfBoundStatusReq reqObject) {
        return iTodoListFacade.updateTrfBoundStatus(reqObject);
    }

    @PostMapping("/bindTrf")
    @ApiOperation(value = "bindTrf")
    public BaseResponse bindTrf(@RequestBody BindTrfReq reqObject) {
        return iTodoListFacade.bindTrf(reqObject);
    }

    @PostMapping("/unBindTrf")
    @ApiOperation(value = "unBindTrf")
    public BaseResponse unBindTrf(@RequestBody UnBindTrfReq reqObject) {
        return iTodoListFacade.unBindTrf(reqObject);
    }

    /**
     * @return
     */
    @PostMapping("/exportTrfInfoList")
    @ApiOperation(value = "exportTrfInfoList")
    public BaseResponse exportTrfInfoList(@RequestBody JsonNode reqObject) {
        return iTodoListFacade.exportTrfInfoList(reqObject);
    }

    /**
     * @return
     */
    @PostMapping("/getTrfDetailInfo")
    @ApiOperation(value = "getTrfDetailInfo")
    public BaseResponse getTrfDetailInfo(@RequestBody TrfDetailReq reqObject) {
        return iTodoListFacade.getTrfDetailInfo(reqObject);
    }

    /**
     * @return
     */
    @PostMapping("/getCustomerTrfDetailInfo")
    @ApiOperation(value = "getCustomerTrfDetailInfo")
    public BaseResponse getCustomerTrfDetailInfo(@RequestBody TrfDetailReq reqObject) {
        return iTodoListFacade.getCustomerTrfDetailInfo(reqObject);
    }

    @PostMapping("/getTrfStatus")
    @ApiOperation(value = "getTrfStatus")
    public BaseResponse getTrfStatus(@PathParam("type") Integer type){
        return iTodoListFacade.getTrfStatus(type);
    }

    @Deprecated
    @PostMapping("/sendTrfMainInfo")
    @ApiOperation(value = "sendTrfMainInfo")
    public BaseResponse sendTrfMainInfo(@RequestBody SendTrfMainInfoReq reqObject){
        return iTodoListFacade.sendTrfMainInfo(reqObject);
    }

    /**
     *
     * @param reqObject
     * @return
     */
    @PostMapping("/searchTrfNo")
    @ApiOperation(value = "searchTrfNo")
    public BaseResponse<List<TrfTodoDTO>> searchTrfNo(@RequestBody SearchTrfNoReq reqObject){
        return iTodoListFacade.searchTrfNo(reqObject);
    }

    @PostMapping("/checkCustomerRule")
    @ApiOperation(value = "checkCustomerRule")
    public BaseResponse<Integer> checkCustomerRule(@RequestBody SearchTrfNoReq reqObject){
        return iTodoListFacade.checkCustomerRule(reqObject);
    }

    @PostMapping("/getTrfDetailPrintInfo")
    @ApiOperation(value = "getTrfDetailPrintInfo")
    public BaseResponse<GetTrfDetailPrintRsp> getTrfDetailPrintInfo(@Valid @RequestBody GetTrfDetailPrintReq reqObject) {
        return iTodoListFacade.getTrfDetailPrintInfo(reqObject);
    }

}
