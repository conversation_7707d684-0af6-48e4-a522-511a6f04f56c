package com.sgs.customerbiz.web.controllers;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.sgs.customerbiz.biz.convertor.TrfConvertor;
import com.sgs.customerbiz.biz.event.LocalEventFailoverScheduler;
import com.sgs.customerbiz.biz.service.SciTrfBizService;
import com.sgs.customerbiz.biz.service.ValidationBizService;
import com.sgs.customerbiz.biz.service.datacollector.CollectedData;
import com.sgs.customerbiz.biz.service.message.MessageService;
import com.sgs.customerbiz.biz.service.task.impl.ScheduledSendReportRequest;
import com.sgs.customerbiz.biz.service.task.impl.ScheduledSendReportService;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.*;
import com.sgs.customerbiz.dbstorages.mybatis.model.*;
import com.sgs.customerbiz.dfv.enums.ActiveIndicatorEnum;
import com.sgs.customerbiz.dfv.helper.GenDfvConfigService;
import com.sgs.customerbiz.domain.domainevent.*;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.domain.domainservice.TrfOrderDomainService;
import com.sgs.customerbiz.domain.domainservice.TrfReportDomainService;
import com.sgs.customerbiz.domain.enums.TaskStatusEnum;
import com.sgs.customerbiz.domain.enums.TrfEventTriggerBy;
import com.sgs.customerbiz.facade.model.req.MessageSendReq;
import com.sgs.customerbiz.facade.model.rsp.MessageSendRsp;
import com.sgs.customerbiz.infrastructure.api.IdService;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.TrfReportDTO;
import com.sgs.customerbiz.model.trf.dto.TrfSyncHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.customerbiz.model.trf.dto.resp.TestLineMappingInfoV2DTO;
import com.sgs.customerbiz.model.trf.enums.DeliveryFlagEnum;
import com.sgs.customerbiz.validation.dbstorages.mybatis.model.SGSCustomerFieldPO;
import com.sgs.customerbiz.validation.service.TestMappingQueryService;
import com.sgs.customerbiz.validation.service.ValidationConfigInitializer;
import com.sgs.customerbiz.validation.utils.CustomerDataUtils;
import com.sgs.customerbiz.validation.validator.veyer.VeyerSupplier;
import com.sgs.customerbiz.web.controllers.tool.TrfOrderReq;
import com.sgs.customerbiz.web.request.ResendLocalEvent;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import io.swagger.annotations.Api;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

import static com.sgs.customerbiz.core.constants.Constants.USER_DEFAULT;
import static com.sgs.customerbiz.validation.service.config.ValidationConfigMaker.mkOnlyOneHaveValue;
import com.sgs.customerbiz.core.config.EmailRecipientConfig;

/**
 *
 */
@RestController
@RequestMapping("/tool")
@Slf4j
@Deprecated
@Api(value = "/tool", tags = "tool")
public class ToolController {
    private static final String TOKEN = "1U23U4UASKDFHIU1234POP2IU3N4N888";

    @Autowired
    private TrfDomainService trfDomainService;

    @Autowired
    private TrfOrderDomainService trfOrderDomainService;

    @Autowired
    private LocalEventFailoverScheduler localEventFailoverScheduler;
    @Autowired
    private LocalEventFailoverMapper localEventFailoverMapper;

    @Autowired
    private LocalEventMapper localEventMapper;

    @Autowired
    private IdService idService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Autowired
    private ApiRequestMapper apiRequestMapper;

    @Autowired
    private SciTrfBizService sciTrfBizService;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    private TrfLogMapper trfLogMapper;

    @Autowired
    private TrfReportMapper trfReportMapper;

    @Autowired
    private GenDfvConfigService genDfvConfigService;

    @Autowired
    private ValidationConfigInitializer validationConfigInitializer;

    @Autowired
    private ValidationBizService validationBizService;

    @Autowired
    private ConfigClient configClient;

    @Autowired
    private ScheduledSendReportService scheduledSendReportService;

    @Autowired
    private TrfReportDomainService trfReportDomainService;

    @Autowired
    private EmailRecipientConfig emailRecipientConfig;

    @GetMapping("/test/customer/data")
    public String testCustomerData(){
        Object data = configClient.getCustomerDataConfig(10026, "$.customerData.supplierList");
        VeyerSupplier.VeyerSupplierData object = new VeyerSupplier.VeyerSupplierData();
        object.setFactoryNo("F00028");
        object.setVendorNo("S00020");
        return CustomerDataUtils.containsObject(JSON.toJSONString(data), object) ? "OK" : "Fail";
    }

    @Data
    public static class MockTestMappingBody {
        private List<TestLineMappingInfoV2DTO> mockData;
    }

    @PostMapping("/gen/dfv/config")
    public String genDfvConfig(@RequestBody GenDfvConfigService.GenConfig genConfig){
        genDfvConfigService.gen(genConfig);
        return "Ok";
    }

    @GetMapping("/gen/validation/config")
    public String genValidationConfig(){
        validationConfigInitializer.writeRuleConfig();
//                validationConfigInitializer.writeCustomerFieldConfig(LowesMaker.getLowesValidationConfigs(),
//                "CG0000846",
//                "370196",
//                10018,
//                "SL",
//                0,
//                0);
        //        validationConfigInitializer.writeCustomerFieldConfig(F21Maker.getValidationConfigs(),
//                "CG0000059",
//                "4035949",
//                10020,
//                "SL",
//                21,
//                22);

//        validationConfigInitializer.writeCustomerFieldConfig(JoAnnMaker.getValidationConfigs(),
//                "CG0000836",
//                "138261",
//                10021,
//                "SL",
//                29,
//                34);
//
//        validationConfigInitializer.writeCustomerFieldConfig(TargetMaker.getValidationConfigs(),
//                "CG0000219",
//                "176582",
//                10023,
//                "SL",
//                32,
//                39);
//        validationConfigInitializer.writeCustomerFieldConfig(BigLotsMaker.getValidationConfigs(),
//        "CG0000538",
//        "1285714",
//        10024,
//        "SL",
//        48,
//        56);
//        validationConfigInitializer.writeCustomerFieldConfig(VeyerMaker.getValidationConfigs(),
//                "CG0000830",
//                "6009906",
//                10026,
//                "SL",
//                56,
//                64);
//        validationConfigInitializer.writeCustomerFieldConfig(WalmartMaker.getValidationConfigs(),
//                "CG0000647",
//                "1985292",
//                10022,
//                "SL",
//                77,
//                87);
//        validationConfigInitializer.writeCustomerFieldConfig(DollarTreeMaker.getValidationConfigs(),
//                "CG0000896",
//                "369878",
//                10025,
//                "SL",
//                101,
//                119);
        return "Ok";
    }
    @PostMapping("/gen/validation/field")
    public String addField(){
        SGSCustomerFieldPO entity = new SGSCustomerFieldPO();
        entity.setId(105);
        entity.setModelName("Trf");
        entity.setFieldId(null);
        entity.setCustomerGroupCode("CG0000896");
        entity.setCustomerNo("369878");
        entity.setRefSystemId(10025);
        entity.setServiceUnit("Testing");
        entity.setProductLineCode("SL");
        entity.setCustomerFieldName("customerProductId");
        entity.setFieldType("String");
        entity.setModelFieldExpr("$.Trf[0].customerProductList[0].id.customerProductId");
        validationConfigInitializer.addCustomerField(entity);
        return "Ok";
    }

    @PostMapping("/gen/validation/field_rule")
    public String addFieldRule(){
//        validationConfigInitializer.addFieldRule(59, 126, mkVeyerSupplier(
//                "$.Order[0].customerList[customerUsage=6][0].customerName",
//                "$.Order[0].productList[0].productAttrList[labelCode='FactoryID'][0].value",
//                "$.Order[0].customerList[customerUsage=5][0].customerName",
//                "$.Order[0].productList[0].productAttrList[labelCode='VendorNo'][0].value"
//        ));
        validationConfigInitializer.addFieldRule(105, 128, mkOnlyOneHaveValue(
                "$.Trf[0].purchaseOrderList[0].id.purchaseOrderId",
                "purchaseOrderId"
        ));
        return "Ok";
    }

    @Data
    public static class TestValidationCollectedData {
        private Integer refSystemId;
        private String buCode;
        private String labCode;
        private CollectedData collectedData;
    }

    @PostMapping("/scheduled/send/report")
    public void sendReport(@RequestBody ScheduledSendReportRequest send) {
        scheduledSendReportService.sendReport(send);
    }


    @PostMapping("/updateTrfAndOrderInfo")
    public BaseResponse updateTrfAndOrderInfo(@RequestBody TrfOrderReq trfOrderReq, @RequestHeader(name = "Sgstoken") String sgsToken) {
        if (!Objects.equals(sgsToken, TOKEN)) {
            return null;
        }
        String trfNos = trfOrderReq.getTrfNos();
        String[] trfNoList = trfNos.split(",");
        StringBuffer sb = new StringBuffer();
        sb.append("处理结果 TrfNos：");
        for (String trfNo : trfNoList) {
            TrfInfoPO trfInfoByTrfNo = trfDomainService.selectByTrfNo(trfOrderReq.getRefSystemId(), trfNo);
            if (Func.isNotEmpty(trfInfoByTrfNo)) {
                if (Func.isNotEmpty(trfOrderReq.getStatus())) {
                    trfInfoByTrfNo.setStatus(trfOrderReq.getStatus());
                }
                // update trf info
                this.updateTrfInfoPO(trfInfoByTrfNo, trfOrderReq);
                if (trfOrderReq.getUpdateOrder()) {
                    String orderNo = trfOrderReq.getOrderNo();
                    if (Func.isNotBlank(orderNo)) {
                        this.updateTrfOrderInfo(trfOrderReq.getRefSystemId(), trfNo, orderNo, trfOrderReq.getStatus(), trfOrderReq.getPendingFlag());
                    } else {
                        // update trf order rel info
                        this.updateTrfOrderInfo(trfOrderReq.getRefSystemId(), trfNo, trfOrderReq.getStatus(), trfOrderReq.getPendingFlag());
                    }
                }
                if (Func.isNotEmpty(trfOrderReq.getReportDeliveryFlag())) {
                    this.updateReportDeliveryFlag(trfInfoByTrfNo.getId(), trfOrderReq.getReportNos(), trfOrderReq.getReportDeliveryFlag());
                }
                sb.append(trfInfoByTrfNo.getTrfNo() + ",");
            }
        }
        return BaseResponse.newInstance(sb.toString());
    }

    private void updateReportDeliveryFlag(Long trfId, String reportNos, Integer deliveryFlag) {
        if (Func.isEmpty(trfId)) {
            throw new BizException("数据异常！");
        }
        TrfReportExample trfReportExample = new TrfReportExample();
        if (Func.isEmpty(reportNos)) {
            trfReportExample.createCriteria().andTrfIdEqualTo(trfId);
        } else {
            trfReportExample.createCriteria().andReportNoIn(Arrays.asList(reportNos.split(","))).andTrfIdEqualTo(trfId);
        }
        List<TrfReportPO> trfReportPOS = trfReportMapper.selectByExample(trfReportExample);
        if (Func.isEmpty(trfReportPOS)) {
            return;
        }
        trfReportPOS.forEach(l -> l.setDeliveryFlag(Func.isEmpty(deliveryFlag) ? 0 : deliveryFlag));
        trfReportMapper.batchUpdate(trfReportPOS);
        this.trfReportDomainService.updateTrfReportDeliveryFlag(trfReportPOS.stream().map(TrfReportPO::getReportNo).collect(Collectors.toList()));
    }

    @PostMapping("/updateOrderStataus")
    public BaseResponse updateOrderStatus(@RequestBody TrfOrderReq trfOrderReq, @RequestHeader(name = "Sgstoken") String sgsToken) {
        if (!Objects.equals(sgsToken, TOKEN)) {
            return null;
        }
        String trfNo = trfOrderReq.getTrfNos();

        String orderNo = trfOrderReq.getOrderNo();

        Integer status = trfOrderReq.getStatus();

        Integer refSystemId = trfOrderReq.getRefSystemId();

        if (Func.isNotEmpty(trfNo) && Func.isNotEmpty(orderNo) && Func.isNotEmpty(status) && Func.isNotEmpty(refSystemId)) {
            this.updateTrfOrderInfo(trfOrderReq.getRefSystemId(), trfNo, orderNo, status, trfOrderReq.getPendingFlag());
        }
        return BaseResponse.newInstance(trfNo);
    }

    @PostMapping("/localEventRef")
    public BaseResponse localEventRef(@RequestBody MessageSendReq messageSendReq, @RequestHeader(name = "Sgstoken") String sgsToken) {
        if (!Objects.equals(sgsToken, TOKEN)) {
            return null;
        }

        LocalEventExample eventExample = new LocalEventExample();
        eventExample.createCriteria().andEventIdEqualTo(Func.toLong(messageSendReq.getMsgId()));
        int i = localEventMapper.countByExample(eventExample);
        if (i == 0) {
            return null;
        }

        LocalEventFailoverPO localEventFailoverPO = new LocalEventFailoverPO();
        localEventFailoverPO.setId(idService.nextId());
        localEventFailoverPO.setEventId(Func.toLong(messageSendReq.getMsgId()));
        localEventFailoverPO.setCreatedAt(new Date());
        localEventFailoverPO.setStatus(0);
        localEventFailoverMapper.insert(localEventFailoverPO);

        localEventFailoverScheduler.failover();
        return BaseResponse.newInstance(Func.toLong(messageSendReq.getMsgId()));
    }

    @PostMapping("/local_event/resend")
    public BaseResponse resendLocalEvent(@RequestBody ResendLocalEvent resendLocalEvent, @RequestHeader(name = "Sgstoken") String sgsToken) {
        if (!Objects.equals(sgsToken, TOKEN)) {
            return null;
        }

        if(resendLocalEvent.invalid()) {
            return BaseResponse.newFailInstance("one of id, eventId and extId must be not none");
        }


        LocalEventExample eventExample = new LocalEventExample();
        LocalEventExample.Criteria query = eventExample.createCriteria();
        Optional.ofNullable(resendLocalEvent.getId()).ifPresent(query::andIdIn);
        Optional.ofNullable(resendLocalEvent.getEventId()).ifPresent(query::andEventIdIn);
        Optional.ofNullable(resendLocalEvent.getExtId()).ifPresent(query::andExtIdIn);
        Optional.ofNullable(resendLocalEvent.getEventType()).ifPresent(query::andEventTypeEqualTo);
        int count = localEventMapper.countByExample(eventExample);
        if(count == 0) {
            return BaseResponse.newInstance("Not Found by " + resendLocalEvent);
        }
        List<LocalEventPO> localEventPOList = localEventMapper.selectByExampleWithBLOBs(eventExample);
        List<LocalEventPO> groupByEventId = localEventPOList.stream()
                .collect(Collectors.groupingBy(
                        LocalEventPO::getEventType,
                        Collectors.reducing((v1, v2) -> v1)
                ))
                .values().stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
        List<String> result = new ArrayList<>();
        for (LocalEventPO localEventPO : groupByEventId) {
            try {
                Class<?> eventClass = Class.forName(localEventPO.getEventType());
                Object o = JSON.parseObject(localEventPO.getEventContent(), eventClass);
                if(!(o instanceof ObjectEvent)) {
                    result.add("skip if not a ObjectEvent id:" + localEventPO.getId() + " eventId:" + localEventPO.getEventId() + " extId:" + localEventPO.getExtId() + "eventType:" + localEventPO.getEventType());
                    continue;
                }
                ObjectEvent objectEvent = (ObjectEvent) o;
                objectEvent.setEventId(idService.nextId());
                Optional.ofNullable(objectEvent.getPayload()).map(payload -> JSON.parseObject(JSON.toJSONString(payload), TrfFullDTO.class)).ifPresent(objectEvent::setPayload);
                if(resendLocalEvent.isResetReportDeliveryFlag() && eventClass.equals(TrfCompletedEvent.class)) {
                    resetReportDeliveryFlag(objectEvent);
                }
                applicationEventPublisher.publishEvent(o);
                result.add("resend id:" + localEventPO.getId() + " eventId:" + localEventPO.getEventId() + " extId:" + localEventPO.getExtId() + "eventType:" + localEventPO.getEventType());
            } catch (ClassNotFoundException e) {
                return BaseResponse.newInstance("class  " + localEventPO.getEventType() + " Not Found!");
            }
        }

        return BaseResponse.newInstance(result);
    }

    private void resetReportDeliveryFlag(ObjectEvent objectEvent) {
        Optional.ofNullable(objectEvent.getTrfNo()).map(trfNo ->
            trfDomainService.selectByTrfNo(objectEvent.getRefSystemId(), objectEvent.getTrfNo())
        ).ifPresent(trf -> {
            trfReportDomainService.updateTrfReportDeliveryFlag(trf.getId(), DeliveryFlagEnum.NEW.getCode());
            List<TrfReportPO> trfReportPOS = trfReportDomainService.selectByTrfId(trf.getId());
            List<String> reportNos = trfReportPOS.stream().map(TrfReportPO::getReportNo).collect(Collectors.toList());
            trfReportDomainService.updateTrfReportDeliveryFlag(reportNos, trf.getRefSystemId());
        });
    }

    @PostMapping("/createEventByRequestId")
    public BaseResponse createEventByRequestId(@RequestBody MessageSendReq messageSendReq, @RequestHeader(name = "Sgstoken") String sgsToken) {
        if (!Objects.equals(sgsToken, TOKEN)) {
            return null;
        }

        ApiRequestExample apiRequestExample = new ApiRequestExample();
        apiRequestExample.createCriteria().andIdEqualTo(Long.parseLong(messageSendReq.getMsgId()));
        List<ApiRequestPO> list = apiRequestMapper.selectByExample(apiRequestExample);
        if (Func.isEmpty(list)) {
            return null;
        }
        ApiRequestPO apiRequestPO = list.get(0);
        Object requestBody = apiRequestPO.getRequestBody();
        TrfFullDTO jsonObject = JSONObject.parseObject(requestBody.toString(), TrfFullDTO.class);
        TrfHeaderDTO header = jsonObject.getTrfList().get(0);
        TrfSyncReq syncReq = JSONObject.parseObject(requestBody.toString(), TrfSyncReq.class);

        TrfSyncHeaderDTO trfSyncHeaderDTO = JSONObject.parseObject(JSONObject.toJSONString(header), TrfSyncHeaderDTO.class);
        syncReq.setHeader(trfSyncHeaderDTO);

        Integer statusForm = 1;
        TrfFullDTO trfDOParam = TrfConvertor.toTrfFullDTO(header, syncReq);
        TrfEvent event = new TrfUnBindEvent(TrfEventTriggerBy.PREORDER);
        event.setPayload(trfDOParam);
        event.setTriggerBy(TrfEventTriggerBy.PREORDER);
        event.setRefSystemId(header.getRefSystemId());
        event.setProductLineCode(syncReq.getProductLineCode());
        event.setSystemId(header.getSystemId());
        event.setSourceId(apiRequestPO.getRequestId());
        event.setTrfNo(header.getTrfNo());
        event.setStatusFrom(statusForm);
        event.setStatusTo(com.sgs.framework.model.enums.TrfStatusEnum.ToBeBound.getStatus());
        applicationEventPublisher.publishEvent(event);
        return BaseResponse.newInstance(header.getTrfNo());
    }

    @PostMapping("/createEventByOrderRequest")
    public BaseResponse createEventByOrderRequest(@RequestBody TrfFullDTO trfDOParam, @RequestHeader(name = "Sgstoken") String sgsToken) {
        if (!Objects.equals(sgsToken, TOKEN)) {
            return null;
        }
        String action = trfDOParam.getAction();
        if (Func.isBlank(action)) {
            return null;
        }

        List<TrfHeaderDTO> trfList = trfDOParam.getTrfList();
        if (Func.isEmpty(trfList)) {
            return null;
        }
        TrfEvent event = null;
        StringBuilder sb = new StringBuilder();
        sb.append("处理结果，TrfNos：");
        for (TrfHeaderDTO trf : trfList) {

            switch (action) {
                case "SyncUnBind":
                    event = new TrfUnBindEvent(TrfEventTriggerBy.PREORDER);
                    break;
                case "SyncToOrder":
                    event = new TrfToOrderEvent(TrfEventTriggerBy.PREORDER);
                    break;
                case "SyncConfirmed":
                    event = new TrfConfirmedEvent(TrfEventTriggerBy.PREORDER);
                    break;
                case "SyncTesting":
                    event = new TrfTestingEvent(TrfEventTriggerBy.PREORDER);
                    break;
                case "SyncCompleted":
                    event = new TrfCompletedEvent(TrfEventTriggerBy.PREORDER);
                    List<TrfReportDTO> reportList = trfDOParam.getReportList();
                    if (Func.isEmpty(reportList)) {
                        return null;
                    }
                    List<String> reportNos = reportList.stream().map(TrfReportDTO::getReportNo).filter(Func::isNotEmpty).collect(Collectors.toList());

                    TrfReportExample trfReportExample = new TrfReportExample();
                    trfReportExample.createCriteria().andReportNoIn(reportNos).andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
                    List<TrfReportPO> trfReportPOS = trfReportMapper.selectByExample(trfReportExample);
                    if (Func.isEmpty(trfReportPOS)) {
                        return null;
                    }
                    trfReportPOS.forEach(l -> l.setDeliveryFlag(DeliveryFlagEnum.NEW.getCode()));
                    trfReportMapper.batchUpdate(trfReportPOS);
                    break;
                case "SyncClosed":
                    event = new TrfClosedEvent(TrfEventTriggerBy.PREORDER);
                    break;
            }

            if (Func.isEmpty(event)) {
                return null;
            }
            event.setPayload(trfDOParam);
            event.setTriggerBy(TrfEventTriggerBy.PREORDER);
            event.setRefSystemId(trf.getRefSystemId());
            event.setProductLineCode(trfDOParam.getProductLineCode());
            event.setSystemId(trfDOParam.getSystemId());
            event.setSourceId(trfDOParam.getRequestId());
            event.setTrfNo(trf.getTrfNo());
//        event.setStatusFrom(statusForm);
//        event.setStatusTo(com.sgs.framework.model.enums.TrfStatusEnum.ToBeBound.getStatus());
            applicationEventPublisher.publishEvent(event);

            sb.append(trf.getTrfNo() + ",");
        }

        return BaseResponse.newInstance(sb.toString());
    }

    @PostMapping("/messageSend")
    public BaseResponse<MessageSendRsp> send(
            @RequestBody MessageSendReq messageSendReq,
            @RequestHeader(name = "Sgstoken") String sgsToken) {
        if (!Objects.equals(sgsToken, TOKEN)) {
            return null;
        }

        return BaseResponse.newSuccessInstance(messageService.send(messageSendReq));
    }

    @PostMapping("/sendMsgNow")
    public BaseResponse updateTaskInfo(@RequestBody MessageSendReq messageSendReq, @RequestHeader(name = "Sgstoken") String sgsToken) {
        if (!Objects.equals(sgsToken, TOKEN)) {
            return null;
        }
        TaskInfoExample taskInfoExample = new TaskInfoExample();
        taskInfoExample.createCriteria().andTaskIdEqualTo(Long.parseLong(messageSendReq.getMsgId()));
        List<TaskInfoPO> taskInfoPOList = taskInfoMapper.selectByExampleWithBLOBs(taskInfoExample);
        if (CollectionUtils.isEmpty(taskInfoPOList)) {
            log.error("TaskInfo was not found. TaskId={}", messageSendReq.getMsgId());
            throw new BizException("Message was not found");
        }
        TaskInfoPO taskInfoPO = CollUtil.get(taskInfoPOList, 0);
        taskInfoPO.setTaskStatus(TaskStatusEnum.INIT.getCode());
        taskInfoPO.setMaxFail(taskInfoPO.getMaxFail() * 2);
        taskInfoPO.setMaxRetry(taskInfoPO.getMaxRetry() * 2);
        taskInfoPO.setModifiedDate(DateUtil.now());
        taskInfoPO.setModifiedBy(USER_DEFAULT);
        taskInfoPO.setExpiredTime(DateTime.now().plusDays(2).getMillis());
        taskInfoPO.setLastExecTime(0L);
        taskInfoPO.setCreatedBy(USER_DEFAULT);
        taskInfoPO.setCreatedDate(DateUtil.now());
        //去除所有依赖
        taskInfoPO.setDependTask(null);
        taskInfoPO.setDependCondition(null);
        taskInfoMapper.updateByPrimaryKey(taskInfoPO);
        return BaseResponse.newInstance(messageSendReq.getMsgId());
    }

    private void updateTrfInfoPO(TrfInfoPO trfInfoByTrfNo, TrfOrderReq trfOrderReq) {
        if (Func.isNotEmpty(trfInfoByTrfNo)) {
            Integer pendingFlag = trfOrderReq.getPendingFlag();
            if (Func.isNotEmpty(pendingFlag)) {
                trfInfoByTrfNo.setPendingFlag(pendingFlag);
            }
            if (Func.isNotEmpty(trfOrderReq.getStatus())) {
                trfInfoByTrfNo.setStatus(trfInfoByTrfNo.getStatus());
            }
            trfDomainService.updateByPrimaryKey(trfInfoByTrfNo);
        }
    }

    private void updateTrfOrderInfo(Integer refSystemId, String trfNo, Integer status, Integer pendingFlag) {
        if (Func.isEmpty(status)) {
            return;
        }
        trfOrderDomainService.updateOrderStatus(trfNo, refSystemId, status, pendingFlag);
    }


    private void updateTrfOrderInfo(Integer refSystemId, String trfNo, String orderNo, Integer status, Integer pendingFlag) {
        if (Func.isEmpty(status) && Func.isEmpty(pendingFlag)) {
            return;
        }

        trfOrderDomainService.updateOrderStatus(trfNo, orderNo, refSystemId, status, pendingFlag);
    }

    @PostMapping("/email/recipients")
    public BaseResponse<EmailRecipientConfig> getEmailRecipients(@RequestHeader(name = "Sgstoken") String sgsToken) {
        if (!Objects.equals(sgsToken, TOKEN)) {
            return null;
        }
        EmailRecipientConfig config = new EmailRecipientConfig();
        BeanUtils.copyProperties(emailRecipientConfig, config);
        return BaseResponse.newSuccessInstance(config);
    }

}
