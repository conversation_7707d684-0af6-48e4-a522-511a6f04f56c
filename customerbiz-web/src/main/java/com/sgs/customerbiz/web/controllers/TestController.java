package com.sgs.customerbiz.web.controllers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeType;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.sgs.config.api.dto.EventSubscribeDTO;
import com.sgs.config.api.service.EventSubscribeService;
import com.sgs.customerbiz.biz.convert.impl.ImportJsonDataConvertor;
import com.sgs.customerbiz.biz.convert.impl.JsonDataConvertor;
import com.sgs.customerbiz.biz.convert.impl.fn.DffCodeListFn;
import com.sgs.customerbiz.biz.dto.DataSyncExtra;
import com.sgs.customerbiz.biz.dto.TaskInfoDTO;
import com.sgs.customerbiz.biz.dto.TrfEventPoolDTO;
import com.sgs.customerbiz.biz.enums.TaskResultEnum;
import com.sgs.customerbiz.biz.event.handler.DataSyncHandler;
import com.sgs.customerbiz.biz.event.handler.HandleContextHolder;
import com.sgs.customerbiz.biz.service.SciTrfBizService;
import com.sgs.customerbiz.biz.service.SgsMartService;
import com.sgs.customerbiz.biz.service.StarLimsService;
import com.sgs.customerbiz.biz.service.preconvert.DataPreConvertService;
import com.sgs.customerbiz.biz.service.preconvert.DataPreConvertServiceImpl;
import com.sgs.customerbiz.biz.service.task.TaskService;
import com.sgs.customerbiz.biz.service.task.impl.ScheduledSendReportRequest;
import com.sgs.customerbiz.biz.service.task.impl.ScheduledSendReportService;
import com.sgs.customerbiz.biz.service.task.impl.TrfEventPoolService;
import com.sgs.customerbiz.biz.service.task.impl.batch.DollarTreeMerger;
import com.sgs.customerbiz.biz.service.task.impl.batch.SendLocalILayer;
import com.sgs.customerbiz.biz.service.task.impl.batch.SendLocalILayerBodyMerger;
import com.sgs.customerbiz.biz.service.task.impl.batch.TargetBodyMerger;
import com.sgs.customerbiz.biz.service.task.impl.handler.TaskExecResult;
import com.sgs.customerbiz.biz.service.task.impl.handler.model.TrfCtx;
import com.sgs.customerbiz.biz.service.task.impl.handler.model.message.ILayerMessageTaskParameters;
import com.sgs.customerbiz.biz.service.task.impl.handler.model.message.base.MessageHeader;
import com.sgs.customerbiz.biz.service.task.impl.message.ConvertedDataSorter;
import com.sgs.customerbiz.biz.service.task.impl.message.ConvertedDataSorterMgr;
import com.sgs.customerbiz.biz.service.task.impl.message.SorterKey;
import com.sgs.customerbiz.biz.service.task.impl.message.TargetSorter;
import com.sgs.customerbiz.biz.utils.DataSyncExtraUtils;
import com.sgs.customerbiz.biz.utils.JobLogUtil;
import com.sgs.customerbiz.biz.utils.TrfEventCodeMapping;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.core.errorcode.ErrorCodeRegistry;
import com.sgs.customerbiz.core.util.SpringUtil;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.ApiRequestMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.TaskInfoPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfInfoPO;
import com.sgs.customerbiz.domain.domainevent.TrfCompletedEvent;
import com.sgs.customerbiz.domain.domainevent.TrfEvent;
import com.sgs.customerbiz.domain.domainevent.message.MessageTaskCompletedEvent;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.domain.domainservice.TrfOrderDomainService;
import com.sgs.customerbiz.domain.enums.TaskStatusEnum;
import com.sgs.customerbiz.facade.ITrfFacade;
import com.sgs.customerbiz.facade.model.todolist.req.TrfCleanReq;
import com.sgs.customerbiz.infrastructure.api.IdService;
import com.sgs.customerbiz.integration.DffClient;
import com.sgs.customerbiz.integration.dto.DffMappingConfigRsp;
import com.sgs.customerbiz.model.trf.dto.TrfDTO;
import com.sgs.customerbiz.model.trf.dto.req.QueryCustomerFieldValueByLabCode;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.customerbiz.model.tuple.Pair;
import com.sgs.customerbiz.validation.newtypes.RefSystemId;
import com.sgs.customerbiz.validation.props.ValidationProps;
import com.sgs.customerbiz.validation.request.ModelValidationReq;
import com.sgs.customerbiz.validation.service.FieldValidationConfig;
import com.sgs.customerbiz.validation.service.ValidationConfigService;
import com.sgs.customerbiz.validation.service.ValidationDffService;
import com.sgs.customerbiz.validation.service.ValidationFilterService;
import com.sgs.customerbiz.validation.utils.DffUtils;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import io.swagger.annotations.Api;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import com.sgs.customerbiz.biz.service.todolist.TodoListService;

import javax.annotation.Resource;
import javax.ws.rs.PathParam;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 */
@RestController
@RequestMapping("/test")
@Api(value = "/test", tags = "test")
@Slf4j
@Deprecated
public class TestController {
    @Autowired
    private ITrfFacade trfFacade;

    @Autowired
    private SciTrfBizService trfBizService;

    @Autowired
    private ApiRequestMapper apiRequestMapper;

    @Autowired
    private ImportJsonDataConvertor importJsonDataConvertor;

    @Autowired
    private JsonDataConvertor jsonDataConvertor;


    @Autowired
    private TaskService taskService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private DollarTreeMerger dollarTreeMerger;

    @Autowired
    private TargetBodyMerger targetBodyMerger;

    @Autowired
    private TargetSorter targetSorter;

    @Autowired
    private DffCodeListFn dffCodeListFn;

    @Resource
    protected EventSubscribeService eventSubscribeService;

    @Autowired
    private IdService idService;

    @Autowired
    private ScheduledSendReportService scheduledSendReportService;

    @Autowired
    private TrfEventPoolService trfEventPoolService;

    @Autowired
    private SgsMartService sgsMartService;

    @Autowired
    private StarLimsService starLimsService;

    @Autowired
    private ValidationFilterService validationFilterService;

    @Autowired
    private ValidationConfigService validationConfigService;

    @Autowired
    private ValidationDffService validationDffService;

    @Autowired
    private DffClient dffClient;

    @Autowired
    private ConvertedDataSorterMgr sorterMgr;

    @Autowired
    private List<SendLocalILayerBodyMerger> mergers;

    @Autowired
    private ValidationProps props;

    @Data
    public static class MockILayerResp {
        private String msgId;
        private int status;
        private int errorCode;
        private String message;
    }

    @PostMapping("/mock/ilayer/msg")
    public BaseResponse<?> mockILayerReturnMsg(@RequestBody MockILayerResp callbackData) {
        long taskId = Long.parseLong(callbackData.getMsgId());
        TaskInfoPO taskInfo = taskService.findByTaskId(taskId,true);
        boolean updateTaskSuccess;

        boolean ilayerNotifyCustomerSuccess = Objects.equals(200, callbackData.getStatus());
        if (ilayerNotifyCustomerSuccess) {
            updateTaskSuccess = taskService.justUpdateTaskResult(taskInfo,
                    new TaskExecResult(TaskResultEnum.SUCCESS, "success"));
        } else {
            updateTaskSuccess = taskService.justUpdateTaskResult(taskInfo,
                    new TaskExecResult(TaskResultEnum.FAILED, String.format("code:%s ,msg:%s", callbackData.getErrorCode(), callbackData.getMessage())));
        }

        if (!updateTaskSuccess) {
            log.error("CustomerDataSyncCallbackListener4ILayer.doMessage update task failure. taskId:{} ,errCode:{} msg:{}", taskId,
                    callbackData.getErrorCode(), callbackData.getMessage());
        }

        // 更新成功发布事件
        MessageTaskCompletedEvent completedEvent = new MessageTaskCompletedEvent();
        completedEvent.setTaskId(taskId);
        completedEvent.setEventCode(taskInfo.getGroupKey());
        completedEvent.setStatus(ilayerNotifyCustomerSuccess ? TaskStatusEnum.SUCCESS : TaskStatusEnum.FAILED);
        completedEvent.setMessage(callbackData.getMessage());
        String taskParameters = taskInfo.getTaskParameters();
        if (StringUtils.isNotEmpty(taskParameters)) {
            ILayerMessageTaskParameters parameters = JSON.parseObject(taskParameters, ILayerMessageTaskParameters.class);
            MessageHeader msgHeader = parameters.getHeader();
            String sourceId = msgHeader.getSourceId();
            completedEvent.setSourceId(sourceId);
            completedEvent.setSystemId(msgHeader.getSystemId());
        }
        applicationEventPublisher.publishEvent(completedEvent);
        return BaseResponse.newSuccessInstance(new Object());
    }

    @GetMapping("/importConvert")
    public BaseResponse washSgsMartDatas(@PathVariable("source") String source, @PathVariable("template") String template) {
        if (Func.isBlank(source) || Func.isBlank(template)) {
            return BaseResponse.newFailInstance("参数不能为空");
        }

        return BaseResponse.newSuccessInstance(importJsonDataConvertor.convert(source, template));
    }

    @GetMapping("/getErrorCodeDesc")
    public BaseResponse getErrorCodeDesc(@PathParam("type") String errorCode) {
        return BaseResponse.newSuccessInstance(ErrorCodeRegistry.getInstance().getErrorCodeDesc(errorCode));
    }

    @GetMapping("/convert")
    public BaseResponse convert(@PathVariable("source") String source, @PathVariable("template") String template) {
        if (Func.isBlank(source) || Func.isBlank(template)) {
            return BaseResponse.newFailInstance("参数不能为空");
        }
        return BaseResponse.newSuccessInstance(jsonDataConvertor.convert(source, template));
    }

    @Data
    public static class ConvertBody {
        private Long apiId;
        private String bu;
        private Map<String,Object> source;
        private Map<String,Object> template;
    }

    @PostMapping("/convert0")
    public BaseResponse convert0(@RequestBody ConvertBody body) {
        if(!body.getSource().containsKey("customerDff")) {
            Object formId = JSONPath.eval(body.getSource(), "$.orderList[0].productList[0].templateId");
            Object gridId = JSONPath.eval(body.getSource(), "$.orderList[0].sampleList[0].templateId");
            Object customerGroupCode = JSONPath.eval(body.getSource(), "$.orderList[0].customerList[customerUsage=3][0].customerGroupCode");
            Object invoke = dffCodeListFn.invoke(new Object[]{customerGroupCode, body.getBu(), formId, gridId});
            body.getSource().put("customerDff", invoke);
        }
        JSON normed = (JSON)DffUtils.normLabelCodeAndLabelValue(body.getSource());
        JSON converted = jsonDataConvertor.convert(JSONObject.toJSONString(normed), JSONObject.toJSONString(body.getTemplate()));
        Map<Long, SendLocalILayerBodyMerger> mergers = this.mergers.stream().collect(Collectors.toMap(SendLocalILayerBodyMerger::getApiId, Function.identity()));
        Optional<SendLocalILayerBodyMerger> sendLocalILayerBodyMerger = Optional.ofNullable(mergers.get(body.getApiId()));
        if(sendLocalILayerBodyMerger.isPresent()) {
            SendLocalILayer sendLocalILayer = JSON.parseObject(converted.toJSONString(), SendLocalILayer.class);
            List<JSON> bodys = new ArrayList<>();
            bodys.add(sendLocalILayer.getBody());
            JSON merge = sendLocalILayerBodyMerger.get().merge(bodys);
            JSONObject root = new JSONObject();
            root.put("body", merge);
            converted = JSON.parseObject(JSON.toJSONString(root));
        }
        ConvertedDataSorter sorter = sorterMgr.getOrDefault(SorterKey.valueOf(body.getApiId()));

        return BaseResponse.newSuccessInstance(sorter.sort(converted));
    }

    @PostMapping("/convert1")
    public BaseResponse convert1(@RequestBody ConvertBody body) {
        return BaseResponse.newSuccessInstance(importJsonDataConvertor.convert(JSON.toJSONString(body.getSource()), JSON.toJSONString(body.getTemplate())));
    }

    /**
     * @param reqObject
     * @return
     */
    @PostMapping("/importTrf")
    public BaseResponse importTrf(@RequestBody TrfImportReq reqObject) {
        return trfFacade.importTrf(reqObject);
    }

    /**
     * https://blog.csdn.net/weixin_43287892/article/details/*********
     * https://vimsky.com/examples/detail/java-method-org.codehaus.jackson.JsonNode.isArray.html
     *
     * @param reqObject
     * @return
     */
    @PostMapping("/getJsonNode")
    public BaseResponse getJsonNode(@RequestBody JsonNode reqObject) {
        ObjectMapper objectMapper = new ObjectMapper();
        String json = "{\n" +
                "    \"orderNo\":\"\",\n" +
                "    \"customerOrderNo\":\"客户单据编号\",\n" +
                "    \"companyName\":\"品牌方公司简称\",\n" +
                "    \"companyDesc\":\"品牌方公司全称\",\n" +
                "    \"relatedVendorName\":\"供应商简称\",\n" +
                "    \"relatedVendorDesc\":\"供应商全称\",\n" +
                "    \"applyOrgName\":\"申请单位名称\",\n" +
                "    \"applyOrgAddress\":\"申请单位地址\",\n" +
                "    \"contactPerson\":\"申请单位联系人\",\n" +
                "    \"contactPhone\":\"申请单位联系电话\",\n" +
                "    \"contactEmail\":\"申请单位邮箱\",\n" +
                "    \"payerOrgName\":\"付款单位名称\",\n" +
                "    \"payerOrgAddress\":\"付款单位地址\",\n" +
                "    \"brandDisplay\":\"品牌\",\n" +
                "    \"isRecheckDisplay\":\"送检类型（首次检测, 重检）\",\n" +
                "    \"thirdpartyTestingTestpartDisplay\":\"样品类型\",\n" +
                "    \"sampleGradeDisplay\":\"样品等级\",\n" +
                "    \"prdCode\":\"成品款号\",\n" +
                "    \"prdName\":\"成品款名\",\n" +
                "    \"prdF1Display\":\"成品颜色\",\n" +
                "    \"matCode\":\"料号\",\n" +
                "    \"matName\":\"物料名称\",\n" +
                "    \"matF1Display\":\"物料颜色\",\n" +
                "    \"sampleName\":\"样品描述\",\n" +
                "    \"yearDisplay\":\"年度\",\n" +
                "    \"seasonDisplay\":\"季节\",\n" +
                "    \"checkLevelDisplay\":\"安全技术类别\",\n" +
                "    \"checkStandardDispl\":\"执行标准\",\n" +
                "    \"testingCycle\":\"测试周期(常规,加急, 快反加急)\",\n" +
                "    \"remark\":\"备注\",\n" +
                "    \"items\":[\n" +
                "        {\n" +
                "            \"itemName\":\"检测项目\",\n" +
                "            \"itemStandard\":\"测试方法\",\n" +
                "            \"checkItemCode\":\"检测机构检测项目唯一标识\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";


        if (reqObject != null) {
            try {
                json = objectMapper.writeValueAsString(reqObject);
                JsonNode getItems = reqObject.get("items");
                if (getItems != null) {
                    String textValue = getItems.textValue();
                    if (StringUtils.isBlank(textValue)) {

                    }
                }
            } catch (Exception ex) {
                String message = ex.getMessage();
            }
        }
        // OLD，New
        Map<String, String> fieldMaps = Maps.newHashMap();
        fieldMaps.put("orderNo", "trfNo");
        fieldMaps.put("customerOrderNo", "customerGroupName");
        fieldMaps.put("companyName", "companyName");
        fieldMaps.put("companyDesc", "companyDesc");
        fieldMaps.put("testLineIds", "testLineIds");
        fieldMaps.put("citationIds", "citationIds");
        fieldMaps.put("items", "testLines");

        fieldMaps.put("itemName", "evaluationName");
        fieldMaps.put("itemStandard", "citationName");
        fieldMaps.put("checkItemCode", "testItemCode");

        JsonNode rootNode = null;
        try {
            rootNode = objectMapper.readTree(json);

            Map<String, Object> hashMaps = objectMapper.readValue(json, HashMap.class);
            // 数组查询
            String itemname = rootNode.at("/items/1/itemName").asText();

            for (Map.Entry<String, Object> entry : hashMaps.entrySet()) {
                String key = entry.getKey();
                if (StringUtils.equalsIgnoreCase(key, "items")) {
                    int hashCode = entry.hashCode();
                }
                Object jsonNode = entry.getValue();
                if (jsonNode instanceof ArrayList) {
                    List<LinkedHashMap> arrayLists = (ArrayList) jsonNode;
                    if (arrayLists == null || arrayLists.isEmpty()) {
                        continue;
                    }
                    boolean isEmpty = arrayLists.isEmpty();
                    if (arrayLists == null) {

                    }
                }
            }

            JsonNode getItems = rootNode.get("items");
            if (getItems != null) {
                String textValue = getItems.textValue();
                if (StringUtils.isBlank(textValue)) {

                }
            }
            JsonNode pathItems = rootNode.path("items");
            if (pathItems != null) {
                String textValue = pathItems.textValue();
                if (StringUtils.isBlank(textValue)) {

                }
            }

            JsonNode findPath = rootNode.findPath("items");
            if (findPath != null) {
                String textValue = findPath.textValue();
                if (StringUtils.isBlank(textValue)) {

                }
            }
            // 只针对数组items{"A":[1,2]}
            JsonNode itemName = rootNode.at("/items/itemName");
            if (itemName != null) {
                String textValue = itemName.textValue();
                if (StringUtils.isBlank(textValue)) {

                }
            }

            // 遍历某个JsonNode的key和value(value可能是字符串也可能是子jsonNode，但如果value是jsonNode数组的话，是无法读取的)
            Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> jsonNode = fields.next();
                JsonNode value = jsonNode.getValue();
                if (value != null && value.isArray()) {
                    int size = value.size();
                    boolean isArray = value.isArray();
                    boolean asBoolean = value.asBoolean();
                }
                System.err.println("遍历获取key:" + jsonNode.getKey());
                // DIG-8555 A "NullPointerException" could be thrown; "value" is nullable here.
                String valueStr = Objects.nonNull(value) ? value.toString() : "";
                System.err.println("遍历获取值:" + valueStr);
                //System.err.println("遍历获取值:"+value.toString());
            }

            // 创建新节点
            ObjectNode newNode = objectMapper.createObjectNode();
            /*Map<String,? extends JsonNode>;
            newNode.setAll((ObjectNode)rootNode);
            newNode.put("JsonNode", "JsonNodeValue");*/

            // jsonNode1为数组形式， 即在json字符串中为 array形式， 利用json在线解析工具可以看出来
            Iterator<JsonNode> elements = rootNode.elements();
            while (elements.hasNext()) {
                JsonNode node = elements.next();
                if (node instanceof TextNode) {
                    TextNode textNode = (TextNode) node;
                    if (textNode != null) {

                    }
                }
                String text1 = node.asText();

                ArrayNode arrayNode = this.getArrayNode(objectMapper, node);

                if (node.isArray() && !node.isEmpty()) {
                    Iterator<JsonNode> it = node.iterator();
                    while (it.hasNext()) {
                        JsonNode childNode = it.next();
                        Iterator<JsonNode> jsonNodes = childNode.elements();
                        while (jsonNodes.hasNext()) {
                            JsonNode next = jsonNodes.next();
                            String text = next.asText();
                            if (next != null) {
                                boolean isArray = next.isArray();
                                if (isArray) {

                                }
                            }
                        }


                    }
                    JsonNodeType nodeType = node.getNodeType();
                    if (nodeType != null) {
                        Class<JsonNodeType> declaringClass = nodeType.getDeclaringClass();
                        if (declaringClass != null) {

                        }
                    }
                }
                String textVal = node.asText();
                if (StringUtils.isBlank(textVal)) {

                }
            }

            Iterator<String> iterator = rootNode.fieldNames();
            while (iterator.hasNext()) {
                String key = iterator.next();
                String newKey = fieldMaps.get(key);

                // 通过key获取JsonNode节点
                JsonNode node2 = rootNode.get(key);  // 输出为  "value"		带有双引号，属于JsonNode类型
                // 获取节点的String值
                String textVal = node2.asText(); // 输出为 value， 属于String类型
                if (StringUtils.isBlank(textVal)) {

                }
            }

        } catch (Exception ex) {
            String message = ex.getMessage();
            if (ex != null) {

            }
        }
        ObjectNode objectNode = this.getObjectNode(objectMapper, rootNode, fieldMaps);

        return BaseResponse.newInstance(objectNode);
    }

    /**
     * @param objectMapper
     * @param rootNode
     * @param fieldMaps
     */
    public ObjectNode getObjectNode(ObjectMapper objectMapper, JsonNode rootNode, Map<String, String> fieldMaps) {
        // 创建新节点
        ObjectNode objectNode = objectMapper.createObjectNode();
        // 遍历某个JsonNode的key和value(value可能是字符串也可能是子jsonNode，但如果value是jsonNode数组的话，是无法读取的)
        Iterator<Map.Entry<String, JsonNode>> jsonNodes = rootNode.fields();
        while (jsonNodes.hasNext()) {
            Map.Entry<String, JsonNode> entry = jsonNodes.next();
            String fieldKey = fieldMaps.get(entry.getKey());
            if (StringUtils.isBlank(fieldKey)) {
                continue;
            }
            JsonNode jsonNode = entry.getValue();
            if (jsonNode == null) {
                continue;
            }
            if (!(jsonNode.isObject() || jsonNode.isArray())) {
                objectNode.set(fieldKey, jsonNode);
                continue;
            }
            if (!jsonNode.isArray()) {
                continue;
            }
            /*List<JsonNode> itemNames = jsonNode.findValues("itemName");
            for(JsonNode itemName: itemNames){
                System.out.println(itemName.toString());
            }*/
            objectNode.set(fieldKey, this.getArrayNode(objectMapper, jsonNode, fieldMaps));
        }
        return objectNode;
    }

    /**
     * @param objectMapper
     * @param arrayNode
     * @param fieldMaps
     * @return
     */
    public ArrayNode getArrayNode(ObjectMapper objectMapper, JsonNode arrayNode, Map<String, String> fieldMaps) {
        if (arrayNode == null || arrayNode.isEmpty() || !arrayNode.isArray()) {
            return null;
        }
        ArrayNode arrayNodes = objectMapper.createArrayNode();
        Iterator<JsonNode> jsonNodes = arrayNode.iterator();
        while (jsonNodes.hasNext()) {
            JsonNode jsonNode = jsonNodes.next();
            /*boolean isObject = jsonNode.isObject();
            boolean isArray = jsonNode.isArray();
            boolean isTextual = jsonNode.isTextual();
            boolean isContainerNode = jsonNode.isContainerNode();
            boolean isMissingNode = jsonNode.isMissingNode();
            boolean isValueNode = jsonNode.isValueNode();*/
            if (!jsonNode.isObject()) {
                arrayNodes.add(jsonNode);
                continue;
            }
            ObjectNode objectNode = this.getObjectNode(objectMapper, jsonNode, fieldMaps);
            arrayNodes.add(objectNode);
        }
        return arrayNodes;
    }

    /**
     * https://vimsky.com/zh-tw/examples/detail/java-method-com.fasterxml.jackson.databind.ObjectMapper.createObjectNode.html
     *
     * @param dbName
     * @param field_name
     * @param namespace
     * @param elementName
     * @param weight
     * @param attrNS_URI
     * @param attr_localname
     * @param attr_value
     * @throws Exception
     */
    public void includeElementFieldWithWeight(String dbName, String field_name, String namespace, String elementName, double weight, String attrNS_URI, String attr_localname, String attr_value) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        // ObjectNode mainNode = mapper.createObjectNode();
        ObjectNode rootNode = mapper.createObjectNode();
        ArrayNode arrayNodes = mapper.createArrayNode();
        ObjectNode childNode = mapper.createObjectNode();
        childNode.put("namespace-uri", namespace);
        childNode.put("localname", elementName);
        childNode.put("weight", weight);
        // These 3 are new fields that have been added as of 8.0.2 from 03/20/2015 in the Management API.
        childNode.put("attribute-namespace-uri", attrNS_URI);
        childNode.put("attribute-localname", attr_localname);
        childNode.put("attribute-value", attr_value);

        arrayNodes.add(childNode);
        rootNode.putArray("included-element").addAll(arrayNodes);

        // JsonNode 转 JSON：
        String json = mapper.writeValueAsString(rootNode);
        JsonNode path = rootNode.path("included-element");

        //System.out.println( childNode.toString());
        //setDatabaseFieldProperties(dbName,field_name,"included-element",childNode);

    }

    /**
     * @param objectMapper
     * @param rootNode
     * @return
     */
    public ArrayNode getArrayNode(ObjectMapper objectMapper, JsonNode rootNode) {
        if (rootNode == null || rootNode.isEmpty() || !rootNode.isArray()) {
            return null;
        }
        // 根据key值获取对应的值,三种方法都可以
        JsonNode path = rootNode.path("itemName");
        JsonNode resultValue = rootNode.findValue("itemName");
        JsonNode resultPath = rootNode.findPath("itemName");

        // 取出所有key值为companyName的JsonNode的List
        List<JsonNode> findKeys = rootNode.findParents("itemName");
        for (JsonNode result : findKeys) {
            System.err.println(result.toString());
        }

        Iterator<JsonNode> nodeIterator = rootNode.iterator();
        while (nodeIterator.hasNext()) {
            JsonNode elementNode = nodeIterator.next();
            boolean isObject = elementNode.isObject();
            boolean isArray = elementNode.isArray();
            boolean isTextual = elementNode.isTextual();
            boolean isContainerNode = elementNode.isContainerNode();
            boolean isMissingNode = elementNode.isMissingNode();
            boolean isValueNode = elementNode.isValueNode();

            if (elementNode != null) {

            }
            Iterator<Map.Entry<String, JsonNode>> fields = elementNode.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> jsonNode = fields.next();
                String key = jsonNode.getKey();
                JsonNode value = jsonNode.getValue();
                if (value != null && value.isArray()) {
                    int size = value.size();
                }
                System.err.println("遍历获取key:" + jsonNode.getKey());
                // DIG-8555 A "NullPointerException" could be thrown; "value" is nullable here.
                String valueStr = Objects.nonNull(value) ? value.toString() : "";
                System.err.println("遍历获取值:" + valueStr);
                //System.err.println("遍历获取值:"+value.toString());
            }

        }

        // 取出所有key值为number对应的value(如果value中包含子jsonNode并且子jsonNode的key值也为number，是无法捕获到并加入list的)
        List<JsonNode> itemNames = rootNode.findValues("itemName");
        for (JsonNode itemName : itemNames) {
            System.out.println(itemName.toString());
        }

        // 遍历某个JsonNode的key和value(value可能是字符串也可能是子jsonNode，但如果value是jsonNode数组的话，是无法读取的)
        Iterator<Map.Entry<String, JsonNode>> jsonNodes = rootNode.fields();
        while (jsonNodes.hasNext()) {
            Map.Entry<String, JsonNode> jsonNode = jsonNodes.next();
            JsonNode value = jsonNode.getValue();

            System.err.println("遍历获取key:" + jsonNode.getKey());
            System.err.println("遍历获取值:" + value.toString());
        }

        for (int indexNode = 0; indexNode < rootNode.size(); indexNode++) {
            JsonNode jsonNode = rootNode.get(indexNode);
            if (jsonNode == null) {
                continue;
            }
            Iterator<JsonNode> elements = jsonNode.elements();
            while (elements.hasNext()) {
                JsonNode node = elements.next();
                String text = node.asText();
                if (StringUtils.isNotBlank(text)) {

                }
            }
            if (jsonNode == null) {
                continue;
            }
            JsonNode value = jsonNode.findValue("");

        }
        for (JsonNode jsonNode : rootNode) {
            String textValue = jsonNode.textValue();
            if (StringUtils.isBlank(textValue)) {

            }
        }

        Iterator<JsonNode> elements = rootNode.path("items").elements();
        while (elements.hasNext()) {
            JsonNode node = elements.next();
            String id = node.path("id").asText();
            if (StringUtils.isNotBlank(id)) {

            }
        }

        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        Iterator<String> fieldNames = rootNode.fieldNames();
        while (fieldNames.hasNext()) {
            String fieldName = fieldNames.next();
            // 输出为  "value" 带有双引号，属于JsonNode类型
            JsonNode jsonNode = rootNode.get(fieldName);
            // 获取节点的String值
            String textValue = jsonNode.textValue();
            String textVal = jsonNode.asText(); // 输出为 value， 属于String类型
            if (StringUtils.isBlank(textVal)) {

            }
        }

        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> field = fields.next();
            String key = field.getKey();
            JsonNode jsonNode = field.getValue();
            String textValue = jsonNode.textValue();
            if (StringUtils.isBlank(textValue)) {

            }
        }
        ArrayNode arrayNodes = objectMapper.createArrayNode();

        /*ObjectNode childNode = objectMapper.createObjectNode();
        childNode.put( "namespace-uri", namespace);
        childNode.put( "localname", elementName);
        childNode.put("weight", weight);
        // These 3 are new fields that have been added as of 8.0.2 from 03/20/2015 in the Management API.
        childNode.put( "attribute-namespace-uri", attrNS_URI);
        childNode.put( "attribute-localname", attr_localname);
        childNode.put( "attribute-value", attr_value);

        arrayNodes.add(childNode);*/

        return arrayNodes;
    }

    /**
     * https://vimsky.com/examples/detail/java-class-com.fasterxml.jackson.databind.JsonNode.html
     * @param object
     * @param results
     * @param allowedNodeTypes
     * @param <T>
     */
    /*public <T> void validate(T object, ValidationResults results, Set<Class<? extends JsonNode>> allowedNodeTypes) {
        JsonOverlay<?> overlay = (JsonOverlay<?>) object;
        JsonNode json = overlay.toJson();
        boolean isValidJsonType = false;
        for (Class<? extends JsonNode> type : allowedNodeTypes) {
            if (type.isAssignableFrom(json.getClass())) {
                isValidJsonType = true;
                break;
            }
        }
        isValidJsonType = isValidJsonType || json.isMissingNode();
        if (!isValidJsonType) {

        }
    }*/
}
