package com.sgs.customerbiz.domain.domainservice;

import cn.hutool.core.collection.CollUtil;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfCustomerContactMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfCustomerLangMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfCustomerMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.*;
import com.sgs.customerbiz.domain.convertor.TrfCustomerConvertor;
import com.sgs.customerbiz.domain.domainobject.v2.TrfCustomerContactDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfCustomerDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfCustomerLangDOV2;
import com.sgs.customerbiz.infrastructure.api.IdService;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.tool.utils.Func;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class TrfCustomerDomainService {

    @Resource
    private IdService idService;

    @Resource
    private TrfCustomerMapper trfCustomerMapper;

    @Resource
    private TrfCustomerContactMapper trfCustomerContactMapper;

    @Resource
    private TrfCustomerLangMapper trfCustomerLangMapper;


    public void batchSave(Long trfId, List<TrfCustomerDOV2> customerList) {
        Assert.isTrue(CollUtil.isNotEmpty(customerList), ResponseCode.ILLEGAL_ARGUMENT);
        for (TrfCustomerDOV2 trfCustomerDOV2 : customerList) {
            trfCustomerDOV2.setTrfId(trfId);
            trfCustomerDOV2.setId(idService.nextId());
        }
        TrfCustomerExample serviceRequirementExample = new TrfCustomerExample();
        serviceRequirementExample.createCriteria().andTrfIdEqualTo(trfId);
        trfCustomerMapper.deleteByExample(serviceRequirementExample);

        TrfCustomerContactExample trfCustomerContactExample = new TrfCustomerContactExample();
        trfCustomerContactExample.createCriteria().andTrfIdEqualTo(trfId);
        trfCustomerContactMapper.deleteByExample(trfCustomerContactExample);

        TrfCustomerLangExample trfCustomerLangExample = new TrfCustomerLangExample();
        trfCustomerLangExample.createCriteria().andTrfIdEqualTo(trfId);
        trfCustomerLangMapper.deleteByExample(trfCustomerLangExample);


        List<TrfCustomerPO> customerPOList = TrfCustomerConvertor.toCustomerPOList(customerList);
        trfCustomerMapper.batchInsert(customerPOList);

        List<TrfCustomerContactPO> contactPOList = CollUtil.newArrayList();
        for (TrfCustomerDOV2 trfCustomerDOV2 : customerList) {
            List<TrfCustomerContactDOV2> customerContactList = trfCustomerDOV2.getCustomerContactList();
            if (CollUtil.isEmpty(customerContactList)) {
                continue;
            }
            for (TrfCustomerContactDOV2 trfCustomerContactDOV2 : customerContactList) {
                trfCustomerContactDOV2.setId(idService.nextId());
                trfCustomerContactDOV2.setTrfId(trfId);
                trfCustomerContactDOV2.setTrfCustomerId(trfCustomerDOV2.getId());
            }
            List<TrfCustomerContactPO> trfCustomerContactPOList = TrfCustomerConvertor.toTrfCustomerContactPOList(customerContactList);
            contactPOList.addAll(trfCustomerContactPOList);
        }
        if (CollUtil.isNotEmpty(contactPOList)) {
            trfCustomerContactMapper.batchInsert(contactPOList);
        }


        List<TrfCustomerLangPO> customerLangPOList = CollUtil.newArrayList();
        for (TrfCustomerDOV2 trfCustomerDOV2 : customerList) {
            List<TrfCustomerLangDOV2> languageList = trfCustomerDOV2.getLanguageList();
            if (CollUtil.isEmpty(languageList)) {
                continue;
            }
            for (TrfCustomerLangDOV2 customerLangDOV2 : languageList) {
                customerLangDOV2.setId(idService.nextId());
                customerLangDOV2.setTrfCustomerId(trfCustomerDOV2.getId());
                customerLangDOV2.setTrfId(trfId);
            }
            List<TrfCustomerLangPO> trfCustomerLangPOList = TrfCustomerConvertor.toCustomerLangPOList(languageList);
            customerLangPOList.addAll(trfCustomerLangPOList);
        }
        if (CollUtil.isNotEmpty(customerLangPOList)) {
            trfCustomerLangMapper.batchInsert(customerLangPOList);
        }
    }

    public List<TrfCustomerDOV2> selectByTrfId(Long trfId) {
        Assert.notNull(trfId);
        TrfCustomerExample customerExample = new TrfCustomerExample();
        customerExample.createCriteria().andTrfIdEqualTo(trfId);
        List<TrfCustomerPO> customerPOList = trfCustomerMapper.selectByExample(customerExample);
        List<TrfCustomerDOV2> trfCustomerDOList = TrfCustomerConvertor.toCustomerDOList(customerPOList);
        if (Func.isNotEmpty(trfCustomerDOList)) {
            List<Long> customerIdList = trfCustomerDOList.parallelStream().map(TrfCustomerDOV2::getId).collect(Collectors.toList());
            TrfCustomerContactExample customerContactExample = new TrfCustomerContactExample();
            customerContactExample.createCriteria().andTrfCustomerIdIn(customerIdList);
            List<TrfCustomerContactPO> customerContactList = trfCustomerContactMapper.selectByExample(customerContactExample);
            Map<Long, List<TrfCustomerContactPO>> contactPOMap = customerContactList.parallelStream().collect(Collectors.groupingBy(TrfCustomerContactPO::getTrfCustomerId));

            for (TrfCustomerDOV2 trfCustomerDO : trfCustomerDOList) {
                List<TrfCustomerContactPO> trfCustomerContactPOS = contactPOMap.get(trfCustomerDO.getId());
                if (Func.isEmpty(trfCustomerContactPOS)) {
                    continue;
                }
                List<TrfCustomerContactDOV2> trfCustomerContactDOList = TrfCustomerConvertor.toTrfCustomerContactDOList(trfCustomerContactPOS);
                trfCustomerDO.setCustomerContactList(trfCustomerContactDOList);
            }

            TrfCustomerLangExample customerLangExample = new TrfCustomerLangExample();
            customerLangExample.createCriteria().andTrfCustomerIdIn(customerIdList);
            List<TrfCustomerLangPO> customerLangPOList = trfCustomerLangMapper.selectByExample(customerLangExample);
            Map<Long, List<TrfCustomerLangPO>> customerLangPOMap = customerLangPOList.parallelStream().collect(Collectors.groupingBy(TrfCustomerLangPO::getTrfCustomerId));

            for (TrfCustomerDOV2 trfCustomerDO : trfCustomerDOList) {
                List<TrfCustomerLangPO> trfCustomerContactPOS = customerLangPOMap.get(trfCustomerDO.getId());
                if (Func.isEmpty(trfCustomerContactPOS)) {
                    continue;
                }
                List<TrfCustomerLangDOV2> customerLangDOList = TrfCustomerConvertor.toCustomerLangDOList(trfCustomerContactPOS);
                trfCustomerDO.setLanguageList(Func.isEmpty(customerLangDOList) ? new ArrayList<>() : customerLangDOList);
            }
        }
        return trfCustomerDOList;
    }
}
