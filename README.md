# SCI系统 (SGS Customer Information System)

## 项目简介
SCI系统是SGS的业务中台服务系统，主要负责客户相关应用服务，为企业提供全面的客户信息管理与测试报告处理系统。系统围绕TRF(Testing Request Form)的全生命周期管理，提供从TRF创建、状态变更、与订单绑定等一系列功能服务。

## 目录
- [业务视角](#业务视角)
- [技术视角](#技术视角)
- [数据视角](#数据视角)
- [部署视角](#部署视角)
- [模块说明](#模块说明)
- [核心业务流程](#核心业务流程)

## 业务视角
SCI系统主要解决企业客户信息管理和测试报告表单(TRF)全生命周期管理的问题，具体包括：
- TRF的创建与导入（pull模式与push模式）
- TRF与订单(Order)的绑定关系管理
- TRF状态变更同步（确认、测试中、报告生成、完成、关闭等）
- TRF挂起(pending)和解挂(unpending)状态管理

详细业务功能说明请参考：[业务功能说明](./doc/业务功能说明.md)

## 技术视角
系统基于Spring Boot框架，采用DDD(领域驱动设计)思想构建，整体架构分层清晰：
- 表现层：customerbiz-web、customerbiz-facade
- 应用层：customerbiz-facade-impl、customerbiz-process
- 领域层：customerbiz-domain、customerbiz-biz
- 基础设施层：customerbiz-dbstorages、customerbiz-integration

系统使用LiteFlow作为核心业务流程编排引擎，通过配置实现灵活的业务流程定义。

详细技术架构说明请参考：[技术架构说明](./doc/技术架构说明.md)

## 数据视角
系统数据主要围绕客户信息和TRF信息两大核心领域展开：
- 客户基础信息管理
- 测试请求表单(TRF)管理
- 订单信息管理
- TRF与订单关联关系管理

详细数据模型说明请参考：[数据模型说明](./doc/数据模型说明.md)

## 部署视角
系统采用微服务架构，支持容器化部署，主要组件包括：
- 应用服务
- 数据库服务
- 缓存服务
- 消息队列服务
- 监控系统（Prometheus + Grafana）

详细部署架构说明请参考：[部署架构说明](./doc/部署架构说明.md)

## 模块说明
| 模块名称 | 功能描述 |
|---------|---------|
| config-api | 配置中心API |
| config-impl | 配置中心实现 |
| customerbiz-biz | 业务逻辑实现层，包含核心业务逻辑 |
| customerbiz-core | 核心通用组件，如异常处理、缓存、常量定义等 |
| customerbiz-dbstorages | 数据库存储层 |
| customerbiz-dfv | 数据验证相关功能 |
| customerbiz-domain | 领域模型层，包含领域实体和业务规则 |
| customerbiz-facade | 对外接口定义 |
| customerbiz-facade-impl | 接口实现层，负责调用路由分发 |
| customerbiz-facade-model | 接口模型定义 |
| customerbiz-integration | 外部系统集成层 |
| customerbiz-model | 内部数据模型定义 |
| customerbiz-mybatis-generator | MyBatis代码生成工具 |
| customerbiz-process | 业务流程编排层，基于LiteFlow引擎实现 |
| customerbiz-validation | 数据验证层 |
| customerbiz-web | Web应用入口层 |
| sci-common-service | SCI通用服务组件 |

## 核心业务流程
系统主要围绕TRF全生命周期管理构建，核心流程包括：
1. TRF创建流程
   - ImportToTRF流程：通过pull或push模式导入TRF
   - OrderToTRF流程：从订单创建TRF
2. TRF状态变更流程(SyncTRF)
   - 确认(syncConfirm)
   - 测试中(syncTesting)
   - 报告生成(syncReporting)
   - 完成(syncCompleted)
   - 关闭(syncClosed)
3. TRF与订单绑定流程(bindTrf)
4. TRF挂起和解挂流程(pending/unpending)

详细流程说明请参考：[核心业务流程说明](./doc/核心业务流程说明.md)
