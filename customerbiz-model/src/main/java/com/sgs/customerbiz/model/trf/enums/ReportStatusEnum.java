package com.sgs.customerbiz.model.trf.enums;

import com.sgs.framework.model.enums.SubReportStatusEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * @Desc
 * <AUTHOR>
 * @date 2023/11/23 15:21
 */
public enum ReportStatusEnum {
    Cancelled(202, "Cancelled"),
    Approved(203, "Approved"),
    Draft(204, "Draft"),
    Reworked(205, "Reworked"),
    Completed(208, "Completed");

    private int code;
    private String message;

    private ReportStatusEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

}
