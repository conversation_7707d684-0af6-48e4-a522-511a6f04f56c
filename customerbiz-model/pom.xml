<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sgs-customerbiz</artifactId>
        <groupId>com.sgs.customerbiz</groupId>
        <version>1.0.22</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <version>${customerbiz.version}</version>
    <artifactId>customerbiz-model</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.sgs.framework</groupId>
            <artifactId>sgs-framework-core</artifactId>
        </dependency>
        <!--javax.validation验证的包-->
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sgs.testdatabiz</groupId>
            <artifactId>testdatabiz-facade-model</artifactId>
            <version>${testdatabiz.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sgs.framework</groupId>
            <artifactId>sgs-framework-commons</artifactId>
            <version>1.0.575-beta</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sgs.framework</groupId>
            <artifactId>sgs-framework-tool</artifactId>
            <version>1.0.575-beta</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
