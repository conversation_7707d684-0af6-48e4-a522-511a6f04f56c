package com.sgs.customerbiz.facade;

import com.sgs.customerbiz.facade.model.dto.BoundTrfRelDTO;
import com.sgs.customerbiz.facade.model.todolist.req.*;
import com.sgs.customerbiz.facade.model.trf.req.BoundTrfInfoSearchReq;
import com.sgs.customerbiz.facade.model.trf.req.CustomerTrfInfoReq;
import com.sgs.customerbiz.facade.model.trf.rsp.CustomerTrfInfoRsp;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.importtrfresp.TrfImportResult;
import com.sgs.customerbiz.model.trf.dto.req.*;
import com.sgs.customerbiz.model.trf.dto.resp.CustomerConfirmReportFlag;
import com.sgs.framework.core.base.BaseResponse;

import java.util.List;

public interface ITrfFacade {

    /**
     * Reject Inspectorio Trf
     *
     * @param reject
     * @return
     */
    BaseResponse rejectInspectorio(RejectInspectorio reject);

    /**
     * Remove Trf
     *
     * @param reqObject
     * @return
     */
    BaseResponse remove(RemoveTrfReq reqObject);

    /**
     * Cancel Trf
     *
     * @param reqObject
     * @return
     */
    BaseResponse cancel(CancelTrfReq reqObject);

    /**
     * UnDisplay Trf
     *
     * @param reqObject
     * @return
     */
    BaseResponse unDisplay(TrfDisplayReq reqObject);

    /**
     * display Trf
     *
     * @param reqObject
     * @return
     */
    BaseResponse display(TrfDisplayReq reqObject);

    /**
     * @param reqObject
     * @return
     */
    BaseResponse<List<CustomerTrfInfoRsp>> getTrfInfoList(CustomerTrfInfoReq reqObject);

    BaseResponse<List<BoundTrfRelDTO>> getBoundTrfInfoList(BoundTrfInfoSearchReq reqObject);

    BaseResponse<List<CustomerTrfInfoRsp>> getCustomerTrfInfoList(List<String> trfNos, List<String> packageBarcodes);

    BaseResponse getRemoveCancelData(RemoveCancelReq reqObject);


    BaseResponse<TrfImportResult> importTrf(TrfImportReq importReq);

    BaseResponse<TrfHeaderDTO> orderToTrf(OrderToTrfReq orderToTrfReq);

    BaseResponse<TrfHeaderDTO> syncTrf(TrfSyncReq syncReq);

    BaseResponse<TrfHeaderDTO> syncReportToTrf(TrfSyncReq trfSyncReq);

    BaseResponse unbindTrf(TrfUnbindReq unbindReq);

    BaseResponse cancelByCustomer(TrfCancelReq reqObject);

    BaseResponse cancelByPreOrder(TrfCancelReq reqObject);

    BaseResponse returnByCustomer(TrfRemoveReq reqObject);

    BaseResponse returnByPreOrder(TrfRemoveReq reqObject);

    BaseResponse bindTrf(TrfBindReq bindReq);

    BaseResponse<TrfImportResult> exportSgsTrfByTrfNo(TrfImportReq trfImportReq);

    BaseResponse<TrfImportResult> getSgsTrfByTrfNo(String trfNo);

    BaseResponse getConfig(GetConfigReq getConfigReq);

    BaseResponse syncQuotationToTrf(TrfSyncQuotationReq trfQuotationDTO);

    BaseResponse sendQuotationStatus(TrfSyncQuotationStatusReq reqObject);

    BaseResponse getCustomerConfig(GetCustomerConfigReq getConfigReq);

    BaseResponse syncReviewConclusion(SyncReportToTrfReq req);

    BaseResponse getCustomerInfo(CustomerTrfReq req);

    BaseResponse<CustomerConfirmReportFlag> confirmReport(CustomerConfirmedReport confirmedReport);

    BaseResponse<CustomerConfirmReportTrf<CustomerConfirmReportFlag>> queryReportConfirmFlag(CustomerConfirmReportTrf<ReportNo> queryCustomerConfirmReport);

    /**
     * Check if a revised operation can be executed for the given SyncRequest
     *
     * @param syncReq The request
     * @return BaseResponse with Boolean indicating if the revised operation can be executed
     */
    BaseResponse<Boolean> canReviseReport(TrfSyncReq syncReq);
}
