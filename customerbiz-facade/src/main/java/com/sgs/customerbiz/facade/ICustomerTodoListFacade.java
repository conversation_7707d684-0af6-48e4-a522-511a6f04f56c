package com.sgs.customerbiz.facade;

import com.fasterxml.jackson.databind.JsonNode;
import com.sgs.customerbiz.facade.model.todolist.dto.TrfTodoDTO;
import com.sgs.customerbiz.facade.model.todolist.req.*;
import com.sgs.customerbiz.facade.model.todolist.rsp.CheckTrfInfoRsp;
import com.sgs.customerbiz.facade.model.todolist.rsp.GetTrfDetailPrintRsp;
import com.sgs.customerbiz.facade.model.todolist.rsp.TrfInfoRsp;
import com.sgs.customerbiz.facade.model.trf.rsp.CustomerTrfInfoRsp;
import com.sgs.framework.core.base.BaseResponse;

import java.util.List;

public interface ICustomerTodoListFacade {


    /**
     * @param reqObject
     * @return
     */
    BaseResponse refSystemId(QueryRefSystemId reqObject);

    /**
     * @param reqObject
     * @return
     */
    BaseResponse trfTodoList(TrfTodoReq reqObject);

    /**
     * @param reqObject
     * @return
     */
    BaseResponse importTrfNo(JsonNode reqObject);

    /**
     *
     * @param reqObject
     * @return
     */
    BaseResponse updateTrfInfo(UpdateTrfInfoReq reqObject);

    /**
     * @param reqObject
     * @return
     */
    BaseResponse getTrfInfoList(JsonNode reqObject);

    /**
     *
     * @param reqObject
     * @return
     */
    BaseResponse exportTrfInfoList(JsonNode reqObject);

    /**
     *
     * @param reqObject
     * @return
     */
    BaseResponse getTrfDetailInfo(TrfDetailReq reqObject);

    BaseResponse getCustomerTrfDetailInfo(TrfDetailReq reqObject);

    BaseResponse<List<TrfInfoRsp>> getTrfInfo(TrfInfoReq reqObject);

    /**
     *
     * @param reqObject
     * @return
     */
    BaseResponse<CheckTrfInfoRsp> checkTrfInfo(CheckTrfInfoReq reqObject);

    BaseResponse updateTrfBoundStatus(TrfBoundStatusReq reqObject);

    /**
     * 绑定Trf
     * @param reqObject
     * @return
     */
    BaseResponse bindTrf(BindTrfReq reqObject);

    /**
     * 解绑Trf
     * @param reqObject
     * @return
     */
    BaseResponse unBindTrf(UnBindTrfReq reqObject);


    BaseResponse getTrfStatus(Integer type);

    /**
     *
     * @param reqObject
     * @return
     */
    BaseResponse sendTrfMainInfo(SendTrfMainInfoReq reqObject);

    /**
     *
     * @param reqObject
     * @return
     */
    BaseResponse<List<TrfTodoDTO>> searchTrfNo(SearchTrfNoReq reqObject);

    BaseResponse<CustomerTrfInfoRsp> queryCustomerTrfInfoByTrfNo(SearchTrfNoReq reqObject);

    BaseResponse<Integer> checkCustomerRule(SearchTrfNoReq reqObject);

    BaseResponse<GetTrfDetailPrintRsp> getTrfDetailPrintInfo(GetTrfDetailPrintReq reqObject);

}
