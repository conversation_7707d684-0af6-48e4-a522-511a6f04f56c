#set($sampleMap={})
#foreach ($reportItem in $reportList)
    #foreach ($reportMatrixItem in $reportItem.reportMatrixList)
        #foreach ($testSampleItem in $testSampleList)
            #if($testSampleItem.testSampleInstanceId==$reportMatrixItem.testSampleInstanceId&&$sampleMap.putIfAbsent($testSampleItem.testSampleInstanceId,$testSampleItem))
            #end
        #end
    #end
#end

{
  "testLineList": [
#foreach ($testLine in $testLineList)
    {
      "testLineInstanceId": "$!{testLine.testLineInstanceId}",
      "testLineId": "$!{testLine.testLineId}",
      "citationId": "$!{testLine.citationId}",
      "citationType": "$!{testLine.citationType}"#if(!$testSampleList),#end
      "sampleList":[
    #foreach ($sampleEntry in $sampleMap.entrySet())
        {
        "testSampleInstanceId": "$!{sampleEntry.value.testSampleInstanceId}",
        "color": "$!{sampleEntry.value.materialAttrList[0].materialColor}",
        "description": "$!{sampleEntry.value.materialAttrList[0].materialDescription}",
        "endUse": "$!{sampleEntry.value.materialAttrList[0].materialEndUse}"
        }
        #if(!$foreach.last),#end
    #end
     ]
    }
    #if(!$foreach.last),#end
#end
  ]
}