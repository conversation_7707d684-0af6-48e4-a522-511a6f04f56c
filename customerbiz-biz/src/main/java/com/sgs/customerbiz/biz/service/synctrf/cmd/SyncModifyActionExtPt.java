package com.sgs.customerbiz.biz.service.synctrf.cmd;

import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.extension.Extension;
import com.sgs.customerbiz.core.util.OptionalUtils;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfInfoPO;
import com.sgs.customerbiz.dfv.enums.ActiveIndicatorEnum;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfReportDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.domain.service.ReportDataService;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.enums.TrfStatusEnum;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.exception.BizException;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */


@Slf4j
@Extension(scenario = "SyncModify")
public class SyncModifyActionExtPt extends AbstractSyncActionExtPt {

    @Autowired
    private ReportDataService reportDataService;

    @Override
    public TrfStatusResult doStatusControl(TrfStatusControlDO trfStatusControlDO) {
        TrfInfoPO trfInfoByTrfNo = trfDomainService.getTrfInfoByTrfNo(trfStatusControlDO.getTrfNo(), trfStatusControlDO.getRefSystemId());
        Assert.notNull(trfInfoByTrfNo, "Trf is not Exist!");
        if (!Objects.equals(TrfStatusEnum.Completed.getStatus(),trfInfoByTrfNo.getStatus())
                && !Objects.equals(TrfStatusEnum.Closed.getStatus(),trfInfoByTrfNo.getStatus())
                && !Objects.equals(TrfStatusEnum.Revise.getStatus(),trfInfoByTrfNo.getStatus())
                && !Objects.equals(TrfStatusEnum.Testing.getStatus(),trfInfoByTrfNo.getStatus())) {
            throw new BizException("Trf Status Must As Completed or Closed!");
        }

        Optional<Integer> changeStatus = OptionalUtils.when(TrfStatusEnum.check(trfInfoByTrfNo.getStatus(), TrfStatusEnum.Completed, TrfStatusEnum.Closed), TrfStatusEnum.Testing)
                .map(TrfStatusEnum::getStatus);

        trfDomainService.updateTrfStatusAndDeliveryFlag(trfInfoByTrfNo.getTrfNo(), trfInfoByTrfNo.getRefSystemId(), changeStatus,trfStatusControlDO.getReportList());
        List<TrfReportDOV2> reportList = trfStatusControlDO.getReportList();
        // 校验trf是否存在
        TrfInfoPO trfInfoPO = trfDomainService.selectByTrfNo(trfStatusControlDO.getRefSystemId(), trfStatusControlDO.getTrfNo());
        Assert.notNull(trfInfoPO, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), StrUtil.format("The trf {} not found", trfStatusControlDO.getTrfNo()));

        reportList.forEach(rpt -> rpt.setTrfId(trfInfoPO.getId()));
//SCI-1299 取消call rd cancel
        TrfStatusResult statusResult = new TrfStatusResult();
        statusResult.setOldTrfStatus(trfInfoByTrfNo.getStatus());
        statusResult.setNewTrfStatus(TrfStatusEnum.Testing.getStatus());
        statusResult.setNewActiveIndicator(ActiveIndicatorEnum.Active.getStatus());
        statusResult.setOldActiveIndicator(ActiveIndicatorEnum.Active.getStatus());

        statusResult.setIsStatus(false);
        return statusResult;
    }

    @Override
    public void syncSingleTrfPostprocess(TrfStatusResult trfStatusResult, TrfFullDTO trfDOParam) {

    }

}
