package com.sgs.customerbiz.biz.convertor;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.core.common.SciRequestContext;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.domain.domainobject.v2.*;
import com.sgs.customerbiz.domain.enums.TrfActionEnum;
import com.sgs.customerbiz.model.trf.dto.*;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.customerbiz.model.trf.dto.resp.TrfSyncResult;
import com.sgs.framework.tool.utils.BeanUtil;
import com.sgs.framework.tool.utils.Func;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public abstract class TrfSyncConvertor {

    public static TrfSyncResult toSyncResult(TrfSyncReq trfSyncReq) {
        TrfSyncHeaderDTO header = trfSyncReq.getHeader();
        TrfSyncResult trfSyncResult = new TrfSyncResult();
        trfSyncResult.setRefSystemId(header.getRefSystemId());
        if (Func.isNotEmpty(header.getTrfList())) {
            List<TrfHeaderDTO> trfList = header.getTrfList().stream().map(t -> {
                TrfHeaderDTO header2 = new TrfHeaderDTO();
                header2.setTrfId(t.getTrfId());
                header2.setTrfNo(t.getTrfNo());
                return header2;
            }).collect(Collectors.toList());

            trfSyncResult.setTrfList(trfList);
        }
        if (Func.isNotEmpty(trfSyncReq.getOrder())) {
            TrfOrderDTO order = trfSyncReq.getOrder();
            TrfOrderDTO trfOrderDTO = new TrfOrderDTO();
            trfOrderDTO.setOrderId(order.getOrderId());
            trfOrderDTO.setOrderNo(order.getOrderNo());
            trfSyncResult.setOrder(trfOrderDTO);
        }
        return trfSyncResult;
    }

//    public static List<TrfDOV2> toTrfDOV2(TrfSyncReq trfSyncReq) {
//        List<TrfDOV2> trfDOV2List = new ArrayList<>();
//        CopyOptions copyOptions = CopyOptions.create().setIgnoreCase(true).ignoreNullValue();
//        TrfSyncHeaderDTO header = trfSyncReq.getHeader();
//        List<TrfHeaderDTO> trfList = header.getTrfList();
//        for (TrfHeaderDTO trfHeaderDTO : trfList) {
//            TrfDOV2 trfDO = new TrfDOV2();
//            //header
//            TrfHeaderDOV2 trfHeaderDOV2 = BeanUtil.copyProperties(trfHeaderDTO, TrfHeaderDOV2.class);
//            trfDO.setHeader(trfHeaderDOV2);
//            //quotation
//            List<TrfQuotationDOV2> trfQuotationDOV2List = toTrfQuotationDOV2List(trfSyncReq);
//            trfDO.setQuotationList(trfQuotationDOV2List);
//            //invoice
//            List<TrfInvoiceDOV2> trfInvoiceDOV2List = toTrfInvoiceDOV2List(trfSyncReq);
//            trfDO.setInvoiceList(trfInvoiceDOV2List);
//
//            //report
//            List<TrfReportDOV2> trfReportDOV2List = toTrfReportDOV2List(trfSyncReq);
//            trfDO.setReportList(trfReportDOV2List);
//
//            //order
//            TrfOrderDOV2 trfOrderDOV2 = toTrfOrderDOV2(trfSyncReq);
//            trfDO.setOrder(trfOrderDOV2);
//
//            TrfOtherDOV2 trfOtherDOV2 = toTrfOtherDOV2(trfSyncReq.getOthers(), trfHeaderDTO.getOthers());
//            trfHeaderDOV2.setOthers(trfOtherDOV2);
//            trfDOV2List.add(trfDO);
//        }
//        return trfDOV2List;
//    }

    private static TrfOtherDOV2 toTrfOtherDOV2(TrfOtherDTO others, TrfOtherDTO others2) {
        if (Objects.isNull(others) && Objects.isNull(others2)) {
            return null;
        }

        TrfOtherDTO target;
        if (others != null) {
            target = others;
        } else {
            target = others2;
        }

        TrfOtherDOV2 trfOtherDOV2 = new TrfOtherDOV2();
        if (target.getPending() != null) {
            TrfPendingDOV2 pendingDOV2 = new TrfPendingDOV2();
            pendingDOV2.setPendingFlag(target.getPending().getPendingFlag());
            //请求端传入的值，直接保存透传 --  PendingType不做标准化定义
            pendingDOV2.setPendingType(target.getPending().getPendingType());
            pendingDOV2.setPendingRemark(target.getPending().getPendingRemark());
            trfOtherDOV2.setPending(pendingDOV2);
        }

        if (target.getCancel() != null) {
            trfOtherDOV2.setCancelType(target.getCancel().getCancelType());
            trfOtherDOV2.setCancelRemark(target.getCancel().getCancelReason());
        }

        if (target.getRemove() != null) {
            trfOtherDOV2.setRemoveType(target.getRemove().getRemoveType());
            trfOtherDOV2.setRemoveRemark(target.getRemove().getRemoveReason());
        }

        return trfOtherDOV2;
    }

    public static List<TrfQuotationDOV2> toTrfQuotationDOV2List(TrfSyncReq trfSyncReq) {
        if (Objects.isNull(trfSyncReq.getQuotationList())) {
            return CollUtil.newArrayList();
        }
        TrfOrderDTO order = trfSyncReq.getOrder();
        List<TrfQuotationDOV2> quotationDOV2List = trfSyncReq.getQuotationList().parallelStream().map(q -> {
            TrfQuotationDOV2 trfQuotationDOV2 = new TrfQuotationDOV2();
//            trfQuotationDOV2.setId(q.getQuotationVersionId());
            trfQuotationDOV2.setSystemId(trfSyncReq.getSystemId());
            trfQuotationDOV2.setOrderId(order.getOrderId());
            trfQuotationDOV2.setOrderNo(order.getOrderNo());
            trfQuotationDOV2.setQuotationNo(q.getQuotationNo());
            return trfQuotationDOV2;
        }).collect(Collectors.toList());
        return quotationDOV2List;
    }

    public static List<TrfInvoiceDOV2> toTrfInvoiceDOV2List(TrfSyncReq trfSyncReq) {
        if (Objects.isNull(trfSyncReq.getInvoiceList())) {
            return CollUtil.newArrayList();
        }
        TrfOrderDTO order = trfSyncReq.getOrder();
        List<TrfInvoiceDOV2> trfInvoiceDOV2List = trfSyncReq.getInvoiceList().parallelStream().map(q -> {
            TrfInvoiceDOV2 trfInvoiceDOV2 = new TrfInvoiceDOV2();
//            trfInvoiceDOV2.setId();
//            trfInvoiceDOV2.setTrfId();
            trfInvoiceDOV2.setSystemId(trfSyncReq.getSystemId());
            trfInvoiceDOV2.setInvoiceNo(q.getInvoiceNo());
            trfInvoiceDOV2.setOrderId(order.getOrderId());
            trfInvoiceDOV2.setOrderNo(order.getOrderNo());
            return trfInvoiceDOV2;
        }).collect(Collectors.toList());
        return trfInvoiceDOV2List;
    }

    public static List<TrfReportDOV2> toTrfReportDOV2List(TrfSyncReq trfSyncReq) {
        if (Objects.isNull(trfSyncReq.getReportList())) {
            return CollUtil.newArrayList();
        }
        TrfOrderDTO order = trfSyncReq.getOrder();
        List<TrfReportDOV2> trfReportDOV2List = trfSyncReq.getReportList().parallelStream().map(q -> {
            TrfReportDOV2 trfReportDOV2 = new TrfReportDOV2();
//            trfReportDOV2.setId();
//            trfReportDOV2.setTrfId();
            trfReportDOV2.setSystemId(Func.isNotEmpty(order.getSystemId()) ? order.getSystemId() : trfSyncReq.getSystemId());
            List<TrfReferenceDTO> trfList = q.getTrfList();
            if (Func.isNotEmpty(trfList)) {
                List<TrfReferenceDO> list = new ArrayList<>();
                trfList.forEach(
                        l -> {
                            TrfReferenceDO trfReferenceDO = new TrfReferenceDO();
                            trfReferenceDO.setTrfNo(l.getTrfNo());
                            list.add(trfReferenceDO);
                        }
                );
                trfReportDOV2.setTrfList(list);
            }
            trfReportDOV2.setOrderId(order.getOrderId());
            trfReportDOV2.setOrderNo(order.getOrderNo());
            trfReportDOV2.setReportId(q.getReportId());
            trfReportDOV2.setReportNo(q.getReportNo());
            trfReportDOV2.setOriginalReportNo(q.getOriginalReportNo());
            trfReportDOV2.setReportStatus(q.getReportStatus());
            trfReportDOV2.setReportDueDate(q.getReportDueDate());
            return trfReportDOV2;
        }).collect(Collectors.toList());
        return trfReportDOV2List;
    }

    public static TrfOrderDOV2 toTrfOrderDOV2(TrfSyncReq trfSyncReq) {
        if (Objects.isNull(trfSyncReq.getOrder())) {
            return null;
        }
        TrfOrderDTO order = trfSyncReq.getOrder();
        TrfOrderDOV2 trfOrderDOV2 = new TrfOrderDOV2();
        trfOrderDOV2.setProductList(order.getProductList());
        trfOrderDOV2.setSampleList(order.getSampleList());
//        trfOrderDOV2.setId();
//        trfOrderDOV2.setTrfId();
        SciRequestContext requestContext = ProductLineContextHolder.retrieveSciRequestContext();
        if (Objects.nonNull(requestContext)) {
            if (Func.isNotEmpty(order.getSystemId())) {
                trfOrderDOV2.setSystemId(order.getSystemId());
            } else {
                trfOrderDOV2.setSystemId(requestContext.getSystemId());
            }
        }
        trfOrderDOV2.setOrderId(order.getOrderId());
        trfOrderDOV2.setOrderNo(order.getOrderNo());
        trfOrderDOV2.setRealOrderNo(order.getRealOrderNo());
        trfOrderDOV2.setEnquiryNo(order.getEnquiryNo());
        trfOrderDOV2.setOrderStatus(order.getOrderStatus());
        trfOrderDOV2.setOrderExpectDueDate(order.getOrderExpectDueDate());
        List<TrfChildOrderDTO> childOrderList = order.getChildOrderList();
        if (Func.isNotEmpty(childOrderList)) {
            List<TrfChildOrderDO> list = new ArrayList<>();
            childOrderList.forEach(
                    l -> {
                        TrfChildOrderDO childOrderDO = new TrfChildOrderDO();
                        BeanUtil.copy(l, childOrderDO);
                        childOrderDO.setOrderNo(l.getOrderNo());
                        childOrderDO.setOrderId(l.getOrderId());
                        list.add(childOrderDO);
                    }
            );
            trfOrderDOV2.setChildOrderList(list);
        }

        List<TrfCustomerDTO> customerList = order.getCustomerList();
        if (Func.isNotEmpty(customerList)) {
            List<TrfCustomerDOV2> list = JSONArray.parseArray(JSONObject.toJSONString(customerList), TrfCustomerDOV2.class);
            trfOrderDOV2.setCustomerList(list);
        }

        // 设置pending相关信息
        if (order.getOthers() != null && order.getOthers().getPending() != null) {
            trfOrderDOV2.setPendingType(order.getOthers().getPending().getPendingType());
            trfOrderDOV2.setPendingRemark(order.getOthers().getPending().getPendingRemark());
        }
//        trfOrderDOV2.setBoundStatus();

        return trfOrderDOV2;
    }

    public static TrfStatusControlDO toUnbindTrfStatusControlDO(String trfNo, Integer refSystemId, TrfOrderDOV2 trfOrderDOV2) {
        TrfStatusControlDO trfStatusControlDO = new TrfStatusControlDO();

        trfStatusControlDO.setRefSystemId(refSystemId);
        trfStatusControlDO.setTrfNo(trfNo);
        trfStatusControlDO.setAction(TrfActionEnum.SYNC_UN_BIND.getCode());

        trfStatusControlDO.setOrder(trfOrderDOV2);
        return trfStatusControlDO;
    }

    public static TrfStatusControlDO toTrfStatusControlDO(TrfHeaderDTO trfHeader, TrfSyncReq trfSyncReq) {
        TrfStatusControlDO trfStatusControlDO = new TrfStatusControlDO();

        trfStatusControlDO.setSyncReq(trfSyncReq);
        trfStatusControlDO.setSyncHeader(trfSyncReq.getHeader());
        trfStatusControlDO.setSystemId(trfSyncReq.getSystemId());
        TrfSyncHeaderDTO header = trfSyncReq.getHeader();
        trfStatusControlDO.setRefSystemId(header.getRefSystemId());
        trfStatusControlDO.setTrfNo(trfHeader.getTrfNo());
        trfStatusControlDO.setAction(trfSyncReq.getAction());

        TrfOrderDOV2 trfOrderDOV2 = toTrfOrderDOV2(trfSyncReq);
        if (Func.isNotEmpty(trfOrderDOV2)) {
            trfOrderDOV2.setOperator(trfSyncReq.getOperator());
            trfOrderDOV2.setOperationTime(trfSyncReq.getOperationTime());
        }
        trfStatusControlDO.setOrder(trfOrderDOV2);

        List<TrfReportDOV2> trfReportList = toTrfReportDOV2List(trfSyncReq);
        trfStatusControlDO.setReportList(trfReportList);

        TrfOtherDOV2 trfOtherDOV2 = toTrfOtherDOV2(trfSyncReq.getOthers(), trfHeader.getOthers());
        trfStatusControlDO.setOthers(trfOtherDOV2);

        if (trfOrderDOV2 != null && trfOtherDOV2 != null && trfOtherDOV2.getPending() != null) {
            trfOrderDOV2.setPendingType(trfOtherDOV2.getPending().getPendingType());
            trfOrderDOV2.setPendingRemark(trfOtherDOV2.getPending().getPendingRemark());
        }

        return trfStatusControlDO;
    }
}
