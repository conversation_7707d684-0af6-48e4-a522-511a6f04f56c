package com.sgs.customerbiz.biz.service.ruleengine.fn.sample;

import com.alibaba.fastjson.JSONObject;
import com.googlecode.aviator.runtime.type.AviatorBoolean;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.sgs.customerbiz.biz.service.ruleengine.EngineContext;
import com.sgs.customerbiz.biz.service.ruleengine.fn.IsNotEmpty;
import com.sgs.customerbiz.biz.service.ruleengine.fn.REQFunction;
import com.sgs.customerbiz.biz.service.ruleengine.fn.base.BaseFunction;
import com.sgs.customerbiz.model.trf.dto.TrfMaterialAttrDTO;
import com.sgs.customerbiz.model.trf.dto.TrfTestSampleDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.framework.model.enums.SampleType;
import com.sgs.framework.tool.utils.Func;

import java.util.*;
import java.util.stream.Collectors;

public class FindSampleListByType extends BaseFunction {
    @Override
    public String getName() {
        return "FindSampleListByType";
    }

    @Override
    public String desc() {
        return "FindSampleListByType";
    }


    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1) {
        // get context
        EngineContext context = getContext(env);
        if (context == null) {
            //do something
        }

        // parse arg1
        String jsonStr = arg1.stringValue(env);
        // fixme 待修改
        TrfSyncReq importReportDataReq = JSONObject.parseObject(jsonStr, TrfSyncReq.class);
        if (Func.isEmpty(importReportDataReq)) {
            return AviatorBoolean.FALSE;
        }
        List<TrfTestSampleDTO> testSampleList = importReportDataReq.getTestSampleList();
        if (Func.isEmpty(testSampleList)) {
            return AviatorBoolean.FALSE;
        }
        testSampleList = testSampleList.stream().filter(l -> Objects.equals(SampleType.Sample.getSampleType(), l.getTestSampleType())).collect(Collectors.toList());
        if (Func.isEmpty(testSampleList)) {
            return AviatorBoolean.FALSE;
        }
        List<TrfMaterialAttrDTO> materialAttrList = new ArrayList<>();
        for (TrfTestSampleDTO l : testSampleList) {
            materialAttrList.addAll(l.getMaterialAttrList());
        }
        if (Func.isEmpty(materialAttrList)) {
            return AviatorBoolean.FALSE;
        }
        materialAttrList = materialAttrList.stream().filter(l -> Func.isBlank(l.getMaterialSku())).collect(Collectors.toList());
        if (Func.isEmpty(materialAttrList) || materialAttrList.size() > 0) {
            return AviatorBoolean.FALSE;
        }
        return AviatorBoolean.TRUE;
    }


    public static void main(String[] args) {
        Map env = new HashMap<>();
        // 通过xpath获取json
        REQFunction reqFunction = new REQFunction();
        AviatorObject object = new AviatorString("$trf.matrix");
        AviatorObject call = reqFunction.call(env, object);

        IsNotEmpty isNotEmpty = new IsNotEmpty();
        AviatorBoolean aviatorBoolean = isNotEmpty.call(env, call);
        if (aviatorBoolean.booleanValue(env)) {

        }

    }
}
