package com.sgs.customerbiz.biz.service.task.impl;

import com.google.common.collect.ImmutableList;
import com.sgs.config.api.dto.req.SendReportScheduled;
import com.sgs.customerbiz.biz.enums.EventPoolStatus;
import lombok.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScheduledSendReportRequest {

    private Integer refSystemId;

    private Long apiId;

    private String productLineId;

    private Date startDateTime;

    private Date endDateTime;

    private SendReportScheduled.SendMode sendMode;

    private int batchMaxSize;

    private List<EventPoolStatus>  eventPoolStatusList = new ArrayList<>();

    private boolean sendWhenEmpty = false;

    public Optional<Date> getStartDateTime() {
        return Optional.ofNullable(startDateTime);
    }

    public List<EventPoolStatus> getEventPoolStatusList() {
        if(null == eventPoolStatusList || eventPoolStatusList.isEmpty()){
            return ImmutableList.of(EventPoolStatus.New);
        }
        return eventPoolStatusList;
    }

    public Optional<Long> getApiId() {
        return Optional.ofNullable(apiId);
    }
}
