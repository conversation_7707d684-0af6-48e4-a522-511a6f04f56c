package com.sgs.customerbiz.biz.service.synctrf.validator;

import com.alibaba.cola.extension.Extension;
import com.sgs.customerbiz.biz.convertor.TrfConvertor;
import com.sgs.customerbiz.biz.service.ruleengine.node.CheckReportExist;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.core.util.FieldUtil;
import com.sgs.customerbiz.domain.service.ReportDataService;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.integration.CustomerClient;
import com.sgs.customerbiz.integration.dto.CustomerAndGroupInfoReq;
import com.sgs.customerbiz.integration.dto.CustomerAndGroupInfoRsp;
import com.sgs.customerbiz.integration.dto.RdReportHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.*;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.customerbiz.model.trf.enums.CustomerUsage;
import com.sgs.customerbiz.model.trf.enums.ReportStatusEnum;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.sgs.customerbiz.core.constants.Constants.SYSTEM_SGSMART;

/**
 * <AUTHOR>
 */
@Slf4j
@Extension(scenario = "SyncCompleted")
public class CompletedSyncActionValidatorExtPt extends NormalSyncActionValidatorExtPt {

    @Autowired
    private ReportDataService reportDataService;

    @Resource
    private ConfigClient configClient;

    @Autowired
    private CustomerClient customerClient;


    @Override
    public void validate(TrfSyncReq syncReq) {
        // 1. 基础校验
        validateBasicFields(syncReq);

        List<TrfHeaderDTO> trfList = syncReq.getHeader().getTrfList();
        for (TrfHeaderDTO trfHeaderDTO : trfList) {
            TrfFullDTO trfDOParam = TrfConvertor.toTrfFullDTO(trfHeaderDTO, syncReq);

            // 2. 校验报告状态
            validateReportStatus(syncReq, trfDOParam);

            // 3. 补充数据
            enrichTrfData(syncReq, trfDOParam);
        }

        // 4. 调用父类校验
        super.validate(syncReq);
    }


    /**
     * 校验报告状态
     */
    private void validateReportStatus(TrfSyncReq syncReq, TrfFullDTO trfDOParam) {
        // 如果是SGSMart系统,校验报告状态
        if (syncReq.getSystemId().equals(SYSTEM_SGSMART)) {
            validateSgsmartReport(trfDOParam);
        } else {
            validateNormalReport(trfDOParam);
        }
    }

    /**
     * 校验SGSMart报告
     */
    private void validateSgsmartReport(TrfFullDTO trfDOParam) {
        List<RdReportHeaderDTO> reportHeaderFromRd = reportDataService.batchExportReportHeader(trfDOParam);
        boolean hasCancelled = reportHeaderFromRd.stream()
                .anyMatch(report -> Objects.equals(report.getReportStatus(), ReportStatusEnum.Cancelled.getCode()));

        if(hasCancelled) {
            List<String> reportNoList = reportHeaderFromRd.stream()
                    .filter(report -> Objects.equals(report.getReportStatus(), ReportStatusEnum.Cancelled.getCode()))
                    .map(RdReportHeaderDTO::getReportNo)
                    .collect(Collectors.toList());

            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(
                    ErrorCategoryEnum.BUSINESS_ERROR,
                    ErrorBizModelEnum.NORMALSYNCVALIDATOR,
                    ErrorFunctionTypeEnum.GETINFO,
                    ErrorTypeEnum.DATANOTFOUND);
            throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(),
                    "Rd Report has cancelled! " + reportNoList);
        }
        reportDataService.saveReportData(trfDOParam);
    }

    /**
     * 校验普通报告
     */
    private void validateNormalReport(TrfFullDTO trfDOParam) {
        CheckReportExist reportExist = new CheckReportExist();
        boolean doEvalFlag = reportExist.doEval(trfDOParam);
        if (!doEvalFlag){
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(
                    ErrorCategoryEnum.BUSINESS_ERROR,
                    ErrorBizModelEnum.NORMALSYNCVALIDATOR,
                    ErrorFunctionTypeEnum.GETINFO,
                    ErrorTypeEnum.DATANOTFOUND);
            throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(),
                    "Report In RD Not Found!");
        }
    }

    /**
     * 补充TRF数据
     */
    private void enrichTrfData(TrfSyncReq syncReq, TrfFullDTO trfDOParam) {
        // 补充报告列表数据
        enrichReportList(syncReq);

        // 补充订单数据
        enrichOrderData(syncReq, trfDOParam);

        enrichSyncTestInfo(syncReq, trfDOParam);

        // 补充客户组代码
        enrichCustomerGroupCode(syncReq);
    }

    private static void enrichSyncTestInfo(TrfSyncReq syncReq, TrfFullDTO trfDOParam) {
        if (Func.isEmpty(syncReq.getTestResultList())) {
            syncReq.setTestResultList(trfDOParam.getTestResultList());
        }
        if (Func.isEmpty(syncReq.getTestLineList())) {
            syncReq.setTestLineList(trfDOParam.getTestLineList());
        }
        if (Func.isEmpty(syncReq.getTestSampleList())) {
            syncReq.setTestSampleList(trfDOParam.getTestSampleList());
        }
    }

    /**
     * 补充报告列表数据
     */
    private void enrichReportList(TrfSyncReq syncReq) {
        List<TrfReportDTO> reportList = syncReq.getReportList();
        if (Func.isNotEmpty(reportList)) {
            reportList.forEach(l -> l.setOrderNo(syncReq.getOrder().getOrderNo()));
        }
    }

    /**
     * 补充订单数据
     */
    private void enrichOrderData(TrfSyncReq syncReq, TrfFullDTO trfDOParam) {
        if (Func.isEmpty(syncReq.getOrder())) {
            syncReq.setOrder(trfDOParam.getOrder());
            return;
        }

        TrfOrderDTO order = syncReq.getOrder();
        TrfOrderDTO orderFromRD = trfDOParam.getOrder();

        // 补充客户列表
        if (Func.isEmpty(order.getCustomerList())) {
            order.setCustomerList(orderFromRD.getCustomerList());
        }

        // 补充产品列表
        if (Func.isEmpty(order.getProductList())) {
            order.setProductList(orderFromRD.getProductList());
        }

        // 补充样品列表
        if (Func.isEmpty(order.getSampleList())) {
            order.setSampleList(orderFromRD.getSampleList());
        }

        // 补充其他订单数据
        if (order.getOrderNo().equals(orderFromRD.getOrderNo())) {
            FieldUtil.supplementData(order, orderFromRD);
        }

        enrichReportData(syncReq, trfDOParam);
    }

    /**
     * 补充报告数据
     */
    private void enrichReportData(TrfSyncReq syncReq, TrfFullDTO trfDOParam) {
        List<TrfReportDTO> trfDOParamReportList = trfDOParam.getReportList();
        if (Func.isNotEmpty(trfDOParamReportList)) {
            List<TrfReportDTO> list = new ArrayList<>();
            Map<String, TrfReportDTO> reportMap = new HashMap<>();
            trfDOParamReportList.forEach(l -> reportMap.put(l.getReportNo(), l));

            for (TrfReportDTO reportDTO : syncReq.getReportList()) {
                TrfReportDTO trfReportDTO = reportMap.get(reportDTO.getReportNo());
                if (Func.isNotEmpty(trfReportDTO)) {
                    FieldUtil.supplementData(reportDTO, trfReportDTO);
                    list.add(reportDTO);
                } else {
                    list.add(reportDTO);
                }
            }
            syncReq.setReportList(list);
        }
    }

    /**
     * 补充客户组代码
     */
    private void enrichCustomerGroupCode(TrfSyncReq syncReq) {
        TrfOrderDTO order = syncReq.getOrder();
        if (Func.isNotEmpty(order) && Func.isNotEmpty(order.getCustomerList())) {
            List<TrfCustomerDTO> customerList = order.getCustomerList();
            customerList.forEach(l -> {
                if (Objects.equals(CustomerUsage.Buyer.getUsage(), l.getCustomerUsage())
                        && Func.isBlank(l.getCustomerGroupCode())
                        && Func.isNotEmpty(l.getBossNo())) {
                    CustomerAndGroupInfoReq req = new CustomerAndGroupInfoReq();
                    req.setBuCode(syncReq.getProductLineCode());
                    req.setCustomerNumber(l.getBossNo().toString());
                    CustomerAndGroupInfoRsp customerAndGroupInfo = customerClient.getCustomerAndGroupInfo(req);
                    if (Func.isNotEmpty(customerAndGroupInfo) && Func.isNotEmpty(customerAndGroupInfo.getRows())) {
                        l.setCustomerGroupCode(customerAndGroupInfo.getRows().get(0).getCustomerGroupCode());
                    }
                }
            });
        }
    }

    /**
     * 校验基础字段
     */
    private void validateBasicFields(TrfSyncReq syncReq) {
        List<TrfHeaderDTO> trfList = syncReq.getHeader().getTrfList();
        if(Func.isEmpty(trfList)) {
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(
                    ErrorCategoryEnum.BUSINESS_ERROR,
                    ErrorBizModelEnum.NORMALSYNCVALIDATOR,
                    ErrorFunctionTypeEnum.SYNC,
                    ErrorTypeEnum.REQUESTNULL);
            throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(),
                    "TrfList cannot be empty! " + trfList);
        }
    }
}

