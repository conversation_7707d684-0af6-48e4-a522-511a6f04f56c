package com.sgs.customerbiz.biz.service.task.impl.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.sgs.customerbiz.biz.enums.TaskResultEnum;
import com.sgs.customerbiz.biz.event.eventhandler.customer.shein.config.MandatoryFieldConfig;
import com.sgs.customerbiz.biz.event.eventhandler.customer.shein.dto.SheInBaseInfoReq;
import com.sgs.customerbiz.biz.service.task.impl.handler.model.message.ILayerMessageTaskParameters;
import com.sgs.customerbiz.biz.service.task.impl.handler.model.message.base.RequestApi;
import com.sgs.customerbiz.core.common.KafkaProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

import static com.sgs.customerbiz.biz.service.task.impl.handler.ILayerMessageAdapterTaskHandler.HANDLER_NAME;

/**
 * @author: shawn.yang
 * @create: 2023-05-09 13:52
 */
@Slf4j
@Component(HANDLER_NAME)
public class ILayerMessageAdapterTaskHandler extends AbstractTaskHandler {
    public static final String HANDLER_NAME= "task.ilayerMessageAdapterTaskHandler";
    @Autowired
    private KafkaProducer kafkaProducer;

    @Override
    protected TaskExecResult execute(String parameters) {
        ILayerMessageTaskParameters iLayerMessage = JSON.parseObject(parameters, ILayerMessageTaskParameters.class);
        RequestApi apiInfo = iLayerMessage.getApiInfo();

        SheInBaseInfoReq rawMessage = iLayerMessage.getRawMessage();
        String trfNo = rawMessage.getObjectNumber();

        PropertyFilter filter = (object, name, value) -> {
            if (Arrays.asList("sign","token","requestId").contains(name)) {
                return false;
            }
            List<String> mandatoryFieldList = MandatoryFieldConfig.getMandatoryFieldList(rawMessage.getRefSystemId(), rawMessage.getDataStatus());
            if (CollectionUtils.isNotEmpty(mandatoryFieldList)) {
                if (mandatoryFieldList.contains(name)) {
                    return true;
                }
            }
            return value != null;
        };
        kafkaProducer.doSendExternal(apiInfo.getRequestUrl(), trfNo , rawMessage,
            (obj)-> JSON.toJSONString(obj, filter, SerializerFeature.WriteMapNullValue));

        log.info("send message to ilayer success ,trfNo:{} ,topic:[{}]",trfNo,apiInfo.getRequestUrl());
        return new TaskExecResult(TaskResultEnum.PROCESSING, "async call");
    }

}
