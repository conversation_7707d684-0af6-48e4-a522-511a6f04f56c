package com.sgs.customerbiz.biz.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.model.trf.dto.TrfDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.customerbiz.model.trf.enums.CustomerUsage;
import org.jetbrains.annotations.NotNull;

import java.util.Objects;
import java.util.Optional;

public class CustomerContentUtils {

    public static final String SGS_PLATFORM_REQUEST_CONTEXT = "__sgs_platform_request_context";

    @NotNull
    public static String mixCustomerTrf(TrfImportReq importReq, TrfDTO sgsTRF, String customerTrfOriginal) {
        JSONObject customerTrfJSONObject = JSON.parseObject(customerTrfOriginal);
        if(customerTrfJSONObject.containsKey(SGS_PLATFORM_REQUEST_CONTEXT)) {
            return customerTrfOriginal;
        }
        JSONObject ctxOfRequest = new JSONObject();
        Optional.ofNullable(sgsTRF.getCustomerList())
                .flatMap(cl -> cl.stream().filter(c -> Objects.equals(c.getCustomerUsage(), CustomerUsage.Applicant.getUsage())).findFirst())
                .ifPresent(applicant -> ctxOfRequest.put("applicant", applicant));
        ctxOfRequest.put("systemId",importReq.getSystemId());
        ctxOfRequest.put("labCode", importReq.getLabCode());
        ctxOfRequest.put("buCode", importReq.getBuCode());
        ctxOfRequest.put("trfTemplateId", importReq.getTrfTemplateId());
        ctxOfRequest.put("trfTemplateName", importReq.getTrfTemplateName());
        ctxOfRequest.put("trfTemplateType", importReq.getTrfTemplateType());
        ctxOfRequest.put("formId", importReq.getFormId());
        ctxOfRequest.put("gridId", importReq.getGridId());
        ctxOfRequest.put("labContactName", importReq.getLabContactName());
        ctxOfRequest.put("labContact", importReq.getLabContact());
        customerTrfJSONObject.put(SGS_PLATFORM_REQUEST_CONTEXT, ctxOfRequest);
        return customerTrfJSONObject.toJSONString();
    }


}
