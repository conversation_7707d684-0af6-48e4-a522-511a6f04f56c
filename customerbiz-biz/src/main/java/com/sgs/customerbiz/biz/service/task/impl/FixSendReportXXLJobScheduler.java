package com.sgs.customerbiz.biz.service.task.impl;

import com.alibaba.fastjson.JSON;
import com.sgs.config.api.dto.req.DateRange;
import com.sgs.config.api.dto.req.SendReportScheduled;
import com.sgs.customerbiz.biz.utils.JobLogUtil;
import com.sgs.customerbiz.domain.constants.TrfConstants;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Component
@RequiredArgsConstructor
@Slf4j
public class FixSendReportXXLJobScheduler {

    private final ScheduledSendReportService scheduledSendReportService;

    @XxlJob("fixSendReportHandler")
    public void sendReportHandler() {
        JobLogUtil.info(log, "start fix send report handler on " + JobLogUtil.now());
        ScheduledSendReportRequest request = JSON.parseObject(XxlJobHelper.getJobParam(), ScheduledSendReportRequest.class);
        JobLogUtil.info(log,"param from job : {}", JSON.toJSONString(request));
        try {
            scheduledSendReportService.sendReport(request);
        } catch (Throwable t) {
            JobLogUtil.error(log, t, "fix send report handler error");
            XxlJobHelper.handleFail(t.getMessage());
        }
        JobLogUtil.info(log,"success fix send report handler;" + request.toString() + " on " + JobLogUtil.now());
    }

}
