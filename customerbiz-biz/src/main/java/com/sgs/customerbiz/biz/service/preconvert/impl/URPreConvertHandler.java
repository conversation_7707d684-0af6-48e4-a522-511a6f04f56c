package com.sgs.customerbiz.biz.service.preconvert.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.ImmutableList;
import com.sgs.config.api.dto.EventSubscribeDTO;
import com.sgs.customerbiz.biz.convert.impl.fn.DffCodeListFn;
import com.sgs.customerbiz.biz.convert.impl.fn.GridValueDyncByDynamicsCodeFn;
import com.sgs.customerbiz.biz.dto.ur.URItemVO;
import com.sgs.customerbiz.biz.dto.ur.URSampleVO;
import com.sgs.customerbiz.biz.service.datacollector.CollectedData;
import com.sgs.customerbiz.biz.service.datacollector.StandardDataStructureEnum;
import com.sgs.customerbiz.biz.service.datacollector.impl.ExtraDataCollector;
import com.sgs.customerbiz.biz.service.preconvert.DataPreConvertHandler;
import com.sgs.customerbiz.biz.utils.CollectDataUtil;
import com.sgs.customerbiz.core.util.AF;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfInfoExtMapper;
import com.sgs.customerbiz.domain.domainevent.ObjectEvent;
import com.sgs.customerbiz.facade.model.trf.rsp.CustomerTrfInfoRsp;
import com.sgs.customerbiz.infrastructure.api.IdService;
import com.sgs.customerbiz.integration.dto.DffMappingConfigRsp;
import com.sgs.customerbiz.model.trf.dto.*;
import com.sgs.customerbiz.model.trf.dto.interfaces.TestMappingKey;
import com.sgs.customerbiz.model.trf.dto.resp.TestLineMappingInfoV2DTO;
import com.sgs.customerbiz.model.trf.enums.ConclusionTypeEnum;
import com.sgs.customerbiz.model.trf.enums.CustomerUsage;
import com.sgs.customerbiz.model.tuple.Pair;
import com.sgs.customerbiz.validation.service.TestMappingQueryService;
import com.sgs.extsystem.facade.model.customer.req.MappingTestLineReq;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.model.enums.SampleType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ObjectUtil;
import com.sgs.testdatabiz.facade.model.dto.rd.report.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
public class URPreConvertHandler extends AbstractDataPreConvertHandler implements DataPreConvertHandler {

    private final TestMappingQueryService testMappingQueryService;

    private final TrfInfoExtMapper trfInfoExtMapper;

    private final IdService idService;

    private final GridValueDyncByDynamicsCodeFn gridValueGetter;

    private static final List<Integer> ACCEPT_SUBSCRIBER_SYSTEM_ID_LIST = new ArrayList<>();

    static {
        ACCEPT_SUBSCRIBER_SYSTEM_ID_LIST.add(RefSystemIdEnum.UrbanRevivo.getRefSystemId());
    }

    public URPreConvertHandler(TestMappingQueryService testMappingQueryService, TrfInfoExtMapper trfInfoExtMapper, IdService idService, GridValueDyncByDynamicsCodeFn gridValueGetter) {
        this.testMappingQueryService = testMappingQueryService;
        this.trfInfoExtMapper = trfInfoExtMapper;
        this.idService = idService;
        this.gridValueGetter = gridValueGetter;
    }

    @Override
    public void preHandler(EventSubscribeDTO subscribe, ObjectEvent trfEvent, CollectedData collectedData) {
        log.info("URPreConvertHandler:Start {}", trfEvent.getTrfNo());

        TrfFullDTO trfFullDTO = JSON.parseObject(collectedData.toJSONString(), TrfFullDTO.class);

        List<TrfRichMatrixV2DTO> allReportMatrixList = CollectDataUtil.splitByReport(trfFullDTO).stream()
                .map(CollectDataUtil::buildMatrixListV2)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        List<TestLineMappingInfoV2DTO> mappingList = queryTlMappings(trfEvent, trfFullDTO, trfFullDTO.getTestLineList());
        CustomerTrfInfoRsp trfInfo = trfInfoExtMapper.getTrfInfo(trfEvent.getRefSystemId(), trfEvent.getTrfNo());
        JSONObject customerTrfInfo = JSON.parseObject(trfInfo.getContent());
        List<URSampleVO> urSampleVOS = Optional.ofNullable(customerTrfInfo)
                .filter(c -> c.containsKey("samples"))
                .map(c -> c.getJSONArray("samples"))
                .map(samples -> JSON.parseArray(samples.toJSONString(), URSampleVO.class))
                .orElse(Collections.emptyList());
        Map<String, URSampleVO> uniqueSamples = urSampleVOS.stream()
                .collect(Collectors.groupingBy(
                        URSampleVO::getItemID,
                        Collectors.collectingAndThen(Collectors.toList(), URSampleVO::merged)));
        Set<String> itemCodes = uniqueSamples.values().stream()
                .flatMap(sample -> sample.getItems().stream())
                .filter(Objects::nonNull)
                .map(URItemVO::getItemCode)
                .collect(Collectors.toSet());
        Map<String, List<TestLineMappingInfoV2DTO>> mappingMap = mappingList.stream()
                .filter(mapping -> StringUtils.isNotBlank(mapping.getItemCode()))
                .filter(mapping -> itemCodes.contains(mapping.getItemCode()))
                .collect(Collectors.groupingBy(TestMappingKey::asKey0));
        Object dffCodeMapping = collectedData.get(DffCodeListFn.CUSTOMERDFF_ATTR);
        List<TrfRichMatrixV2DTO> finalAllMatrixList = zip(allReportMatrixList, mappingMap, uniqueSamples, dffCodeMapping);
        collectedData.put("_allReportMatrixList", finalAllMatrixList);

        log.info("URPreConvertHandler:End {}", trfEvent.getTrfNo());
        super.preHandler(subscribe, trfEvent, collectedData);
    }

    public List<TrfRichMatrixV2DTO> zip(List<TrfRichMatrixV2DTO> allReportMatrixList, Map<String, List<TestLineMappingInfoV2DTO>> mappingMap, Map<String, URSampleVO> urSamplesByItemId, Object dffCodeMapping) {
        return allReportMatrixList.stream()
                .filter(matrix -> CollectionUtils.isNotEmpty(matrix.getSampleList()))
                .flatMap(matrix -> {
                    Object invokeResult = gridValueGetter.invoke(new Object[]{"RefCode1", dffCodeMapping, JSON.toJSONString(matrix.getSampleList())});
                    List<String> itemIds = (List<String>) invokeResult;
                    Map<String, URItemVO> itemMap = itemIds.stream()
                            .filter(urSamplesByItemId::containsKey)
                            .map(urSamplesByItemId::get)
                            .map(URSampleVO::getItems)
                            .flatMap(Collection::stream)
                            .collect(Collectors.toMap(URItemVO::getItemCode, Function.identity(), (v1, v2) -> v1));
                    List<Pair<TestLineMappingInfoV2DTO, URItemVO>> withKey = mappingMap.getOrDefault(matrix.getTestLine().asKey(), Collections.emptyList()).stream()
                            .filter(mapping -> itemMap.containsKey(mapping.getItemCode()))
                            .map(mapping -> Pair.of(mapping, itemMap.get(mapping.getItemCode())))
                            .collect(Collectors.toList());
                    List<Pair<TestLineMappingInfoV2DTO, URItemVO>> withKey1 = mappingMap.getOrDefault(matrix.getTestLine().asKey1(), Collections.emptyList()).stream()
                            .filter(mapping -> itemMap.containsKey(mapping.getItemCode()))
                            .map(mapping -> Pair.of(mapping, itemMap.get(mapping.getItemCode())))
                            .collect(Collectors.toList());
                    if(withKey.isEmpty() && withKey1.isEmpty()) {
                        TrfRichMatrixV2DTO copiedMatrix = copyWithJson(matrix, TrfRichMatrixV2DTO.class);
                        URItemVO mockItem = new URItemVO();
                        mockItem.setItemCode("");
                        mockItem.setItemName("");
                        mockItem.setProjectId(idService.nextId());
                        copiedMatrix.setCustomerTestLine(mockItem);
                        return ImmutableList.of(copiedMatrix).stream();
                    }
                    return Stream.concat(withKey.stream(), withKey1.stream())
                            .map(pair -> {
                                TestLineMappingInfoV2DTO copiedMapping = copyWithJson(pair.getFirst(), TestLineMappingInfoV2DTO.class);
                                URItemVO copiedItemVO = copyWithJson(pair.getSecond(), URItemVO.class);
                                TrfRichMatrixV2DTO copiedMatrix = copyWithJson(matrix, TrfRichMatrixV2DTO.class);
                                copiedMatrix.setMapping(copiedMapping);
                                copiedMatrix.setCustomerTestLine(copiedItemVO);
                                return copiedMatrix;
                            });

                })
                .collect(Collectors.toList());
    }

    public static <T> T copyWithJson(T obj, Class<? extends T> clazz) {
        return JSON.parseObject(JSON.toJSONString(obj, SerializerFeature.DisableCircularReferenceDetect), clazz);
    }


    @NotNull
    private List<TestLineMappingInfoV2DTO> queryTlMappings(ObjectEvent trfEvent, TrfFullDTO trfFullDTO, List<TrfTestLineDTO> testLineDTOList) {
        Optional<String> customerGroupCode = Optional.ofNullable(trfFullDTO)
                .map(TrfFullDTO::getOrderList)
                .filter(CollectionUtils::isNotEmpty)
                .map(arr -> arr.get(0))
                .map(TrfOrderDTO::getCustomerList)
                .flatMap(cs -> cs.stream().filter(c -> Objects.equals(c.getCustomerUsage(), CustomerUsage.Buyer.getUsage())).findFirst())
                .map(TrfCustomerDTO::getCustomerGroupCode)
                .filter(StringUtils::isNotBlank);
        Optional<String> productLineCode = Optional.ofNullable(trfEvent.getProductLineCode()).filter(StringUtils::isNotBlank);
        Optional<List<TrfTestLineDTO>> trfTestLineDTOS = Optional.of(testLineDTOList).filter(CollectionUtils::isNotEmpty);
        return AF.ap3(this::queryTlMappings, trfTestLineDTOS, customerGroupCode, productLineCode).orElse(Collections.emptyList());
    }

    private Optional<List<TestLineMappingInfoV2DTO>> queryTlMappings(List<TrfTestLineDTO> testLineList, String customerGroupCode, String productLineCode) {
        List<MappingTestLineReq> testLineReqList = testLineList.stream().flatMap(this::toRequest).collect(Collectors.toList());
        return Optional.ofNullable(testMappingQueryService.queryTestLineMappings0(testLineReqList, customerGroupCode, productLineCode));
    }

    public Stream<MappingTestLineReq> toRequest(TrfTestLineDTO testLine) {
        MappingTestLineReq testLineReq = new MappingTestLineReq();
        testLineReq.setPpNo(testLine.getPpNo());
        testLineReq.setTestLineId(testLine.getTestLineId());
        testLineReq.setCitationId(testLine.getCitationId());
        testLineReq.setCitationType(testLine.getCitationType());
        MappingTestLineReq testLineReqWithoutPP = new MappingTestLineReq();
        testLineReqWithoutPP.setTestLineId(testLine.getTestLineId());
        testLineReqWithoutPP.setCitationId(testLine.getCitationId());
        testLineReqWithoutPP.setCitationType(testLine.getCitationType());
        return ImmutableList.of(testLineReq, testLineReqWithoutPP).stream();
    }

    /**
     * Fail(10, "Fail", "Fail"),
     * DataOnly(20, "Data Only", "Pending"),
     * Inconclusive(30, "Inconclusive", "Pending"),
     * Pass(40, "Pass", "Pass"),
     * Exempt(50, "Exempt", "Pass"),
     * NA(60, "NA", "Pass");
     *
     * @param code
     * @return
     */
    private static int intCode(String code) {
        switch (code) {
            case "Fail":
            case "10":
                return 10;
            case "Data Only":
            case "20":
                return 20;
            case "Inconclusive":
            case "30":
                return 30;
            case "Pass":
            case "40":
                return 40;
            case "Exempt":
            case "50":
                return 50;
            case "NA":
            case "60":
                return 60;
            default:
                return 100;
        }
    }

    /**
     * Fail(10, "Fail", "Fail"),
     * DataOnly(20, "Data Only", "Pending"),
     * Inconclusive(30, "Inconclusive", "Pending"),
     * Pass(40, "Pass", "Pass"),
     * Exempt(50, "Exempt", "Pass"),
     * NA(60, "NA", "Pass");
     *
     * @param code
     * @return
     */
    private static String strCode(int code) {
        switch (code) {
            case 10:
                return "Fail";
            case 20:
                return "Data Only";
            case 30:
                return "Inconclusive";
            case 40:
                return "Pass";
            case 50:
                return "Exempt";
            case 60:
                return "NA";
            default:
                return "Unknow";
        }
    }


    @Override
    public List<Integer> acceptSubscriber() {
        return ACCEPT_SUBSCRIBER_SYSTEM_ID_LIST;
    }
}
