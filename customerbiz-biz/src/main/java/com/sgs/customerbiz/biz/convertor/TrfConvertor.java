package com.sgs.customerbiz.biz.convertor;

import com.sgs.customerbiz.model.trf.dto.TrfApproveQuotationDTO;

import com.alibaba.fastjson.JSONArray;
import com.sgs.customerbiz.model.trf.dto.TrfOrderDTO;
import com.sgs.customerbiz.model.trf.dto.TrfReportDTO;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.TrfQuotationDTO;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sgs.customerbiz.biz.constants.I18nErrorCodeConstants;
import com.sgs.customerbiz.core.util.SpringUtil;
import com.sgs.customerbiz.domain.domainobject.TrfDO;
import com.sgs.customerbiz.domain.domainobject.TrfOrderRelationshipDO;
import com.sgs.customerbiz.domain.domainobject.v2.*;
import com.sgs.customerbiz.facade.model.file.FileInfo;
import com.sgs.customerbiz.integration.FileClient;
import com.sgs.customerbiz.integration.FrameWorkClient;
import com.sgs.customerbiz.model.trf.dto.*;
import com.sgs.customerbiz.model.trf.dto.req.*;
import com.sgs.customerbiz.model.trf.enums.PendingFlagEnum;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.preorder.facade.model.info.LabInfo;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;

import static com.sgs.customerbiz.core.constants.Constants.URL_PATTERN;

/**
 * <AUTHOR>
 */
public abstract class TrfConvertor {

    public static TrfDTO toTrfDTO(OrderToTrfReq toTrfReq) {
        CopyOptions copyOptions = CopyOptions.create().setIgnoreCase(true).ignoreNullValue();

        TrfDTO trfDTO = new TrfDTO();
        BeanUtil.copyProperties(toTrfReq, trfDTO, copyOptions);
        TrfConvertor.assembleCommonHeader(toTrfReq, trfDTO.getHeader());

        TrfServiceRequirementDTO serviceRequirementDTO = toTrfReq.getServiceRequirement();
        if (Func.isNotEmpty(serviceRequirementDTO)) {
//            TrfHeaderDTO header = trfDTO.getHeader();
//            if (Func.isEmpty(header)) {
//                header = new TrfHeaderDTO();
//            }
//            header.setServiceRequirement(serviceRequirementDTO);
            trfDTO.setServiceRequirement(serviceRequirementDTO);
        }

        if (Func.isNotEmpty(trfDTO.getOrder())) {
            TrfOrderDTO order = trfDTO.getOrder();
            order.setSystemId(toTrfReq.getSystemId());
        }
        return trfDTO;
    }

    private static void assembleCommonHeader(SciBaseRequest request, TrfHeaderDTO header) {
        if (Func.isNotEmpty(header)) {
            header.setSystemId(request.getSystemId());
            TrfLabDTO trfLabDTO = header.getLab();
            if (Func.isEmpty(header.getLab())) {
                trfLabDTO = new TrfLabDTO();
            }
            trfLabDTO.setLabCode(request.getLabCode());
            trfLabDTO.setBuId(request.getBuId());
            trfLabDTO.setBuCode(request.getBuCode());
            header.setLab(trfLabDTO);
        }
    }

    public static TrfDTO toTrfDTO(TrfSyncReq syncReq) {
        CopyOptions copyOptions = CopyOptions.create().setIgnoreCase(true).ignoreNullValue();

        TrfDTO trfDTO = BeanUtil.copyProperties(syncReq.getHeader(), TrfDTO.class);

        BeanUtil.copyProperties(syncReq, trfDTO, copyOptions);

        TrfConvertor.assembleCommonHeader(syncReq, trfDTO.getHeader());

        return trfDTO;
    }

    public static TrfDO toTrfDO(TrfSyncReq syncReq) {
        CopyOptions copyOptions = CopyOptions.create().setIgnoreCase(true).ignoreNullValue();

        TrfDO trfDO = BeanUtil.copyProperties(syncReq.getHeader(), TrfDO.class);
        if (Func.isNotEmpty(trfDO.getOrderList())) {
            for (TrfOrderRelationshipDO trfOrderRelationshipDO : trfDO.getOrderList()) {
                trfOrderRelationshipDO.setSystemId(trfDO.getSystemId());
            }
        }
        BeanUtil.copyProperties(syncReq, trfDO, copyOptions);

        return trfDO;
    }

    public static TrfDO toTrfDTO(TrfDTO trfDTO) {
        CopyOptions copyOptions = CopyOptions.create().setIgnoreCase(true).ignoreNullValue();

        TrfDO trfDO = BeanUtil.copyProperties(trfDTO.getHeader(), TrfDO.class);

        TrfOtherDTO others = trfDTO.getHeader().getOthers();
        if (Func.isNotEmpty(others)) {
            trfDO.setTrfRemark(others.getTrfRemark());
        }
        BeanUtil.copyProperties(trfDTO, trfDO, copyOptions);
        if (Func.isNotEmpty(trfDO.getOrderList())) {
            for (TrfOrderRelationshipDO trfOrderRelationshipDO : trfDO.getOrderList()) {
                trfOrderRelationshipDO.setSystemId(trfDO.getSystemId());
            }
        }

        return trfDO;
    }

    public static TrfDOV2 toTrfDOV2(TrfDTO trfDTO) {
        CopyOptions copyOptions = CopyOptions.create().setIgnoreCase(true).ignoreNullValue();

        TrfDOV2 trfDO = new TrfDOV2();
        BeanUtil.copyProperties(trfDTO.getHeader(), trfDO, copyOptions);
        BeanUtil.copyProperties(trfDTO, trfDO, copyOptions);
        List<TrfSampleDffDTO> sampleList = trfDTO.getSampleList();
        trfDO.setSampleList(toProductDOV2(sampleList));
        List<TrfCustomerDTO> customerList = trfDTO.getCustomerList();
        trfDO.setCustomerList(toCustomerDOV2(customerList));
        TrfServiceRequirementDTO serviceRequirement = trfDTO.getServiceRequirement();
        TrfHeaderDOV2 trfHeaderDOV2 = new TrfHeaderDOV2();
        BeanUtil.copyProperties(trfDTO.getHeader(), trfHeaderDOV2);
        trfDO.setHeader(trfHeaderDOV2);
        if (Func.isNotEmpty(serviceRequirement)) {
            TrfHeaderDOV2 header = trfDO.getHeader();
            if (Func.isEmpty(header)) {
                header = new TrfHeaderDOV2();
            }
            BeanUtil.copyProperties(trfDTO.getHeader(), header);
            trfDO.setHeader(header);
            TrfServiceRequirementDOV2 trfServiceRequirementDOV2 = JSONObject.parseObject(JSONObject.toJSONString(serviceRequirement), TrfServiceRequirementDOV2.class);
            if (Func.isNotEmpty(trfServiceRequirementDOV2)) {
                header.setServiceRequirement(trfServiceRequirementDOV2);
            }
        }
        return trfDO;

    }

    public static List<TrfCustomerDOV2> toCustomerDOV2(List<TrfCustomerDTO> customerList) {
        if (Func.isNotEmpty(customerList)) {
            List<TrfCustomerDOV2> list = Lists.newArrayList();
            customerList.forEach(
                    customer -> {
                        TrfCustomerDOV2 customerDOV2 = new TrfCustomerDOV2();
                        customerDOV2.setCustomerInstanceId(customer.getCustomerInstanceId());
                        customerDOV2.setCustomerId(customer.getCustomerId());
                        customerDOV2.setCustomerUsage(customer.getCustomerUsage());
                        // TODO 此处需确认数据类型，Long转Integer有问题
                        customerDOV2.setBossNo(Func.isNotEmpty(customer.getBossNo()) ? Integer.parseInt(String.valueOf(customer.getBossNo())) : null);
                        customerDOV2.setCustomerGroupCode(customer.getCustomerGroupCode());
                        customerDOV2.setCustomerName(customer.getCustomerName());
                        customerDOV2.setCustomerAddress(customer.getCustomerAddress());
                        customerDOV2.setPaymentTerm(customer.getPaymentTerm());
                        customerDOV2.setBlackFlag(customer.getBlackFlag());

                        // customerContactList
                        List<TrfCustomerContactDTO> customerContactList = customer.getCustomerContactList();
                        if (Func.isNotEmpty(customerContactList)) {
                            List<TrfCustomerContactDOV2> trfCustomerContactDOV2s = Lists.newArrayList();
                            customerContactList.forEach(
                                    customerContact -> {
                                        TrfCustomerContactDOV2 customerContactDO = new TrfCustomerContactDOV2();
                                        customerContactDO.setContactAddressId(customerContact.getContactAddressId());
                                        customerContactDO.setBossContactId(customerContact.getBossContactId());
                                        customerContactDO.setBossSiteUseId(customerContact.getBossSiteUseId());
                                        customerContactDO.setContactUsage(customerContact.getContactUsage());
                                        customerContactDO.setContactRegionAccount(customerContact.getContactRegionAccount());
                                        customerContactDO.setContactName(customerContact.getContactName());
                                        customerContactDO.setContactEmail(customerContact.getContactEmail());
                                        customerContactDO.setContactPhone(customerContact.getContactMobile());
                                        customerContactDO.setContactTelephone(customerContact.getContactTelephone());
                                        customerContactDO.setContactFax(customerContact.getContactFax());
                                        customerContactDO.setResponsibleTeamCode(customerContact.getResponsibleTeamCode());
                                        trfCustomerContactDOV2s.add(customerContactDO);
                                    }
                            );
                            customerDOV2.setCustomerContactList(trfCustomerContactDOV2s);
                        }

                        // languageList
                        List<TrfCustomerLangDTO> languageList = customer.getLanguageList();
                        if (Func.isNotEmpty(languageList)) {
                            List<TrfCustomerLangDOV2> langDOV2s = Lists.newArrayList();
                            languageList.forEach(
                                    lang -> {
                                        TrfCustomerLangDOV2 langDOV2 = new TrfCustomerLangDOV2();
                                        langDOV2.setLanguageId(lang.getLanguageId());
                                        langDOV2.setCustomerName(lang.getCustomerName());
                                        langDOV2.setCustomerAddress(lang.getCustomerAddress());
                                        langDOV2s.add(langDOV2);
                                    }
                            );
                            customerDOV2.setLanguageList(langDOV2s);
                        }
                        list.add(customerDOV2);
                    }
            );
            return list;
        }
        return null;
    }

    public static List<TrfProductDOV2> toProductDOV2(List<TrfSampleDffDTO> sampleList) {
        if (Func.isNotEmpty(sampleList)) {
            List<TrfProductDOV2> list = new ArrayList<>();
            sampleList.forEach(
                    sample -> {
                        TrfProductDOV2 trfProductDOV2 = new TrfProductDOV2();
                        trfProductDOV2.setTemplateId(sample.getTemplateId());
                        trfProductDOV2.setProductItemNo(sample.getProductItemNo());
                        List<TrfSampleAttrDTO> sampleAttrList = sample.getSampleAttrList();
                        if (Func.isNotEmpty(sampleAttrList)) {
                            List<TrfProductAttrDOV2> productAttrDOV2List = new ArrayList<>();
                            sampleAttrList.forEach(
                                    sampleAttr -> {
                                        TrfProductAttrDOV2 productAttrDOV2 = new TrfProductAttrDOV2();
                                        productAttrDOV2.setProductInstanceId(sampleAttr.getSampleInstanceId());
                                        productAttrDOV2.setLanguageId(sampleAttr.getLanguageId());
                                        productAttrDOV2.setAttrSeq(sampleAttr.getAttrSeq());
                                        productAttrDOV2.setLabelCode(sampleAttr.getLabelCode());
                                        productAttrDOV2.setLabelName(sampleAttr.getLabelName());
                                        productAttrDOV2.setLabelValue(sampleAttr.getLabelValue());
                                        productAttrDOV2.setFieldCode(sampleAttr.getFieldCode());
                                        productAttrDOV2.setDataType(sampleAttr.getDataType());
                                        productAttrDOV2.setCustomerLabel(sampleAttr.getCustomerLabel());
                                        List<TrfProductAttrLangDTO> languageList = sampleAttr.getLanguageList();
                                        if (Func.isNotEmpty(languageList)) {
                                            List<TrfProductAttrLangDOV2> langList = new ArrayList<>();
                                            languageList.forEach(
                                                    lang -> {
                                                        TrfProductAttrLangDOV2 langDOV2 = new TrfProductAttrLangDOV2();
                                                        langDOV2.setLanguageId(lang.getLanguageId());
                                                        langDOV2.setLabelName(lang.getLabelName());
                                                        langDOV2.setLabelValue(lang.getLabelValue());
                                                        langDOV2.setCustomerLabel(lang.getCustomerLabel());
                                                        langList.add(langDOV2);
                                                    }
                                            );
                                            productAttrDOV2.setLanguageList(langList);
                                        }

                                        productAttrDOV2List.add(productAttrDOV2);
                                    }
                            );
                            trfProductDOV2.setProductAttrList(productAttrDOV2List);
                        }
                        list.add(trfProductDOV2);
                    }
            );
            return list;
        }
        return new ArrayList<>();
    }

    public static TrfDOV2 toTrfDOV2(TrfFullDTO trfDTO) {
        CopyOptions copyOptions = CopyOptions.create().setIgnoreCase(true).ignoreNullValue();

        TrfDOV2 trfDO = new TrfDOV2();

        BeanUtil.copyProperties(trfDTO, trfDO, copyOptions);

        //report order信息补充
        if (Func.isNotEmpty(trfDO.getOrder()) && Func.isNotEmpty(trfDO.getReportList())) {
            TrfOrderDOV2 order = trfDO.getOrder();
            List<TrfReportDOV2> reportList = trfDO.getReportList();
            for (TrfReportDOV2 trfReportDOV2 : reportList) {
                if (Func.isEmpty(trfReportDOV2)) {
                    continue;
                }
                trfReportDOV2.setOrderNo(order.getOrderNo());
                trfReportDOV2.setOrderId(order.getOrderId());
                if (Func.isNotEmpty(trfDO.getHeader())) {
                    trfReportDOV2.setSystemId(Func.isNotEmpty(order.getSystemId()) ? order.getSystemId() : trfDO.getHeader().getSystemId());
                }
            }

        }
        //invoice order信息补充
        if (Func.isNotEmpty(trfDO.getOrder()) && Func.isNotEmpty(trfDO.getInvoiceList())) {
            TrfOrderDOV2 order = trfDO.getOrder();
            List<TrfInvoiceDOV2> invoiceList = trfDO.getInvoiceList();
            for (TrfInvoiceDOV2 invoiceDOV2 : invoiceList) {
                if (Func.isEmpty(invoiceDOV2)) {
                    continue;
                }
                invoiceDOV2.setOrderNo(order.getOrderNo());
                invoiceDOV2.setOrderId(order.getOrderId());
                if (Func.isNotEmpty(trfDO.getHeader())) {
                    invoiceDOV2.setSystemId(trfDO.getHeader().getSystemId());
                }
            }

        }

        if (Func.isNotEmpty(trfDO.getOrder()) && Func.isNotEmpty(trfDO.getQuotationList())) {
            TrfOrderDOV2 order = trfDO.getOrder();
            List<TrfQuotationDOV2> quotationList = trfDO.getQuotationList();
            for (TrfQuotationDOV2 quotationDOV2 : quotationList) {
                if (Func.isEmpty(quotationDOV2)) {
                    continue;
                }
                quotationDOV2.setOrderNo(order.getOrderNo());
                quotationDOV2.setOrderId(order.getOrderId());
                if (Func.isNotEmpty(trfDO.getHeader())) {
                    quotationDOV2.setSystemId(trfDO.getHeader().getSystemId());
                }
            }

        }
        return trfDO;
    }

    public static TrfFullDTO toTrfFullDTO(TrfHeaderDTO trfHeader, TrfSyncReq syncReq) {

        TrfSyncHeaderDTO header = syncReq.getHeader();


        TrfFullDTO trfFullDTO = new TrfFullDTO();
        trfFullDTO.setSystemId(syncReq.getSystemId());
        trfFullDTO.setAction(syncReq.getAction());
        TrfHeaderDTO trfHeaderDTO = new TrfHeaderDTO();
        trfHeaderDTO.setTrfId(trfHeader.getTrfId());
        trfHeaderDTO.setTrfNo(trfHeader.getTrfNo());
        trfHeaderDTO.setSampleReceiveDate(trfHeader.getSampleReceiveDate());
        trfHeaderDTO.setRefSystemId(header.getRefSystemId());
        if (Func.isNotEmpty(syncReq.getOrder()) && Func.isNotEmpty(syncReq.getOrder().getSystemId())) {
            trfHeaderDTO.setSystemId(syncReq.getOrder().getSystemId());
        } else {
            trfHeaderDTO.setSystemId(syncReq.getSystemId());
        }

        TrfLabDTO trfLabDTO = toTrfLabDTO(syncReq.getLabCode());
        if (Func.isNotEmpty(trfLabDTO)) {
            trfLabDTO.setLabCode(syncReq.getLabCode());
            trfHeaderDTO.setLab(trfLabDTO);
        }
        TrfHeaderDTO headerDTO = header.getTrfList().get(0);
        if (Func.isNotEmpty(headerDTO)) {
            trfHeaderDTO.setTrfTemplateId(headerDTO.getTrfTemplateId());
            trfHeaderDTO.setTrfTemplateName(headerDTO.getTrfTemplateName());
            trfHeaderDTO.setPurchaseOrderList(headerDTO.getPurchaseOrderList());
            trfHeaderDTO.setCustomerProductList(headerDTO.getCustomerProductList());
        }
        trfHeaderDTO.setTrfStatus(trfHeader.getTrfStatus());

        trfFullDTO.setHeader(trfHeaderDTO);
//        trfFullDTO.setCustomerList();
//        trfFullDTO.setProduct();
//        trfFullDTO.setSampleList();
//        trfFullDTO.setCareLabelList();
        trfFullDTO.setTestSampleList(syncReq.getTestSampleList());
        transformTrfTestSampleTrfFile(trfFullDTO.getTestSampleList());

//        trfFullDTO.setTestItemList();
//        trfFullDTO.setServiceRequirement();
//        trfFullDTO.setAttachmentList();
        trfFullDTO.setTrfList(syncReq.getHeader().getTrfList());
        trfFullDTO.setOthers(syncReq.getOthers());
        trfFullDTO.setOrder(syncReq.getOrder());
        transformTrfOrderTrfFile(trfFullDTO.getOrder());

        trfFullDTO.setTestLineList(syncReq.getTestLineList());
        trfFullDTO.setTestResultList(syncReq.getTestResultList());

        trfFullDTO.setQuotationList(syncReq.getQuotationList());
        transformTrfQuotationTrfFile(trfFullDTO.getQuotationList());

        trfFullDTO.setInvoiceList(syncReq.getInvoiceList());
        transformTrfInvoiceTrfFile(trfFullDTO.getInvoiceList());

        trfFullDTO.setReportList(syncReq.getReportList());
        transformTrfReportTrfFile(trfFullDTO.getReportList());

        return trfFullDTO;
    }

    public static TrfFullDTO toTrfFullDTO(TrfDTO trfDTO) {
        TrfFullDTO trfFullDTO = new TrfFullDTO();
        if (Func.isEmpty(trfDTO)) {
            return trfFullDTO;
        }
        List<TrfHeaderDTO> trfList = new ArrayList<>();
        trfList.add(trfDTO.getHeader());
        if(Func.isNotEmpty(trfDTO.getServiceRequirement())) {
            Optional.ofNullable(trfDTO.getHeader()).ifPresent(trf -> trf.setServiceRequirement(trfDTO.getServiceRequirement()));
        }
        List<TrfOrderDTO> orderList = new ArrayList<>();
        orderList.add(trfDTO.getOrder());
        trfFullDTO.setCustomerList(trfDTO.getCustomerList());
        trfFullDTO.setTestSampleList(trfDTO.getTestSampleList());
        trfFullDTO.setServiceRequirement(trfDTO.getServiceRequirement());
        trfFullDTO.setAttachmentList(trfDTO.getAttachmentList());
        trfFullDTO.setCareLabelList(trfDTO.getCareLabelList());
        if(!CollectionUtils.isEmpty(trfDTO.getSampleList())) {
            trfFullDTO.setSampleList(JSONArray.parseArray(JSONArray.toJSONString(trfDTO.getSampleList()), TrfProductSampleDTO.class));
        }
        List<TrfProductDTO> productList  = new ArrayList<>();
        productList.add(trfDTO.getProduct());
        trfFullDTO.setProductList(productList);
        trfFullDTO.setTrfList(trfList);
        trfFullDTO.setOrderList(orderList);
        trfFullDTO.setExternalObject(trfDTO.getExternalObject());
        return trfFullDTO;
    }

    public static TrfFullDTO toTrfFullDTO(TrfSyncQuotationReq trfSyncQuotationReq) {
        TrfFullDTO trfFullDTO = new TrfFullDTO();
        if (Func.isEmpty(trfSyncQuotationReq)) {
            return trfFullDTO;
        }
        List<TrfHeaderDTO> trfList = trfSyncQuotationReq.getTrfList();
        List<TrfOrderDTO> orderList = trfSyncQuotationReq.getOrderList();
        List<TrfReportDTO> reportList = trfSyncQuotationReq.getReportList();
        List<TrfQuotationDTO> quotationList = trfSyncQuotationReq.getQuotationList();
        trfFullDTO.setTrfList(trfList);
        trfFullDTO.setOrderList(orderList);
        trfFullDTO.setReportList(reportList);
        trfFullDTO.setQuotationList(quotationList);
        return trfFullDTO;
    }

    private static void transformTrfOrderTrfFile(TrfOrderDTO order) {
        if (Func.isEmpty(order)) {
            return;
        }
        if (Func.isEmpty(order.getAttachmentList())) {
            return;
        }
        for (TrfFileDTO trfFileDTO : order.getAttachmentList()) {
            transformTrfFile(trfFileDTO);
        }
    }

    private static void transformTrfTestSampleTrfFile(List<TrfTestSampleDTO> testSampleList) {
        if (Func.isEmpty(testSampleList)) {
            return;
        }
        for (TrfTestSampleDTO trfTestSampleDTO : testSampleList) {
            if (Func.isEmpty(trfTestSampleDTO.getTestSamplePhotoList())) {
                continue;
            }
            for (TrfFileDTO trfFileDTO : trfTestSampleDTO.getTestSamplePhotoList()) {
                transformTrfFile(trfFileDTO);
            }
        }
    }

    private static void transformTrfQuotationTrfFile(List<TrfQuotationDTO> quotationList) {
        if (Func.isEmpty(quotationList)) {
            return;
        }
        for (TrfQuotationDTO trfQuotationDTO : quotationList) {
            if (Func.isEmpty(trfQuotationDTO.getQuotationFileList())) {
                continue;
            }
            for (TrfFileDTO trfFileDTO : trfQuotationDTO.getQuotationFileList()) {
                transformTrfFile(trfFileDTO);
            }
        }
    }

    private static void transformTrfInvoiceTrfFile(List<TrfInvoiceDTO> invoiceList) {
        if (Func.isEmpty(invoiceList)) {
            return;
        }
        for (TrfInvoiceDTO invoiceDTO : invoiceList) {
            if (Func.isEmpty(invoiceDTO.getInvoiceFileList())) {
                continue;
            }
            for (TrfFileDTO trfFileDTO : invoiceDTO.getInvoiceFileList()) {
                transformTrfFile(trfFileDTO);
            }
        }
    }


    private static void transformTrfReportTrfFile(List<TrfReportDTO> reportList) {
        if (Func.isEmpty(reportList)) {
            return;
        }
        //reportFileList
        for (TrfReportDTO reportDTO : reportList) {
            if (Func.isEmpty(reportDTO.getReportFileList())) {
                continue;
            }
            for (TrfFileDTO trfFileDTO : reportDTO.getReportFileList()) {
                if (Func.isEmpty(trfFileDTO.getFilePath())) {
                    continue;
                }
                transformTrfFile(trfFileDTO);
            }
        }
        //sub report
        for (TrfReportDTO reportDTO : reportList) {
            if (Func.isEmpty(reportDTO.getSubReportList())) {
                continue;
            }
            for (TrfSubReportDTO subReportDTO : reportDTO.getSubReportList()) {
                if (Func.isEmpty(subReportDTO.getSubReportFileList())) {
                    continue;
                }
                for (TrfFileDTO trfFileDTO : subReportDTO.getSubReportFileList()) {
                    transformTrfFile(trfFileDTO);
                }
            }
        }
        //testMatrixFileList
        for (TrfReportDTO reportDTO : reportList) {
            if (Func.isEmpty(reportDTO.getReportMatrixList())) {
                continue;
            }
            for (TrfReportMatrixDTO trfReportMatrixDTO : reportDTO.getReportMatrixList()) {
                if (Func.isEmpty(trfReportMatrixDTO.getTestMatrixFileList())) {
                    continue;
                }
                for (TrfFileDTO trfFileDTO : trfReportMatrixDTO.getTestMatrixFileList()) {
                    transformTrfFile(trfFileDTO);
                }
            }
        }


    }

    private static void transformTrfFile(TrfFileDTO trfFileDTO) {
        if (Func.isEmpty(trfFileDTO)) {
            return;
        }
        if (Func.isNotEmpty(trfFileDTO.getCloudId())) {
            return;
        }
        Pattern pattern = Pattern.compile(URL_PATTERN);
        if (Func.isEmpty(trfFileDTO.getFilePath()) || !pattern.matcher(trfFileDTO.getFilePath()).matches()) {
            return;
        }
        FileClient fileClient = SpringUtil.getBean(FileClient.class);
        FileInfo fileInfo = null;
        try {
            fileInfo = fileClient.uploadFile(trfFileDTO.getFilePath());
        } catch (Exception e) {
            throw new BizException(StrUtil.format("filePath={} can not get the file", trfFileDTO.getFilePath()));
        }
        if (Func.isEmpty(fileInfo) || Func.isEmpty(fileInfo.getCloudID())) {
            Object[] args = {"filePath", trfFileDTO.getFilePath()};
            throw new BizException(I18nErrorCodeConstants.THE_DATA_MUST_BE_VALID, args);
        }
        trfFileDTO.setCloudId(fileInfo.getCloudID());
    }

    public static TrfLabDTO toTrfLabDTO(String labCode) {
        if (Func.isEmpty(labCode)) {
            return null;
        }
        FrameWorkClient frameWorkClient = SpringUtil.getBean(FrameWorkClient.class);
        LabInfo labInfo = frameWorkClient.getLabCodeInfoByLabCode(labCode);
        if (Func.isEmpty(labInfo)) {
            return null;
        }
        TrfLabDTO labDTO = new TrfLabDTO();
        labDTO.setLabId(Func.toLongObject(labInfo.getLaboratoryID(), null));
        labDTO.setLabCode(labInfo.getLaboratoryCode());
        labDTO.setOtherCode(labInfo.getLaboratoryCode());
        labDTO.setLabName(labInfo.getLaboratoryName());
        labDTO.setLabAddress(labInfo.getLaboratoryAddress());
        labDTO.setCountryId(labInfo.getCountryID());
        labDTO.setLocationId(Func.toInteger(labInfo.getLocationID(), null));
        labDTO.setLocationCode(labInfo.getLocationCode());
        labDTO.setBuId(Func.toInteger(labInfo.getProductLineID(), null));
        labDTO.setBuCode(labInfo.getProductLineAbbr());
        if (Func.isNotEmpty(labInfo.getLaboratoryContactName())) {
            TrfLabContactDTO contactDTO = new TrfLabContactDTO();
            contactDTO.setContactName(labInfo.getLaboratoryContactName());
            contactDTO.setContactEmail(labInfo.getLaboratoryContactEmail());
            contactDTO.setContactTelephone(labInfo.getLaboratoryContactTelephone());
            labDTO.setLabContact(contactDTO);
        }
        if (Func.isNotEmpty(labInfo.getLaboratoryNameCn()) || Func.isNotEmpty(labInfo.getLaboratoryAddressCn())) {
            TrfLabLanguageDTO labLanguageDTO = new TrfLabLanguageDTO();
            labLanguageDTO.setLabAddress(labInfo.getLaboratoryAddressCn());
            labLanguageDTO.setLabName(labInfo.getLaboratoryNameCn());
            labLanguageDTO.setLanguageId(LanguageType.Chinese.getLanguageId());
            labDTO.setLanguageList(Arrays.asList(labLanguageDTO));
        }
        return labDTO;
    }


    public static TrfHeaderDTO toTrfHeaderDTO(TrfHeaderDOV2 trfHeaderDOV2) {
        if (Func.isEmpty(trfHeaderDOV2)) {
            return null;
        }

//        TrfHeaderDTO trfHeader = com.sgs.framework.tool.utils.BeanUtil.copyProperties(trfHeaderDOV2, TrfHeaderDTO.class);
        TrfHeaderDTO trfHeader = JSONObject.parseObject(JSONObject.toJSONString(trfHeaderDOV2), TrfHeaderDTO.class);


        TrfOtherDOV2 others = trfHeaderDOV2.getOthers();

        if (Func.isNotEmpty(others)) {
            TrfPendingDOV2 pending = others.getPending();
            String trfRemark = others.getTrfRemark();
            Integer cancelType = others.getCancelType();
            String cancelRemark = others.getCancelRemark();
            Integer removeType = others.getRemoveType();
            String removeRemark = others.getRemoveRemark();
            String repeatService = others.getRepeatService();
            TrfOtherDTO otherDTO = new TrfOtherDTO();

            if (Func.isNotEmpty(pending)) {
                TrfPendingDTO trfPendingDTO = new TrfPendingDTO();
                trfPendingDTO.setPendingFlag(pending.getPendingFlag());
                trfPendingDTO.setPendingType(Func.isNotEmpty(pending.getPendingType()) ? String.valueOf(pending.getPendingType()) : null);
                trfPendingDTO.setPendingRemark(pending.getPendingRemark());
                otherDTO.setPending(trfPendingDTO);
            }
            if (Func.isNotEmpty(cancelRemark) || Func.isNotEmpty(cancelType)) {
                TrfCancelDTO cancelDTO = new TrfCancelDTO();
                cancelDTO.setCancelType(cancelType);
                cancelDTO.setCancelReason(cancelRemark);
                otherDTO.setCancel(cancelDTO);
            }
            if (Func.isNotEmpty(removeType) || Func.isNotEmpty(removeRemark)) {
                TrfRemoveDTO removeDTO = new TrfRemoveDTO();
                removeDTO.setRemoveType(removeType);
                removeDTO.setRemoveReason(removeRemark);
                otherDTO.setRemove(removeDTO);
            }
            otherDTO.setRepeatService(repeatService);
            otherDTO.setTrfRemark(trfRemark);
            trfHeader.setOthers(otherDTO);
        }
        return trfHeader;
    }

    public static List<TrfQuotationDOV2> convertQuotationDOList(List<TrfQuotationDTO> quotationList) {
        if (Func.isEmpty(quotationList)) {
            return null;
        }
        return JSONArray.parseArray(JSONObject.toJSONString(quotationList), TrfQuotationDOV2.class);
    }

    public static TrfApproveDO toTrfApprovedDO(TrfApproveQuotationDTO data) {
        TrfApproveDO trfApproveDO = new TrfApproveDO();
        if (Func.isEmpty(data)) {
            return trfApproveDO;
        }
        trfApproveDO.setTrfNo(data.getTrfNo());
        trfApproveDO.setRefSystemId(data.getRefSystemId());
        trfApproveDO.setLabCode(data.getLabCode());
        trfApproveDO.setObjectNo(data.getQuotationNo());
        trfApproveDO.setObjectVersionId(data.getQuotationVersionId());
        trfApproveDO.setApproveResult(data.getQuotationStatus());
        trfApproveDO.setApproveResultText(data.getQuotationStatusText());
        trfApproveDO.setApproveRemark(data.getModifyContent());
        trfApproveDO.setApproveBy(data.getAccount());
        trfApproveDO.setReportNo(data.getReportNo());
        trfApproveDO.setCustomerEn(data.getCustomerEn());
        trfApproveDO.setCustomerCn(data.getCustomerCn());
        return trfApproveDO;
    }
}
