package com.sgs.customerbiz.biz.dff;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.sgs.config.api.dto.DFFMappingDTO;
import com.sgs.customerbiz.integration.LocalILayerClient;
import com.sgs.customerbiz.model.trf.dto.TrfProductAttrDTO;
import com.sgs.customerbiz.model.trf.dto.TrfProductAttrLangDTO;
import com.sgs.customerbiz.model.trf.dto.TrfProductDTO;
import com.sgs.customerbiz.model.trf.dto.TrfProductSampleDTO;
import com.sgs.customerbiz.model.trf.dto.TrfSampleAttrDTO;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.tool.utils.Func;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @Desc
 * <AUTHOR>
 * @date 2024/1/9 20:14
 */
@Slf4j
@Component
public class DFFConvertor {

    @Autowired
    private LocalILayerClient iLayerClient;

    public Map<String, Object> convertExternalObjectKey(Integer refSystemId, String buCode, String customerGroup, String customerNo,String templateId, String templateType, Map<String, Object> externalObject) {
        //考虑productAttrDTOList 非空
        if (Func.isEmpty(externalObject)) {
            return externalObject;
        }
        List<DFFMappingDTO> dffMappingList = iLayerClient.queryDFFMappingConfig(refSystemId, buCode, customerGroup, customerNo, templateId, templateType)
                .stream()
                .filter(dffMappingDTO -> Objects.equals(dffMappingDTO.getType(), "externalList"))
                .collect(Collectors.toList());
        //考虑productAttrDTOList 非空
        if (Func.isEmpty(externalObject)) {
            return externalObject;
        }

        //为便于后续处理，将dffMappingList 转换成map，key为customerLabel, value为具有相同customerLabel的DFFMappingDTO列表
        Map<String, List<DFFMappingDTO>> dffMappingDTOMap = dffMappingList.stream()
                .collect(Collectors.groupingBy(DFFMappingDTO::getCustomerLabel));

        Map<String, Object> result = new HashMap<>();
        externalObject.forEach((key, value) -> {
            List<DFFMappingDTO> mappings = dffMappingDTOMap.get(key);
            if (Func.isNotEmpty(mappings)) {
                mappings.forEach(dffMappingDTO -> {
                    if (Func.isNotEmpty(dffMappingDTO.getLabelCode())) {
                        result.put(dffMappingDTO.getLabelCode(), value);
                    }
                });
            }
        });
        return result;
    }

    public List<TrfProductAttrDTO> convertProductDFFByCustomerLabel(Integer refSystemId, String buCode, String customerGroup, String customerNo,String templateId, String templateType, List<TrfProductAttrDTO> productAttrDTOList) {
        //考虑productAttrDTOList 非空
        if (Func.isEmpty(productAttrDTOList)) {
            log.info("Param List is Empty , please check . refSystemID={},buCode={},customerGroup={},customerNo={}", refSystemId, buCode, customerGroup, customerNo);
            return null;
        }

        // 获得DFFMappingConfig
        List<DFFMappingDTO> dffMappingList = iLayerClient.queryDFFMappingConfig(refSystemId, buCode, customerGroup, customerNo, templateId, templateType)
                .stream()
                .filter(dffMappingDTO -> !Objects.equals(dffMappingDTO.getType(), "externalList"))
                .collect(Collectors.toList());

        if (Func.isEmpty(dffMappingList)) {
            log.info("Don't found the dff mapping configration, please check . refSystemID={},buCode={},customerGroup={},customerNo={}", refSystemId, buCode, customerGroup, customerNo);
            //没有找到配置信息，则不处理，原参数返回
            return productAttrDTOList;
        }

        //为便于后续处理，将dffMappingList 转换成map，key为customerLabel, value为具有相同customerLabel的DFFMappingDTO列表
        Map<String, List<DFFMappingDTO>> dffMappingDTOMap = dffMappingList.stream()
                .collect(Collectors.groupingBy(DFFMappingDTO::getCustomerLabel));

        List<TrfProductAttrDTO> resultList = new ArrayList<>();
        
        productAttrDTOList.forEach(productAttrDTO -> {
            if (Func.isEmpty(productAttrDTO.getCustomerLabel())) {
                resultList.add(productAttrDTO);
                return;
            }

            List<DFFMappingDTO> dffMappingDTOs = dffMappingDTOMap.get(productAttrDTO.getCustomerLabel());
            if (Func.isNotEmpty(dffMappingDTOs)) {
                for (DFFMappingDTO dffMappingDTO : dffMappingDTOs) {
                    TrfProductAttrDTO newProductAttrDTO = new TrfProductAttrDTO();
                    BeanUtil.copyProperties(productAttrDTO, newProductAttrDTO);
                    
                    newProductAttrDTO.setStandardDff(dffMappingDTO.getStandardDff());
                    newProductAttrDTO.setFieldCode(dffMappingDTO.getFieldCode());
                    newProductAttrDTO.setLabelName(dffMappingDTO.getLabelName());
                    newProductAttrDTO.setLabelCode(dffMappingDTO.getLabelCode());
                    newProductAttrDTO.setAttrSeq(dffMappingDTO.getSeq());
                    newProductAttrDTO.setLabelValue(dffMappingDTO.getValueMap().getOrDefault(productAttrDTO.getLabelValue(), productAttrDTO.getLabelValue()));

                    if (Func.isNotEmpty(productAttrDTO.getLanguageList())) {
                        List<TrfProductAttrLangDTO> newLangList = new ArrayList<>();
                        productAttrDTO.getLanguageList().forEach(trfProductAttrLangDTO -> {
                            TrfProductAttrLangDTO newLangDTO = new TrfProductAttrLangDTO();
                            BeanUtil.copyProperties(trfProductAttrLangDTO, newLangDTO);
                            newLangDTO.setLabelName(dffMappingDTO.getLabelName());
                            newLangDTO.setLabelValue(dffMappingDTO.getValueMap().getOrDefault(productAttrDTO.getLabelValue(), productAttrDTO.getLabelValue()));
                            newLangList.add(newLangDTO);
                        });
                        newProductAttrDTO.setLanguageList(newLangList);
                    } else {
                        TrfProductAttrLangDTO productAttrLangDTO = new TrfProductAttrLangDTO();
                        productAttrLangDTO.setLanguageId(LanguageType.English.getLanguageId());
                        productAttrLangDTO.setLabelName(newProductAttrDTO.getLabelName());
                        productAttrLangDTO.setCustomerLabel(newProductAttrDTO.getCustomerLabel());
                        productAttrLangDTO.setLabelValue(dffMappingDTO.getValueMap().getOrDefault(productAttrDTO.getLabelValue(), productAttrDTO.getLabelValue()));

                        newProductAttrDTO.setLanguageList(Arrays.asList(productAttrLangDTO));
                    }
                    
                    resultList.add(newProductAttrDTO);
                }
            } else {
                resultList.add(productAttrDTO);
            }
        });

        return resultList;
    }

    public List<TrfSampleAttrDTO> convertSampleDFFByCustomerLabel(Integer refSystemId, String buCode, String customerGroup, String customerNo, String templateId, String templateType, List<TrfSampleAttrDTO> sampleAttrDTOList) {
        //考虑sampleAttrDTOList 非空
        if (Func.isEmpty(sampleAttrDTOList)) {
            log.info("Param List is Empty , please check . refSystemID={},buCode={},customerGroup={},customerNo={}", refSystemId, buCode, customerGroup, customerNo);
            return null;
        }

        // 获得DFFMappingConfig
        List<DFFMappingDTO> dffMappingList = iLayerClient.queryDFFMappingConfig(refSystemId, buCode, customerGroup, customerNo, templateId, templateType);

        if (Func.isEmpty(dffMappingList)) {
            log.info("Don't found the dff mapping configration, please check . refSystemID={},buCode={},customerGroup={},customerNo={}", refSystemId, buCode, customerGroup, customerNo);
            //没有找到配置信息，则不处理，原参数返回
            return sampleAttrDTOList;
        }

        //将dffMappingList按customerLabel分组
        Map<String, List<DFFMappingDTO>> dffMappingDTOMap = dffMappingList.stream()
                .collect(Collectors.groupingBy(DFFMappingDTO::getCustomerLabel));

        List<TrfSampleAttrDTO> resultList = new ArrayList<>();

        sampleAttrDTOList.forEach(productAttrDTO -> {
            if (Func.isEmpty(productAttrDTO.getCustomerLabel())) {
                resultList.add(productAttrDTO);
                return;
            }

            List<DFFMappingDTO> dffMappingDTOs = dffMappingDTOMap.get(productAttrDTO.getCustomerLabel());
            if (Func.isNotEmpty(dffMappingDTOs)) {
                for (DFFMappingDTO dffMappingDTO : dffMappingDTOs) {
                    TrfSampleAttrDTO newProductAttrDTO = new TrfSampleAttrDTO();
                    BeanUtil.copyProperties(productAttrDTO, newProductAttrDTO);
                    
                    newProductAttrDTO.setFieldCode(dffMappingDTO.getFieldCode());
                    newProductAttrDTO.setLabelName(dffMappingDTO.getLabelName());
                    newProductAttrDTO.setLabelCode(dffMappingDTO.getLabelCode());
                    newProductAttrDTO.setAttrSeq(dffMappingDTO.getSeq());
                    newProductAttrDTO.setLabelValue(dffMappingDTO.getValueMap().getOrDefault(productAttrDTO.getLabelValue(), productAttrDTO.getLabelValue()));

                    TrfProductAttrLangDTO productAttrLangDTO = new TrfProductAttrLangDTO();
                    productAttrLangDTO.setLanguageId(LanguageType.English.getLanguageId());
                    productAttrLangDTO.setLabelName(newProductAttrDTO.getLabelName());
                    productAttrLangDTO.setCustomerLabel(newProductAttrDTO.getCustomerLabel());
                    productAttrLangDTO.setLabelValue(dffMappingDTO.getValueMap().getOrDefault(productAttrDTO.getLabelValue(), productAttrDTO.getLabelValue()));
                    productAttrLangDTO.setValue(dffMappingDTO.getValueMap().getOrDefault(productAttrDTO.getLabelValue(), productAttrDTO.getLabelValue()));

                    newProductAttrDTO.setLanguageList(Arrays.asList(productAttrLangDTO));
                    
                    resultList.add(newProductAttrDTO);
                }
            } else {
                resultList.add(productAttrDTO);
            }
        });

        return resultList;
    }

    public List<TrfSampleAttrDTO> convertSampleDFFByLabelCode(Integer refSystemId, String buCode, String customerGroup, String customerNo, List<TrfSampleAttrDTO> sampleAttrDTOList) {
        //考虑sampleAttrDTOList 非空
        if (Func.isEmpty(sampleAttrDTOList)) {
            log.info("Param List is Empty , please check . refSystemID={},buCode={},customerGroup={},customerNo={}", refSystemId, buCode, customerGroup, customerNo);
            return null;
        }

        // 获得DFFMappingConfig
        List<DFFMappingDTO> dffMappingList = iLayerClient.queryDFFMappingConfig(refSystemId, buCode, customerGroup, customerNo);

        if (Func.isEmpty(dffMappingList)) {
            log.info("Don't found the dff mapping configration, please check . refSystemID={},buCode={},customerGroup={},customerNo={}", refSystemId, buCode, customerGroup, customerNo);
            //没有找到配置信息，则不处理，原参数返回
            return sampleAttrDTOList;
        }

        //为便于后续处理，将dffMappingList 转换成map，key为dffFieldName
        Map<String, DFFMappingDTO> dffMappingDTOMap = dffMappingList.stream().collect(Collectors.toMap(DFFMappingDTO::getLabelCode, dffMappingDTO -> dffMappingDTO));

        /**
         * 逐个字段处理
         * 1、没有映射到的字段跳过（做日志输出）
         */
        sampleAttrDTOList.forEach(productAttrDTO -> {
            if (Func.isEmpty(productAttrDTO.getCustomerLabel())) {
                return;
            }

            DFFMappingDTO dffMappingDTO = dffMappingDTOMap.get(productAttrDTO.getLabelCode());
            if (Func.isNotEmpty(dffMappingDTO)) {
                productAttrDTO.setFieldCode(dffMappingDTO.getFieldCode());
                productAttrDTO.setLabelName(dffMappingDTO.getLabelName());
                productAttrDTO.setLabelCode(dffMappingDTO.getLabelCode());
                productAttrDTO.setCustomerLabel(dffMappingDTO.getCustomerLabel());
                productAttrDTO.setAttrSeq(dffMappingDTO.getSeq());

                TrfProductAttrLangDTO productAttrLangDTO = new TrfProductAttrLangDTO();
                productAttrLangDTO.setLanguageId(LanguageType.English.getLanguageId());
                productAttrLangDTO.setLabelName(productAttrDTO.getLabelName());
                productAttrLangDTO.setCustomerLabel(productAttrDTO.getCustomerLabel());
                productAttrLangDTO.setLabelValue(productAttrDTO.getLabelValue());
                productAttrLangDTO.setValue(productAttrDTO.getLabelValue());

                productAttrDTO.setLanguageList(Arrays.asList(productAttrLangDTO));
            }
        });


        return sampleAttrDTOList;
    }


    public void convertDffLabCode(List<TrfProductDTO> productList, List<TrfProductSampleDTO> sampleList) {
        if (Func.isNotEmpty(productList)) {
            productList.forEach(
                    product -> convert(product.getProductAttrList())
            );
        }
        if (Func.isNotEmpty(sampleList)) {
            sampleList.forEach(
                    product -> convert(product.getSampleAttrList())
            );
        }
    }


    public void convertDffLabCodeLower(List<TrfProductDTO> productList, List<TrfProductSampleDTO> sampleList) {
        if (Func.isNotEmpty(productList)) {
            productList.forEach(
                    product -> convertLower(product.getProductAttrList())
            );
        }
        if (Func.isNotEmpty(sampleList)) {
            sampleList.forEach(
                    product -> convertLower(product.getSampleAttrList())
            );
        }
    }


    private static void convert(List<TrfProductAttrDTO> productAttrList) {
        if (Func.isEmpty(productAttrList)) {
            return;
        }
        if (Func.isNotEmpty(productAttrList)) {
            productAttrList.forEach(
                    product -> product.setLabelCode(capitalizeFirstLetter(product.getLabelCode()))
            );
        }
    }


    private static void convertLower(List<TrfProductAttrDTO> productAttrList) {
        if (Func.isEmpty(productAttrList)) {
            return;
        }
        if (Func.isNotEmpty(productAttrList)) {
            productAttrList.forEach(
                    product -> product.setLabelCode(capitalizeFirstLower(product.getLabelCode()))
            );
        }
    }

    public static String capitalizeFirstLetter(String str) {
       return StrUtil.upperFirst(str);
    }

    public static String capitalizeFirstLower(String str) {
       return StrUtil.lowerFirst(str);
    }
}
