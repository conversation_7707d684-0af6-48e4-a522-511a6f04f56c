package com.sgs.customerbiz.facade.model.trf.req;

import com.sgs.customerbiz.facade.model.enums.SearchType;
import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
public final class BoundTrfInfoSearchReq extends BaseRequest {
    // 是否已开单
    private Boolean hasOrder = false;
    // 是否查询已绑定订单
    private Boolean createMoreOrder = false;
    //是否关联已经建立TF-Order的数据 且 boundStatus=0的数据 ,非SL目前是这个逻辑
    private Boolean hasConnect = false;
    private Long trfId;

    private String labCode;
    private Integer refSystemId;

    private List<String> trfNos;
    private List<String> packageBarcodes;
    private String orderNo;
    private String createDate;
    private String endDate;
    private String trfStartDate;
    private String trfEndDate;
    private String status;
    private String mockData;
    private List<String> createBys;
    private Map<String, List<String>> requestMaps;

    @NotNull(message = "searchType 不能为空")
    private SearchType searchType;

    private Integer page;
    private Integer rows;

    public Integer getOffset() {
        if(Objects.isNull(page) || Objects.isNull(rows)) {
            return 0;
        }
        return (page-1) * rows;
    }

    public void clearQuery() {
        setTrfId(null);
        setLabCode(null);
        setPackageBarcodes(null);
        setOrderNo(null);
        setCreateDate(null);
        setEndDate(null);
        setTrfStartDate(null);
        setTrfEndDate(null);
        setStatus(null);
        setMockData(null);
        setCreateBys(null);
        setRequestMaps(null);
    }

}
