package com.sgs.customerbiz.facade.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.sgs.customerbiz.app.router.ProcessRouterService;
import com.sgs.customerbiz.biz.service.todolist.TodoListService;
import com.sgs.customerbiz.biz.service.todolist.CustomerTrfService;
import com.sgs.customerbiz.facade.ICustomerTodoListFacade;
import com.sgs.customerbiz.facade.model.todolist.dto.TrfTodoDTO;
import com.sgs.customerbiz.facade.model.todolist.req.*;
import com.sgs.customerbiz.facade.model.todolist.rsp.CheckTrfInfoRsp;
import com.sgs.customerbiz.facade.model.todolist.rsp.GetTrfDetailPrintRsp;
import com.sgs.customerbiz.facade.model.todolist.rsp.TrfInfoRsp;
import com.sgs.customerbiz.facade.model.trf.rsp.CustomerTrfInfoRsp;
import com.sgs.framework.core.base.BaseResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component("customerBizTodoListFacade")
public class CustomerTodoListFacadeImpl implements ICustomerTodoListFacade {
    @Autowired
    private TodoListService todoListService;
    @Autowired
    private CustomerTrfService customerTrfService;
    @Autowired
    private ProcessRouterService processRouterService;

    @Override
    public BaseResponse refSystemId(QueryRefSystemId reqObject) {
        return BaseResponse.newSuccessInstance(todoListService.refSystemId(reqObject));
    }

    @Override
    public BaseResponse trfTodoList(TrfTodoReq reqObject) {
        return BaseResponse.newInstance(todoListService.trfTodoList(reqObject));
    }

    @Override
    public BaseResponse importTrfNo(JsonNode reqObject) {
        return BaseResponse.newInstance(processRouterService.importTrfNo(reqObject));
    }
    @Override
    public BaseResponse updateTrfInfo(UpdateTrfInfoReq reqObject) {
        return BaseResponse.newInstance(todoListService.updateTrfInfo(reqObject));
    }

    @Override
    public BaseResponse getTrfInfoList(JsonNode reqObject) {
        return BaseResponse.newInstance(todoListService.getTrfInfoList(reqObject));
    }

    @Override
    public BaseResponse exportTrfInfoList(JsonNode reqObject) {
        return BaseResponse.newInstance(todoListService.exportTrfInfoList(reqObject));
    }
    @Override
    public BaseResponse getTrfDetailInfo(TrfDetailReq reqObject) {
        return BaseResponse.newInstance(todoListService.getTrfDetailInfo(reqObject));
    }
    @Override
    public BaseResponse getCustomerTrfDetailInfo(TrfDetailReq reqObject) {
        return BaseResponse.newInstance(todoListService.getCustomerTrfDetailInfo(reqObject));
    }

    @Override
    public BaseResponse<List<TrfInfoRsp>> getTrfInfo(TrfInfoReq reqObject) {
        return BaseResponse.newInstance(customerTrfService.getTrfInfo(reqObject));
    }

    @Override
    public BaseResponse<CheckTrfInfoRsp> checkTrfInfo(CheckTrfInfoReq reqObject) {
        return BaseResponse.newInstance(customerTrfService.checkTrfInfo(reqObject));
    }

    @Override
    public BaseResponse updateTrfBoundStatus(TrfBoundStatusReq reqObject) {
        return BaseResponse.newInstance(customerTrfService.updateTrfBoundStatus(reqObject));
    }

    /**
     * 绑定Trf
     * @param reqObject
     * @return
     */
    @Override
    public BaseResponse bindTrf(BindTrfReq reqObject) {
        return BaseResponse.newInstance(customerTrfService.bindTrf(reqObject));
    }

    /**
     * 解绑Trf
     * @param reqObject
     * @return
     */
    @Override
    public BaseResponse unBindTrf(UnBindTrfReq reqObject){
        return BaseResponse.newInstance(customerTrfService.unBindTrf(reqObject));
    }

    @Override
    public BaseResponse getTrfStatus(Integer type) {
        return BaseResponse.newInstance(customerTrfService.getTrfStatus(type));
    }


    @Override
    public BaseResponse sendTrfMainInfo(SendTrfMainInfoReq reqObject) {
        return BaseResponse.newInstance(todoListService.sendTrfMainInfo(reqObject));
    }

    @Override
    public BaseResponse<List<TrfTodoDTO>> searchTrfNo(SearchTrfNoReq reqObject) {
        return BaseResponse.newInstance(todoListService.searchTrfNo(reqObject));
    }

    @Override
    public BaseResponse<CustomerTrfInfoRsp> queryCustomerTrfInfoByTrfNo(SearchTrfNoReq reqObject) {
        return BaseResponse.newInstance(todoListService.queryCustomerTrfInfoByTrfNo(reqObject));
    }

    @Override
    public BaseResponse<Integer> checkCustomerRule(SearchTrfNoReq reqObject) {
        return BaseResponse.newInstance(todoListService.checkCustomerRule(reqObject));
    }

    @Override
    public BaseResponse<GetTrfDetailPrintRsp> getTrfDetailPrintInfo(GetTrfDetailPrintReq reqObject) {
        return BaseResponse.newInstance(todoListService.getTrfDetailPrintInfo(reqObject));
    }

}
