package com.sgs.customerbiz.core.errorcode.enums;

public enum ErrorFunctionTypeEnum {
    VALIDATION("01", "验证"),
    GETINFO("02", "获取信息"),
    CREATESAVE("03", "创建/保存"),
    ORDERTOTRF("04", "OrderToTRF"),
    STATUSCONTROL("05", "状态控制"),
    SYNC("06", "同步"),
    BIND("07", "绑定"),
    UNBIND("08","解绑"),
    CANCEL("09","取消"),
    RETURN("10","回退"),
    PENDING("11","暂停"),
    UNPENDING("12","取消暂停"),
    PROCESS("13,","流程处理"),
    BINDTRF("14", "bindTrf");

    private final String code;
    private final String description;

    ErrorFunctionTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    public static ErrorFunctionTypeEnum fromCode(String code) {
        for (ErrorFunctionTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
