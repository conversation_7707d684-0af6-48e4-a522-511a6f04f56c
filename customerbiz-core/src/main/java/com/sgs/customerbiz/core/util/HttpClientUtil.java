/*
 *
 * (C) Copyright 2016 Ymatou (http://www.ymatou.com/).
 * All rights reserved.
 *
 */

package com.sgs.customerbiz.core.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.sgs.tools.traceability.interceptor.HttpClientHeaderInterceptor;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.*;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.ws.rs.core.MediaType;
import java.io.*;
import java.net.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.*;

public class HttpClientUtil {
    private static final Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);

    /**
     * 三次握手时间
     */
    private static final Integer CONN_TIME_OUT = 3000;
    /**
     * 响应超时时间，超过此时间不再读取响应；
     */
    private static final Integer SOCKET_TIME_OUT = 20000;
    private static final Integer DEFAULT_MAX_PER_ROUTE = 200;
    private static final Integer MAX_TOTAL = 1000;
    /**
     * http clilent中从connetcion pool中获得一个connection的超时时间；
     */
    private static final Integer CONNECTION_REQUEST_TIMEOUT = 30000;
    private static final RequestConfig requestConfig;
    private static final HttpClient httpClient;

    private static int timeout = 60;

    // 编码格式。发送编码格式统一用UTF-8
    private static String ENCODING = "UTF-8";
    //private static Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);

    static {
        requestConfig = RequestConfig.custom().setConnectionRequestTimeout(CONN_TIME_OUT)
                .setSocketTimeout(SOCKET_TIME_OUT).build();

        PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
        cm.setDefaultMaxPerRoute(DEFAULT_MAX_PER_ROUTE);//每个route默认的最大连接数
        cm.setMaxTotal(MAX_TOTAL);//整个连接池的最大连接数

        httpClient = HttpClients.custom().setConnectionManager(cm)
                .addInterceptorFirst(new HttpClientHeaderInterceptor())
                .addInterceptorLast((HttpRequestInterceptor) (httpRequest, httpContext) -> {
                    try {
                        URI uri = new URI(httpRequest.getRequestLine().getUri());
                        httpContext.setAttribute("Path", uri.getPath());
                        httpContext.setAttribute("Sample", Timer.start());
                    } catch (Throwable e) {
                        //ignore
                    }
                })
                .addInterceptorLast((HttpResponseInterceptor) (httpResponse, httpContext) -> {
                    try {
                        Timer.Sample sample = (Timer.Sample) httpContext.getAttribute("Sample");
                        HttpHost host = (HttpHost) httpContext.getAttribute("http.target_host");
                        String path = (String) httpContext.getAttribute("Path");
                        int resCode = httpResponse.getStatusLine().getStatusCode();
                        MeterRegistry registry = SpringUtil.getBean(MeterRegistry.class);
                        Timer timer = Timer.builder("apache.http.client")
                                .tag("host", host.getHostName())
                                .tag("path", path)
                                .tag("response_status", resCode + "")
                                .publishPercentiles(0.5, 0.95, 0.99)
//                                .publishPercentileHistogram()
//                                .serviceLevelObjectives(Duration.ofMillis(100))
//                                .minimumExpectedValue(Duration.ofMillis(1))
//                                .maximumExpectedValue(Duration.ofSeconds(10))
                                .register(registry);
                        sample.stop(timer);
                    } catch (Throwable e) {
                        //ignore
                    }
                })
                .setDefaultRequestConfig(requestConfig)
                .build();
    }

    public static String requestGet(String url, Map<String, String> paramsMap, Integer timeout) throws Exception {
        return sendGet(url, initParams(paramsMap), timeout);
    }

    public static String sendGet(String url, List<NameValuePair> params) throws Exception {
        return sendGet(url, params, null);
    }

    public static String sendGet(String url, List<NameValuePair> params, Integer timeout) throws Exception {
        // Get请求
        HttpGet httpGet = new HttpGet(url);

        try {
            if (timeout != null && timeout > 0) {
                httpGet.setConfig(RequestConfig.copy(requestConfig).setSocketTimeout(timeout).build());
            }

            // 设置参数
            String str = EntityUtils.toString(new UrlEncodedFormEntity(params, Consts.UTF_8));
            httpGet.setURI(new URI(httpGet.getURI().toString() + "?" + str));
            // 发送请求
            HttpResponse response = httpClient.execute(httpGet);
            //logger.info("GET request url:{} params:{} response:{} time:{}", url, paramsMap, response, System.currentTimeMillis() - start);
            // 获取返回数据
            String retStr = getSuccessRetFromResp(response, url, JSON.toJSONString(params));

            return retStr;
        } finally {
            httpGet.releaseConnection();
        }
    }

    /**
     *
     * @param reqUrl
     * @param paramMaps
     * @param host
     * @param port
     * @return
     * @throws Exception
     */
    public static String sendGet(String reqUrl, Map<String, String> paramMaps, String host, int port) throws Exception {
        StringBuffer append = new StringBuffer();
        BufferedReader reader = null;
        try {
            List<NameValuePair> params = initParams(paramMaps);
            // 设置参数
            String reqParams = EntityUtils.toString(new UrlEncodedFormEntity(params, Consts.UTF_8));

            URL client = new URL(String.format("%s?%s", reqUrl, reqParams));
            // 设置代理
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(host, port));
            URLConnection connection = client.openConnection(proxy);

            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"));

            String line;
            while ((line = reader.readLine()) != null) {
                append.append(line);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                }
            } catch (IOException e) {
            }
        }
        return append.toString();
    }

    /**
     *
     * @param reqUrl
     * @param paramMaps
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> T sendGet(String reqUrl, Map<String, String> paramMaps, Class<T> clazz) throws Exception {
        String jsonStr = requestGet(reqUrl, paramMaps, null);
        if (StringUtils.isEmpty(jsonStr)) {
            return null;
        }
        return JSON.parseObject(jsonStr, clazz);
    }

    public static <T> List<T> get(String reqUrl, Map<String, String> paramMaps, Class<T> clazz) throws Exception {
        String jsonStr = requestGet(reqUrl, paramMaps, null);
        if (StringUtils.isEmpty(jsonStr)) {
            return null;
        }
        return JSON.parseArray(jsonStr, clazz);
    }

    public static String sendGet(String reqUrl, Map<String, String> paramMaps) throws Exception {
        return requestGet(reqUrl, paramMaps, null);
    }

    /**
     * @param reqUrl
     * @param paramMaps
     * @param host
     * @param port
     * @return
     * @throws Exception
     */
    public static String sendPost(String reqUrl, Map<String, Object> paramMaps, String host, int port) throws Exception {
        StringBuffer append = new StringBuffer();
        BufferedReader reader = null;
        PrintWriter out = null;
        try {
            // 设置参数
            String reqParams = JSON.toJSONString(paramMaps);
            URL client = new URL(reqUrl);
            // 设置代理
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(host, port));
            URLConnection connection = client.openConnection(proxy);

            connection.setRequestProperty("Content-Type", MediaType.APPLICATION_JSON);
            // 设置超时时间
            /*connection.setConnectTimeout(5000);
            connection.setReadTimeout(15000);*/

            // 发送POST请求必须设置如下两行
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(connection.getOutputStream());
            // 发送请求参数
            out.print(reqParams);
            // flush输出流的缓冲
            out.flush();

            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"));
            String line;
            while ((line = reader.readLine()) != null) {
                append.append(line);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
            } catch (Exception e) {
            }
            try {
                if (reader != null) {
                    reader.close();
                }
            } catch (IOException e) {
            }
        }
        return append.toString();
    }

    /**
     * @param reqUrl
     * @param paramMaps
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> T sendPost(String reqUrl, Map<String, Object> paramMaps, Class<T> clazz) throws Exception {
        String jsonStr = postJson(reqUrl, JSON.toJSONString(paramMaps));
        if (StringUtils.isEmpty(jsonStr)) {
            return null;
        }
        return JSON.parseObject(jsonStr, clazz);
    }

    /**
     * @param reqUrl
     * @param reqParams
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> List<T> sendPost(String reqUrl, Object reqParams, Class<T> clazz) throws Exception {
        String jsonStr = postJson(reqUrl, JSON.toJSONString(reqParams));
        if (StringUtils.isEmpty(jsonStr)) {
            return null;
        }
        return JSON.parseArray(jsonStr, clazz);
    }

    public static <T> T doPost(String reqUrl, Object reqParams, Class<T> clazz) throws Exception {
        String jsonStr = postJson(reqUrl, JSON.toJSONString(reqParams, SerializerFeature.WriteMapNullValue));
        if (StringUtils.isEmpty(jsonStr)) {
            return null;
        }
        return JSON.parseObject(jsonStr, clazz);
    }

    /**
     * @param reqUrl
     * @param httpEntity
     * @return
     * @throws Exception
     */
    public static String sendPost(String reqUrl, HttpEntity httpEntity) throws Exception {
        CloseableHttpClient client = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String result = StringUtils.EMPTY;
        try {
            HttpPost httpPost = new HttpPost(reqUrl);
            httpPost.setEntity(httpEntity);
            CloseableHttpClient httpClient = HttpClients.createDefault();
            response = httpClient.execute(httpPost);
            HttpEntity responseEntity = response.getEntity();
            result = EntityUtils.toString(responseEntity, Charset.forName("UTF-8"));
            return result;
        } catch (IOException var14) {
            logger.error("post请求提交失败,url:{},result:{}, errorMsg:{}", reqUrl, result, var14.getMessage(), var14);
        } finally {
            try {
                if (!Objects.isNull(response)) {
                    response.close();
                }

                if (!Objects.isNull(client)) {
                    client.close();
                }
            } catch (Exception var13) {
                logger.info("post fail,url:" + reqUrl + ",Exception" + var13.getMessage());
            }

        }
        return null;

    }

    /**
     * @param reqUrl
     * @param reqParams
     * @param typeRef
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> T sendPost(String reqUrl, Object reqParams, TypeReference<T> typeRef) throws Exception {
        String jsonStr = postJson(reqUrl, JSON.toJSONString(reqParams, SerializerFeature.WriteMapNullValue));
        if (StringUtils.isEmpty(jsonStr)) {
            return null;
        }
        return JSON.parseObject(jsonStr, typeRef);
    }

    /**
     * @param reqUrl
     * @param reqParams
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> T post(String reqUrl, Object reqParams, Class<T> clazz) throws Exception {
        String jsonStr = postJson(reqUrl, JSON.toJSONString(reqParams, SerializerFeature.WriteMapNullValue));
        if (StringUtils.isEmpty(jsonStr)) {
            return null;
        }
        return JSONObject.parseObject(jsonStr, clazz);
    }

    /**
     * @param reqUrl
     * @param reqParams
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> T post(String reqUrl, Object reqParams, Class<T> clazz, Integer timeout) throws Exception {
        String jsonStr = postJson(reqUrl, JSON.toJSONString(reqParams, SerializerFeature.WriteMapNullValue), timeout);
        if (StringUtils.isEmpty(jsonStr)) {
            return null;
        }
        return JSONObject.parseObject(jsonStr, clazz);
    }

    /**
     * @param reqUrl
     * @param reqParams
     * @return
     * @throws Exception
     */
    public static String post(String reqUrl, Object reqParams) throws Exception {
        return postJson(reqUrl, JSON.toJSONString(reqParams, SerializerFeature.WriteMapNullValue));
    }


    public static String doPostFrom(String url, Map<String, String> params) {
        URL u = null;
        HttpURLConnection con = null;
        // 构建请求参数
        StringBuffer sb = new StringBuffer();
        if (params != null) {
            for (Map.Entry<String, String> e : params.entrySet()) {
                sb.append(e.getKey());
                sb.append("=");
                sb.append(e.getValue());
                sb.append("&");
            }
            sb.substring(0, sb.length() - 1);
        }
        System.out.println("send_url:" + url);
        System.out.println("send_data:" + sb.toString());
        // 尝试发送请求
        try {
            u = new URL(url);
            con = (HttpURLConnection) u.openConnection();
            con.setRequestMethod("POST");
            con.setDoOutput(true);
            con.setDoInput(true);
            con.setUseCaches(false);
            con.setRequestProperty("Content-Type", MediaType.APPLICATION_FORM_URLENCODED);
            OutputStreamWriter osw = new OutputStreamWriter(con.getOutputStream(), "UTF-8");
            osw.write(sb.toString());
            osw.flush();
            osw.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (con != null) {
                con.disconnect();
            }
        }

        // 读取返回内容
        StringBuffer buffer = new StringBuffer();
        try {
            //一定要有返回值，否则无法把请求发送给server端。
            assert con != null;
            BufferedReader br = new BufferedReader(new InputStreamReader(con.getInputStream(), "UTF-8"));
            String temp;
            while ((temp = br.readLine()) != null) {
                buffer.append(temp);
                buffer.append("\n");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return buffer.toString();
    }


    /**
     * @param reqUrl
     * @param paramMaps
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> List<T> post(String reqUrl, Map<String, Object> paramMaps, Class<T> clazz) throws Exception {
        String jsonStr = postJson(reqUrl, JSON.toJSONString(paramMaps, SerializerFeature.WriteMapNullValue));
        if (StringUtils.isEmpty(jsonStr)) {
            return null;
        }
        return JSON.parseArray(jsonStr, clazz);
    }

    /**
     * @param url
     * @param paramMaps
     * @param typeRefs
     * @param <T>
     * @return
     */
    public static <T> T post(String url, Map<String, Object> paramMaps, TypeReference<T> typeRefs) {
        try {
            String jsonStr = post(url, paramMaps);

            return JSON.parseObject(jsonStr, typeRefs);
        } catch (Exception e) {
            logger.error("post fail,url:" + url + ",Exception" + e.getMessage());
        }
        return null;
    }

    public static <T> T post(String reqUrl, Object reqParams, TypeReference<T> typeRefs) throws Exception {
        String jsonStr = postJson(reqUrl, JSON.toJSONString(reqParams, SerializerFeature.WriteMapNullValue));
        if (StringUtils.isEmpty(jsonStr)) {
            return null;
        }
        return JSON.parseObject(jsonStr, typeRefs);
    }

    /**
     * 基于HttpClient 4.5的通用POST方法
     *
     * @param url       提交的URL
     * @param paramsMap 提交<参数，值>Map
     * @return 提交响应
     */
    public static String post(String url, Map<String, Object> paramsMap) {
        HttpPost httpPost = new HttpPost(url);
        String rspStr = null;
        try {
            httpPost.setConfig(RequestConfig.copy(requestConfig).setSocketTimeout(SOCKET_TIME_OUT).build());

            if (paramsMap != null) {
                List<NameValuePair> paramList = new ArrayList<NameValuePair>();
                for (Map.Entry<String, Object> param : paramsMap.entrySet()) {
                    Object paramValue = param.getValue();
                    if (paramValue == null) {
                        paramValue = "";
                    }
                    NameValuePair pair = new BasicNameValuePair(param.getKey(), paramValue.toString());
                    paramList.add(pair);
                }
                httpPost.setEntity(new UrlEncodedFormEntity(paramList, ENCODING));
            }
            HttpResponse response = httpClient.execute(httpPost);

            // 获取返回数据
            rspStr = getSuccessRetFromResp(response, url, JSON.toJSONString(paramsMap));
        } catch (Exception ex) {
            logger.error("post fail,url:" + url + ",Exception" + ex.getMessage());
        } finally {
            httpPost.releaseConnection();
        }
        return rspStr;
    }


    public static <T> T doPost(String reqUrl, Map<String, Object> paramMaps, Class<T> clazz) {
        String jsonStr = post(reqUrl, paramMaps);
        if (StringUtils.isEmpty(jsonStr)) {
            return null;
        }
        return JSON.parseObject(jsonStr, clazz);
    }

    /**
     * post json 格式数据
     *
     * @param url
     * @param params
     * @return
     * @throws Exception
     */
    public static String postJson(String url, String params) throws Exception {
        return postJson(url, params, null);
    }

    /**
     * @param url
     * @param paramMaps
     * @return
     * @throws Exception
     */
    public static String doPost(String url, Map<String, Object> paramMaps) throws Exception {
        return postJson(url, JSON.toJSONString(paramMaps), null);
    }

    /**
     * post json 格式数据
     *
     * @param url
     * @param params
     * @return
     * @throws Exception
     */
    public static String postJson(String url, String params, Integer timeout) throws Exception {

        //logger.info("POST request url:{} params:{}", url, params);

        //Long start = System.currentTimeMillis();

        HttpPost httpPost = new HttpPost(url);

        try {
            if (timeout != null && timeout > 0) {
                httpPost.setConfig(RequestConfig.copy(requestConfig).setSocketTimeout(timeout).build());
            }

            StringEntity entity = new StringEntity(params, Consts.UTF_8);
            entity.setContentType(MediaType.APPLICATION_JSON);

            httpPost.setEntity(entity);

            HttpResponse response = httpClient.execute(httpPost);

            //logger.info("POST request url:{} time:{}", url, System.currentTimeMillis() - start);

            String retStr = getSuccessRetFromResp(response, url, params);

            return retStr;
        } finally {
            httpPost.releaseConnection();
        }

    }


    public static String postJsonHeader(String url, String params, Map<String, String> headerMaps) throws Exception {
        return postJsonHeader(url, params, headerMaps, null);
    }

    /**
     * 追加header
     *
     * @param url
     * @param params
     * @param timeout
     * @param headerMaps
     * @return
     * @throws Exception
     */
    public static String postJsonHeader(String url, String params, Map<String, String> headerMaps, Integer timeout) throws Exception {
        HttpPost httpPost = new HttpPost(url);
        try {
            if (timeout != null && timeout > 0) {
                httpPost.setConfig(RequestConfig.copy(requestConfig).setSocketTimeout(timeout).build());
            }
            if (headerMaps != null) {
                for (Map.Entry<String, String> entry : headerMaps.entrySet()) {
                    httpPost.addHeader(entry.getKey(), entry.getValue());
                }
            }
            StringEntity entity = new StringEntity(params, Consts.UTF_8);
            entity.setContentType(MediaType.APPLICATION_JSON);
            httpPost.setEntity(entity);
            HttpResponse response = httpClient.execute(httpPost);
            String retStr = getSuccessRetFromResp(response, url, params);
            return retStr;
        } finally {
            httpPost.releaseConnection();
        }
    }

    /**
     * post json 格式数据
     *
     * @param url
     * @param obj
     * @return
     * @throws Exception
     */
    public static String postJson(String url, Object obj) throws Exception {
        String params = JSON.toJSONString(obj, SerializerFeature.WriteMapNullValue);
        return postJson(url, params);
    }

    private static String getSuccessRetFromResp(HttpResponse response, String url, String params) throws Exception {
        String retStr;
        // 检验状态码，如果成功接收数据
        int code = response.getStatusLine().getStatusCode();

        if (code == 200) {
            retStr = EntityUtils.toString(response.getEntity(), Consts.UTF_8);
        } else {
            throw new RuntimeException(String.format("Http request error:%s, url:%s, params:%s", response, url,
                    StringUtils.isNotBlank(params) ? (params.length() > 1000 ? params.substring(0, 1000) : params) : null));
        }

        //logger.info("Http request url:{} params={} retStr:{}. ", url, params, retStr);
        return retStr;
    }


    private static List<NameValuePair> initParams(Map<String, String> paramsMap) {
        List<NameValuePair> params = new ArrayList<>();
        if (paramsMap == null)
            return params;
        Iterator<String> iterator = paramsMap.keySet().iterator();


        while (iterator.hasNext()) {
            String key = iterator.next();
            params.add(new BasicNameValuePair(key, paramsMap.get(key)));
        }
        return params;
    }


    private static List<NameValuePair> getParams(Map<String, Object> paramMaps) {

        List<NameValuePair> params = new ArrayList<>();
        if (paramMaps == null)
            return params;
        Iterator<String> iterator = paramMaps.keySet().iterator();


        while (iterator.hasNext()) {
            String key = iterator.next();
            Object value = paramMaps.get(key);
            params.add(new BasicNameValuePair(key, value == null ? "" : value.toString()));
        }
        return params;
    }

    public static String get(String url) {
        // Get请求
        HttpGet httpGet = new HttpGet(url);
        try {
            httpGet.setConfig(RequestConfig.copy(requestConfig).setSocketTimeout(CONN_TIME_OUT).build());
            // 设置参数
            httpGet.setURI(new URI(httpGet.getURI().toString()));
            // 发送请求
            HttpResponse response = httpClient.execute(httpGet);
            //logger.info("GET request url:{} params:{} response:{} time:{}", url, paramsMap, response, System.currentTimeMillis() - start);
            // 获取返回数据
            String retStr = getSuccessRetFromResp(response, url, null);

            return retStr;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            httpGet.releaseConnection();
        }
        return null;
    }

    /**
     * @param url
     * @param contentType
     * @param reqObject
     * @param headerMaps
     */
    public static void doPostAzure(String url, String contentType, Object reqObject, Map<String, String> headerMaps) {
        HttpPost httpPost = new HttpPost(url);
        String jsonStr = JSON.toJSONString(reqObject, SerializerFeature.DisableCircularReferenceDetect);
        try {
            /*if (timeout != null && timeout > 0) {
                httpPost.setConfig(RequestConfig.copy(requestConfig).setSocketTimeout(timeout).build());
            }*/
            if (headerMaps != null) {
                for (Map.Entry<String, String> entry : headerMaps.entrySet()) {
                    httpPost.addHeader(entry.getKey(), entry.getValue());
                }
            }
            StringEntity entity = new StringEntity(jsonStr, Consts.UTF_8);
            if (StringUtils.isBlank(contentType)) {
                contentType = MediaType.APPLICATION_JSON;
            }
            entity.setContentType(contentType);
            httpPost.setEntity(entity);

            HttpResponse response = httpClient.execute(httpPost);
            String retStr = getSuccessRetFromResp(response, url, jsonStr);

            if (StringUtils.isBlank(retStr)) {
                return;
            }
            if (!StringUtils.containsIgnoreCase(retStr, ":200,")) {
                logger.error("doPostAzure_ReqUrl：{}, reqParams：{}, RspResult：{}", url, jsonStr, retStr);
            }
        } catch (Exception ex) {
            logger.error("doPostAzure, ReqUrl：{}, reqParams：{}, Error：{}", url, jsonStr, ex);
        } finally {
            httpPost.releaseConnection();
        }
    }

    /**
     * @param url
     * @param queryStr
     * @param headerMaps
     */
    public static void doGetAzure(String url, String queryStr, Map<String, String> headerMaps) {
        //Long start = System.currentTimeMillis();
       /* String reqParams = JSON.toJSONString(reqObject);
        List<NameValuePair> pairMaps = Lists.newArrayList();
        Map<String, Object> paramsMaps = MapHelper.toHashMap(reqObject);
        if (paramsMaps != null){
            for (Map.Entry<String, Object> entry: paramsMaps.entrySet()) {
                Object paramValue = entry.getValue();
                if (paramValue == null){
                    continue;
                }
                pairMaps.add(new BasicNameValuePair(entry.getKey(), paramValue.toString()));
            }
        }*/
        // Get请求
        HttpGet httpGet = new HttpGet(url);
        try {
            if (headerMaps != null) {
                for (Map.Entry<String, String> entry : headerMaps.entrySet()) {
                    httpGet.addHeader(entry.getKey(), entry.getValue());
                }
            }
            // 设置参数
            /*String str = EntityUtils.toString(new UrlEncodedFormEntity(pairMaps, Consts.UTF_8));*/

            if (StringUtils.isNotEmpty(queryStr)) {
                httpGet.setURI(new URI(httpGet.getURI().toString() + "?" + queryStr));
            }
            // 发送请求
            HttpResponse response = httpClient.execute(httpGet);
            // 获取返回数据
            String retStr = getSuccessRetFromResp(response, url, queryStr);

            if (StringUtils.isBlank(retStr)) {
                return;
            }
            if (!StringUtils.containsIgnoreCase(retStr, ":200,")) {
                logger.error("doGetAzure_ReqUrl：{}, reqParams：{}, RspResult：{}", url, queryStr, retStr);
            }
        } catch (Exception ex) {
            logger.error("doGetAzure_ReqUrl：{}, reqParams：{}, Error：{}", url, queryStr, ex);
        } finally {
            httpGet.releaseConnection();
        }
    }

    public static String sendPostRequest(String url, Map<String, String> formParams) {
        HttpPost post = new HttpPost(url);
        try {
            // 将表单参数转换为请求体
            List<NameValuePair> params = new ArrayList<>();
            for (Map.Entry<String, String> entry : formParams.entrySet()) {
                params.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
            }
            post.setEntity(new UrlEncodedFormEntity(params, StandardCharsets.UTF_8));

            // 发送POST请求
            HttpResponse response = httpClient.execute(post);
            // 处理响应
            return EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            logger.error("do post form ：{}, reqParams：{}, Error：{}", url, formParams, e.toString());
            return null;
        } finally {
            try {
                post.releaseConnection();
            } catch (Exception e) {
                logger.error("do post form ：{}, reqParams：{}, Error：{}", url, formParams, e.toString());
            }
        }
    }

    public static String postFormData(String url, Map<String, String> paramMaps) {
        CloseableHttpClient client = HttpClients.createDefault();
        String jsonStr = "";
        CloseableHttpResponse response = null;
        try {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setConfig(requestConfig);
            if (paramMaps != null) {
                List<NameValuePair> paramList = Lists.newArrayList();
                for (Map.Entry<String, String> param : paramMaps.entrySet()) {
                    NameValuePair pair = new BasicNameValuePair(param.getKey(), param.getValue());
                    paramList.add(pair);
                }
                httpPost.setEntity(new UrlEncodedFormEntity(paramList, ENCODING));
            }
            response = client.execute(httpPost);

            HttpEntity entity = response.getEntity();
            if (entity != null) {
                jsonStr = EntityUtils.toString(entity);
            }
            if (StringUtils.isEmpty(jsonStr)) {
                return null;
            }
            return jsonStr;
        } catch (IOException e) {
            logger.info("post fail,url:" + url + ",Exception" + e.getMessage());
            //将当前异常抛出，由业务系统处理异常
        } finally {
            //关闭response
            try {
                if (!Objects.isNull(response)) {
                    response.close();
                }
                if (!Objects.isNull(client)) {
                    client.close();
                }
            } catch (Exception e) {
                logger.info("post fail,url:" + url + ",Exception" + e.getMessage());
            }
        }
        return null;
    }

    public static void downloadFile(String fileUrl, OutputStream outputStream) throws IOException {
        HttpGet httpGet = new HttpGet(fileUrl);
        HttpResponse response = null;

        try {
            // 执行 GET 请求
            response = httpClient.execute(httpGet);

            // 检查响应状态码
            if (response.getStatusLine().getStatusCode() != 200) {
                throw new IOException("Failed to download file: " + response.getStatusLine().getStatusCode());
            }

            // 获取响应实体
            HttpEntity entity = response.getEntity();

            if (entity != null) {
                try (BufferedOutputStream bos = new BufferedOutputStream(outputStream)) {
                    // 将实体内容写入输出流
                    entity.writeTo(bos);
                    bos.flush(); // 刷新缓冲区
                } finally {
                    // 确保内容完全消费并释放资源
                    EntityUtils.consume(entity);
                }
            }
        } finally {
            // 关闭响应和 HttpClient
            if (response != null) {
                EntityUtils.consume(response.getEntity());
            }
        }
    }


    public static String httpPostForFrom(String url, List<NameValuePair> list) {
        logger.info("\r\nInterface Url:{\r\n\t" + url + "\r\n,data={}}", list);
        CloseableHttpClient httpClient = HttpClients.createDefault();
        RequestConfig defaultRequestConfig = RequestConfig.custom().setSocketTimeout(timeout * 1000)
                .setConnectTimeout(timeout * 1000).setConnectionRequestTimeout(timeout * 1000).build();
        HttpRequest request = new HttpPost(url);

        CloseableHttpResponse response = null;
        String result = null;
        try {
            UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(list, Charset.defaultCharset());
            HttpPost httpPost = (HttpPost) request;
            httpPost.setConfig(defaultRequestConfig);
            httpPost.setEntity(urlEncodedFormEntity);
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            result = EntityUtils.toString(entity);
            interfaceLog(url, list, result);
            if (response.getStatusLine().getStatusCode() != 200) {
                logger.error("Call interface error : result={}", result);
//                throw new SGSException(ErrorCode.SYSTEM_INTERFACE.INTERFACE_EXCEPTION,
//                        "Call interface error [" + result + "]!");
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("Call interface error!,url={}", url);

//            throw new SGSException(ErrorCode.SYSTEM_INTERFACE.INTERFACE_EXCEPTION,
//                    "Call interface error [" + result + "]!");
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
            }
        }
        return result;
    }

    private static void interfaceLog(String url, Object params, String result) {
        logger.info("\r\nInterface Url:{\r\n\t" + url + "\r\n}");
        logger.info("\r\nInterface Param:{\r\n\t" + JSONObject.toJSONString(params) + "\r\n}");
        logger.info("\r\nInterface Result:{\r\n\t" + result + "\r\n}");
    }


}

