package com.sgs.customerbiz.core.config;

import com.google.common.collect.ImmutableMap;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * TodoList参数配置类
 * 用于控制TodoListService中的参数排除逻辑
 */
@Configuration
@ConfigurationProperties(prefix = "sgs.todolist.param")
@Data
public class TodoListParamConfig {
    
    /**
     * 是否启用TRF提交日期参数排除
     * 默认为true，表示排除这些参数
     */
    private boolean enableTrfSubmissionDate = true;

    /**
     * 系统ID配置项
     */
    private List<RefSystemIdConfigItem> refSystemIdConfigItems = new ArrayList<>();
    
    /**
     * 系统ID配置项缓存Map
     */
    private Map<Integer, RefSystemIdConfigItem> refSystemIdConfigItemMap = ImmutableMap
            .<Integer, RefSystemIdConfigItem>builder()
            .put(-1, RefSystemIdConfigItem.of(-1, "SGS Smart", ""))
            .put(2, RefSystemIdConfigItem.of(2, "SGS Smart", ""))
            .put(4, RefSystemIdConfigItem.of(4, "SEMIR", "CG0000840"))
            .put(5, RefSystemIdConfigItem.of(5, "ANTA", "CG0000580"))
            .put(7, RefSystemIdConfigItem.of(7, "SHEIN", "CG0001085"))
            .put(9, RefSystemIdConfigItem.of(9, "FASTFISH", "CG0000856"))
            .put(10, RefSystemIdConfigItem.of(10, "CAMEL", "CG0000858"))
            .put(11, RefSystemIdConfigItem.of(11, "PEACEBIRD", "CG0001231"))
            .put(12, RefSystemIdConfigItem.of(12, "TIC", ""))
            .put(13, RefSystemIdConfigItem.of(13, "LINING", "CG0000563"))
            .put(14, RefSystemIdConfigItem.of(14, "SHEIN Supplier", "CG0001085"))
            .put(10019, RefSystemIdConfigItem.of(10019, "SEPTWOLVES", "CG0001420"))
            .put(10020, RefSystemIdConfigItem.of(10020, "F21", "CG0000059"))
            .put(10021, RefSystemIdConfigItem.of(10021, "JOANN", "CG0000836"))
            .put(10022, RefSystemIdConfigItem.of(10022, "Walmart", "CG0000647"))
            .put(10023, RefSystemIdConfigItem.of(10023, "TARGET", "CG0000219"))
            .put(10024, RefSystemIdConfigItem.of(10024, "BIG LOTS", "CG0000538"))
            .put(10025, RefSystemIdConfigItem.of(10025, "Dollar Tree", "CG0000896"))
            .put(10026, RefSystemIdConfigItem.of(10026, "VEYER", "CG0000830"))
            .put(10028, RefSystemIdConfigItem.of(10028, "TARGET - inspectorio", "CG0000219"))
            .put(10029, RefSystemIdConfigItem.of(10029, "Walmart CA", "CG0000647"))
            .build();

    public void setRefSystemIdConfigItems(List<RefSystemIdConfigItem> refSystemIdConfigItems) {
        this.refSystemIdConfigItems = refSystemIdConfigItems;
        this.refSystemIdConfigItemMap = refSystemIdConfigItems.stream().collect(Collectors.toMap(RefSystemIdConfigItem::getRefSystemId, Function.identity(),(k1,k2) -> k2));

    }

    @Data
    public static class RefSystemIdConfigItem {
        private int refSystemId;
        private String label;
        private String customerGroupCode;

        public static RefSystemIdConfigItem of(int refSystemId, String label, String customerGroupCode) {
            RefSystemIdConfigItem result = new RefSystemIdConfigItem();
            result.setRefSystemId(refSystemId);
            result.setLabel(label);
            result.setCustomerGroupCode(customerGroupCode);
            return result;
        }
    }

} 