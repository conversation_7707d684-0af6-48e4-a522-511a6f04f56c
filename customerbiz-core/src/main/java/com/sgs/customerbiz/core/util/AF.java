package com.sgs.customerbiz.core.util;

import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * Applicative Functor utilities for functional programming
 */
public final class AF {
    private AF() {
        // Prevent instantiation
    }

    // ========== forEach methods ==========

    /**
     * Applicative functor style helper method to perform side effects with one Optional
     */
    public static <A> void forEach1(Consumer<A> consumer,
                                    Optional<A> opt1) {
        opt1.ifPresent(consumer);
    }

    /**
     * Applicative functor style helper method to perform side effects with two Optionals
     */
    public static <A, B> void forEach2(BiConsumer<A, B> consumer,
                                       Optional<A> opt1,
                                       Optional<B> opt2) {
        opt1.ifPresent(a ->
                opt2.ifPresent(b ->
                        consumer.accept(a, b)));
    }

    /**
     * Applicative functor style helper method to perform side effects with three Optionals
     */
    public static <A, B, C> void forEach3(TriConsumer<A, B, C> consumer,
                                          Optional<A> opt1,
                                          Optional<B> opt2,
                                          Optional<C> opt3) {
        opt1.ifPresent(a ->
                opt2.ifPresent(b ->
                        opt3.ifPresent(c ->
                                consumer.accept(a, b, c))));
    }

    /**
     * Applicative functor style helper method to perform side effects with four Optionals
     */
    public static <A, B, C, D> void forEach4(QuadConsumer<A, B, C, D> consumer,
                                             Optional<A> opt1,
                                             Optional<B> opt2,
                                             Optional<C> opt3,
                                             Optional<D> opt4) {
        opt1.ifPresent(a ->
                opt2.ifPresent(b ->
                        opt3.ifPresent(c ->
                                opt4.ifPresent(d ->
                                        consumer.accept(a, b, c, d)))));
    }

    /**
     * Applicative functor style helper method to perform side effects with five Optionals
     */
    public static <A, B, C, D, E> void forEach5(PentaConsumer<A, B, C, D, E> consumer,
                                                Optional<A> opt1,
                                                Optional<B> opt2,
                                                Optional<C> opt3,
                                                Optional<D> opt4,
                                                Optional<E> opt5) {
        opt1.ifPresent(a ->
                opt2.ifPresent(b ->
                        opt3.ifPresent(c ->
                                opt4.ifPresent(d ->
                                        opt5.ifPresent(e ->
                                                consumer.accept(a, b, c, d, e))))));
    }

    /**
     * Applicative functor style helper method to perform side effects with six Optionals
     */
    public static <A, B, C, D, E, F> void forEach6(HexaConsumer<A, B, C, D, E, F> consumer,
                                                   Optional<A> opt1,
                                                   Optional<B> opt2,
                                                   Optional<C> opt3,
                                                   Optional<D> opt4,
                                                   Optional<E> opt5,
                                                   Optional<F> opt6) {
        opt1.ifPresent(a ->
                opt2.ifPresent(b ->
                        opt3.ifPresent(c ->
                                opt4.ifPresent(d ->
                                        opt5.ifPresent(e ->
                                                opt6.ifPresent(f ->
                                                        consumer.accept(a, b, c, d, e, f)))))));
    }

    /**
     * Applicative functor style helper method to perform side effects with seven Optionals
     */
    public static <A, B, C, D, E, F, G> void forEach7(HeptaConsumer<A, B, C, D, E, F, G> consumer,
                                                      Optional<A> opt1,
                                                      Optional<B> opt2,
                                                      Optional<C> opt3,
                                                      Optional<D> opt4,
                                                      Optional<E> opt5,
                                                      Optional<F> opt6,
                                                      Optional<G> opt7) {
        opt1.ifPresent(a ->
                opt2.ifPresent(b ->
                        opt3.ifPresent(c ->
                                opt4.ifPresent(d ->
                                        opt5.ifPresent(e ->
                                                opt6.ifPresent(f ->
                                                        opt7.ifPresent(g ->
                                                                consumer.accept(a, b, c, d, e, f, g))))))));
    }

    /**
     * Applicative functor style helper method to perform side effects with eight Optionals
     */
    public static <A, B, C, D, E, F, G, H> void forEach8(OctaConsumer<A, B, C, D, E, F, G, H> consumer,
                                                         Optional<A> opt1,
                                                         Optional<B> opt2,
                                                         Optional<C> opt3,
                                                         Optional<D> opt4,
                                                         Optional<E> opt5,
                                                         Optional<F> opt6,
                                                         Optional<G> opt7,
                                                         Optional<H> opt8) {
        opt1.ifPresent(a ->
                opt2.ifPresent(b ->
                        opt3.ifPresent(c ->
                                opt4.ifPresent(d ->
                                        opt5.ifPresent(e ->
                                                opt6.ifPresent(f ->
                                                        opt7.ifPresent(g ->
                                                                opt8.ifPresent(h ->
                                                                        consumer.accept(a, b, c, d, e, f, g, h)))))))));
    }

    // ========== ap methods ==========

    /**
     * Applicative functor style helper method to safely apply a function to one Optional
     */
    public static <A, R> Optional<R> ap1(Function<A, Optional<R>> fn,
                                         Optional<A> opt1) {
        return opt1.flatMap(fn);
    }

    /**
     * Applicative functor style helper method to safely combine two Optionals and apply a function
     */
    public static <A, B, R> Optional<R> ap2(BiFunction<A, B, Optional<R>> fn,
                                            Optional<A> opt1,
                                            Optional<B> opt2) {
        return opt1.flatMap(a ->
                opt2.flatMap(b ->
                        fn.apply(a, b)));
    }

    /**
     * Applicative functor style helper method to safely combine three Optionals and apply a function
     */
    public static <A, B, C, R> Optional<R> ap3(TriFunction<A, B, C, Optional<R>> fn,
                                               Optional<A> opt1,
                                               Optional<B> opt2,
                                               Optional<C> opt3) {
        return opt1.flatMap(a ->
                opt2.flatMap(b ->
                        opt3.flatMap(c ->
                                fn.apply(a, b, c))));
    }

    /**
     * Applicative functor style helper method to safely combine four Optionals and apply a function
     */
    public static <A, B, C, D, R> Optional<R> ap4(QuadFunction<A, B, C, D, Optional<R>> fn,
                                                  Optional<A> opt1,
                                                  Optional<B> opt2,
                                                  Optional<C> opt3,
                                                  Optional<D> opt4) {
        return opt1.flatMap(a ->
                opt2.flatMap(b ->
                        opt3.flatMap(c ->
                                opt4.flatMap(d ->
                                        fn.apply(a, b, c, d)))));
    }

    /**
     * Applicative functor style helper method to safely combine five Optionals and apply a function
     */
    public static <A, B, C, D, E, R> Optional<R> ap5(PentaFunction<A, B, C, D, E, Optional<R>> fn,
                                                     Optional<A> opt1,
                                                     Optional<B> opt2,
                                                     Optional<C> opt3,
                                                     Optional<D> opt4,
                                                     Optional<E> opt5) {
        return opt1.flatMap(a ->
                opt2.flatMap(b ->
                        opt3.flatMap(c ->
                                opt4.flatMap(d ->
                                        opt5.flatMap(e ->
                                                fn.apply(a, b, c, d, e))))));
    }

    /**
     * Applicative functor style helper method to safely combine six Optionals and apply a function
     */
    public static <A, B, C, D, E, F, R> Optional<R> ap6(HexaFunction<A, B, C, D, E, F, Optional<R>> fn,
                                                        Optional<A> opt1,
                                                        Optional<B> opt2,
                                                        Optional<C> opt3,
                                                        Optional<D> opt4,
                                                        Optional<E> opt5,
                                                        Optional<F> opt6) {
        return opt1.flatMap(a ->
                opt2.flatMap(b ->
                        opt3.flatMap(c ->
                                opt4.flatMap(d ->
                                        opt5.flatMap(e ->
                                                opt6.flatMap(f ->
                                                        fn.apply(a, b, c, d, e, f)))))));
    }

    /**
     * Applicative functor style helper method to safely combine seven Optionals and apply a function
     */
    public static <A, B, C, D, E, F, G, R> Optional<R> ap7(HeptaFunction<A, B, C, D, E, F, G, Optional<R>> fn,
                                                           Optional<A> opt1,
                                                           Optional<B> opt2,
                                                           Optional<C> opt3,
                                                           Optional<D> opt4,
                                                           Optional<E> opt5,
                                                           Optional<F> opt6,
                                                           Optional<G> opt7) {
        return opt1.flatMap(a ->
                opt2.flatMap(b ->
                        opt3.flatMap(c ->
                                opt4.flatMap(d ->
                                        opt5.flatMap(e ->
                                                opt6.flatMap(f ->
                                                        opt7.flatMap(g ->
                                                                fn.apply(a, b, c, d, e, f, g))))))));
    }

    /**
     * Applicative functor style helper method to safely combine eight Optionals and apply a function
     */
    public static <A, B, C, D, E, F, G, H, R> Optional<R> ap8(OctaFunction<A, B, C, D, E, F, G, H, Optional<R>> fn,
                                                              Optional<A> opt1,
                                                              Optional<B> opt2,
                                                              Optional<C> opt3,
                                                              Optional<D> opt4,
                                                              Optional<E> opt5,
                                                              Optional<F> opt6,
                                                              Optional<G> opt7,
                                                              Optional<H> opt8) {
        return opt1.flatMap(a ->
                opt2.flatMap(b ->
                        opt3.flatMap(c ->
                                opt4.flatMap(d ->
                                        opt5.flatMap(e ->
                                                opt6.flatMap(f ->
                                                        opt7.flatMap(g ->
                                                                opt8.flatMap(h ->
                                                                        fn.apply(a, b, c, d, e, f, g, h)))))))));
    }

    // ========== map methods (using lifting approach) ==========

    /**
     * Applicative functor style helper method to safely apply a function to one Optional
     * The function returns R directly (not wrapped in Optional)
     */
    public static <A, R> Optional<R> map1(Function<A, R> fn,
                                          Optional<A> opt1) {
        return ap1(lift1(fn), opt1);
    }

    /**
     * Applicative functor style helper method to safely combine two Optionals and apply a function
     * The function returns R directly (not wrapped in Optional)
     */
    public static <A, B, R> Optional<R> map2(BiFunction<A, B, R> fn,
                                             Optional<A> opt1,
                                             Optional<B> opt2) {
        return ap2(lift2(fn), opt1, opt2);
    }

    /**
     * Applicative functor style helper method to safely combine three Optionals and apply a function
     * The function returns R directly (not wrapped in Optional)
     */
    public static <A, B, C, R> Optional<R> map3(TriFunction<A, B, C, R> fn,
                                                Optional<A> opt1,
                                                Optional<B> opt2,
                                                Optional<C> opt3) {
        return ap3(lift3(fn), opt1, opt2, opt3);
    }

    /**
     * Applicative functor style helper method to safely combine four Optionals and apply a function
     * The function returns R directly (not wrapped in Optional)
     */
    public static <A, B, C, D, R> Optional<R> map4(QuadFunction<A, B, C, D, R> fn,
                                                   Optional<A> opt1,
                                                   Optional<B> opt2,
                                                   Optional<C> opt3,
                                                   Optional<D> opt4) {
        return ap4(lift4(fn), opt1, opt2, opt3, opt4);
    }

    /**
     * Applicative functor style helper method to safely combine five Optionals and apply a function
     * The function returns R directly (not wrapped in Optional)
     */
    public static <A, B, C, D, E, R> Optional<R> map5(PentaFunction<A, B, C, D, E, R> fn,
                                                      Optional<A> opt1,
                                                      Optional<B> opt2,
                                                      Optional<C> opt3,
                                                      Optional<D> opt4,
                                                      Optional<E> opt5) {
        return ap5(lift5(fn), opt1, opt2, opt3, opt4, opt5);
    }

    /**
     * Applicative functor style helper method to safely combine six Optionals and apply a function
     * The function returns R directly (not wrapped in Optional)
     */
    public static <A, B, C, D, E, F, R> Optional<R> map6(HexaFunction<A, B, C, D, E, F, R> fn,
                                                         Optional<A> opt1,
                                                         Optional<B> opt2,
                                                         Optional<C> opt3,
                                                         Optional<D> opt4,
                                                         Optional<E> opt5,
                                                         Optional<F> opt6) {
        return ap6(lift6(fn), opt1, opt2, opt3, opt4, opt5, opt6);
    }

    /**
     * Applicative functor style helper method to safely combine seven Optionals and apply a function
     * The function returns R directly (not wrapped in Optional)
     */
    public static <A, B, C, D, E, F, G, R> Optional<R> map7(HeptaFunction<A, B, C, D, E, F, G, R> fn,
                                                            Optional<A> opt1,
                                                            Optional<B> opt2,
                                                            Optional<C> opt3,
                                                            Optional<D> opt4,
                                                            Optional<E> opt5,
                                                            Optional<F> opt6,
                                                            Optional<G> opt7) {
        return ap7(lift7(fn), opt1, opt2, opt3, opt4, opt5, opt6, opt7);
    }

    /**
     * Applicative functor style helper method to safely combine eight Optionals and apply a function
     * The function returns R directly (not wrapped in Optional)
     */
    public static <A, B, C, D, E, F, G, H, R> Optional<R> map8(OctaFunction<A, B, C, D, E, F, G, H, R> fn,
                                                               Optional<A> opt1,
                                                               Optional<B> opt2,
                                                               Optional<C> opt3,
                                                               Optional<D> opt4,
                                                               Optional<E> opt5,
                                                               Optional<F> opt6,
                                                               Optional<G> opt7,
                                                               Optional<H> opt8) {
        return ap8(lift8(fn), opt1, opt2, opt3, opt4, opt5, opt6, opt7, opt8);
    }

    // ========== lift methods ==========

    /**
     * Lifts a Function<A, R> to Function<A, Optional<R>>
     */
    public static <A, R> Function<A, Optional<R>> lift1(Function<A, R> fn) {
        return a -> Optional.of(fn.apply(a));
    }

    /**
     * Lifts a BiFunction<A, B, R> to BiFunction<A, B, Optional<R>>
     */
    public static <A, B, R> BiFunction<A, B, Optional<R>> lift2(BiFunction<A, B, R> fn) {
        return (a, b) -> Optional.of(fn.apply(a, b));
    }

    /**
     * Lifts a TriFunction<A, B, C, R> to TriFunction<A, B, C, Optional<R>>
     */
    public static <A, B, C, R> TriFunction<A, B, C, Optional<R>> lift3(TriFunction<A, B, C, R> fn) {
        return (a, b, c) -> Optional.of(fn.apply(a, b, c));
    }

    /**
     * Lifts a QuadFunction<A, B, C, D, R> to QuadFunction<A, B, C, D, Optional<R>>
     */
    public static <A, B, C, D, R> QuadFunction<A, B, C, D, Optional<R>> lift4(QuadFunction<A, B, C, D, R> fn) {
        return (a, b, c, d) -> Optional.of(fn.apply(a, b, c, d));
    }

    /**
     * Lifts a PentaFunction<A, B, C, D, E, R> to PentaFunction<A, B, C, D, E, Optional<R>>
     */
    public static <A, B, C, D, E, R> PentaFunction<A, B, C, D, E, Optional<R>> lift5(PentaFunction<A, B, C, D, E, R> fn) {
        return (a, b, c, d, e) -> Optional.of(fn.apply(a, b, c, d, e));
    }

    /**
     * Lifts a HexaFunction<A, B, C, D, E, F, R> to HexaFunction<A, B, C, D, E, F, Optional<R>>
     */
    public static <A, B, C, D, E, F, R> HexaFunction<A, B, C, D, E, F, Optional<R>> lift6(HexaFunction<A, B, C, D, E, F, R> fn) {
        return (a, b, c, d, e, f) -> Optional.of(fn.apply(a, b, c, d, e, f));
    }

    /**
     * Lifts a HeptaFunction<A, B, C, D, E, F, G, R> to HeptaFunction<A, B, C, D, E, F, G, Optional<R>>
     */
    public static <A, B, C, D, E, F, G, R> HeptaFunction<A, B, C, D, E, F, G, Optional<R>> lift7(HeptaFunction<A, B, C, D, E, F, G, R> fn) {
        return (a, b, c, d, e, f, g) -> Optional.of(fn.apply(a, b, c, d, e, f, g));
    }

    /**
     * Lifts a OctaFunction<A, B, C, D, E, F, G, H, R> to OctaFunction<A, B, C, D, E, F, G, H, Optional<R>>
     */
    public static <A, B, C, D, E, F, G, H, R> OctaFunction<A, B, C, D, E, F, G, H, Optional<R>> lift8(OctaFunction<A, B, C, D, E, F, G, H, R> fn) {
        return (a, b, c, d, e, f, g, h) -> Optional.of(fn.apply(a, b, c, d, e, f, g, h));
    }

    // ========== Functional Interfaces ==========

    /**
     * Functional interface for a consumer that takes two parameters
     */
    @FunctionalInterface
    public interface BiConsumer<A, B> {
        void accept(A a, B b);
    }

    /**
     * Functional interface for a consumer that takes three parameters
     */
    @FunctionalInterface
    public interface TriConsumer<A, B, C> {
        void accept(A a, B b, C c);
    }

    /**
     * Functional interface for a consumer that takes four parameters
     */
    @FunctionalInterface
    public interface QuadConsumer<A, B, C, D> {
        void accept(A a, B b, C c, D d);
    }

    /**
     * Functional interface for a consumer that takes five parameters
     */
    @FunctionalInterface
    public interface PentaConsumer<A, B, C, D, E> {
        void accept(A a, B b, C c, D d, E e);
    }

    /**
     * Functional interface for a consumer that takes six parameters
     */
    @FunctionalInterface
    public interface HexaConsumer<A, B, C, D, E, F> {
        void accept(A a, B b, C c, D d, E e, F f);
    }

    /**
     * Functional interface for a consumer that takes seven parameters
     */
    @FunctionalInterface
    public interface HeptaConsumer<A, B, C, D, E, F, G> {
        void accept(A a, B b, C c, D d, E e, F f, G g);
    }

    /**
     * Functional interface for a consumer that takes eight parameters
     */
    @FunctionalInterface
    public interface OctaConsumer<A, B, C, D, E, F, G, H> {
        void accept(A a, B b, C c, D d, E e, F f, G g, H h);
    }

    /**
     * Functional interface for a function that takes three parameters
     */
    @FunctionalInterface
    public interface TriFunction<A, B, C, R> {
        R apply(A a, B b, C c);
    }

    /**
     * Functional interface for a function that takes four parameters
     */
    @FunctionalInterface
    public interface QuadFunction<A, B, C, D, R> {
        R apply(A a, B b, C c, D d);
    }

    /**
     * Functional interface for a function that takes five parameters
     */
    @FunctionalInterface
    public interface PentaFunction<A, B, C, D, E, R> {
        R apply(A a, B b, C c, D d, E e);
    }

    /**
     * Functional interface for a function that takes six parameters
     */
    @FunctionalInterface
    public interface HexaFunction<A, B, C, D, E, F, R> {
        R apply(A a, B b, C c, D d, E e, F f);
    }

    /**
     * Functional interface for a function that takes seven parameters
     */
    @FunctionalInterface
    public interface HeptaFunction<A, B, C, D, E, F, G, R> {
        R apply(A a, B b, C c, D d, E e, F f, G g);
    }

    /**
     * Functional interface for a function that takes eight parameters
     */
    @FunctionalInterface
    public interface OctaFunction<A, B, C, D, E, F, G, H, R> {
        R apply(A a, B b, C c, D d, E e, F f, G g, H h);
    }
}