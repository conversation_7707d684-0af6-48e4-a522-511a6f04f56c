package com.sgs.customerbiz.context;

import com.alibaba.fastjson.JSON;
import com.sgs.customerbiz.dbstorages.mybatis.model.CustomerTrfInfoPO;
import com.sgs.customerbiz.domain.domainevent.TrfEvent;
import com.sgs.customerbiz.domain.domainobject.v2.TrfDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.model.trf.dto.TrfNoDTO;
import com.sgs.customerbiz.model.trf.dto.req.OrderToTrfReq;
import lombok.Data;

import java.io.File;
import java.io.FileInputStream;
import java.util.List;

@Data
public class Order2TRFContext extends RequestContext{

    private OrderToTrfReq orderToTrfReq;

    private List<CustomerTrfInfoPO> trfs;
    private TrfDOV2 trfDOV2;
    private TrfStatusControlDO trfStatusControlDO;

    private CustomerTrfInfoPO customerTrfInfoPO;

    private TrfNoDTO trfNoDTO;

    public static void main(String[] args) {
        File file = new File("C:\\Users\\<USER>\\work\\test.txt");
        String requestJson = readFile(file, "UTF-8");
        Order2TRFContext order2TRFContext = JSON.parseObject(requestJson, Order2TRFContext.class);
        System.out.println(order2TRFContext.getTrfDTO().getOrder());
    }
    public static String readFile(File file, String encoding) {
        try {
            byte[] buffer = new byte[(int) file.length()];
            FileInputStream fis = new FileInputStream(file);
            fis.read(buffer);
            fis.close();
            return new String(buffer, encoding);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
