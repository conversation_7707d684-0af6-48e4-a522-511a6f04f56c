package com.sgs.customerbiz.context;

import com.sgs.customerbiz.app.process.ProcessTypeEnum;
import com.sgs.customerbiz.domain.domainevent.ObjectEvent;
import com.sgs.customerbiz.domain.domainevent.TrfActionEvent;
import com.sgs.customerbiz.domain.domainevent.TrfEvent;
import com.sgs.customerbiz.domain.enums.TrfActionEnum;
import com.sgs.customerbiz.model.trf.dto.TrfDTO;
import lombok.Data;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@Data
public class RequestContext {

    private Object requestData;

    private TrfActionEnum trfAction;

    private List<TrfActionEvent> trfActionEventList = new LinkedList<>();
    private List<TrfEvent> trfEventList = new LinkedList<>();
    private TrfDTO trfDTO;

    private String ProductLineCode;
    private String action;
    private String chainName;
    private String RequestId;
    private String eventChainName;
    private String traceabilityId;
    private String router;

}
