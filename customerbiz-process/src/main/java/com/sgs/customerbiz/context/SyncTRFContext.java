package com.sgs.customerbiz.context;

import com.sgs.customerbiz.dbstorages.mybatis.model.TrfInfoPO;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import lombok.Data;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
@Data
public class SyncTRFContext extends RequestContext{

    private TrfSyncReq syncReq;
    private TrfHeaderDTO trfHeaderDTO;
    private TrfFullDTO trfDOParam;
    private TrfStatusControlDO trfStatusControlDO;
    private TrfStatusResult trfStatusResult;
    private boolean isReturn;
    private TrfInfoPO trfInfoPO;
    private boolean hostModel;
}
