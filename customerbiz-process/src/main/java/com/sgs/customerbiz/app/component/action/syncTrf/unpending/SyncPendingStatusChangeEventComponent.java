package com.sgs.customerbiz.app.component.action.syncTrf.unpending;

import com.sgs.customerbiz.app.component.action.syncTrf.BaseSyncTRFActionComponent;
import com.sgs.customerbiz.biz.service.synctrf.cmd.BaseSyncTrfAction;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.domain.domainevent.TrfEvent;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@LiteflowComponent(id = "syncPendingStatusChangeEventComponent", name = "syncPending状态变更事件通知")
public class SyncPendingStatusChangeEventComponent extends BaseSyncTRFActionComponent {

    @Autowired
    protected BaseSyncTrfAction baseSyncTrfAction;
    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        return null;
    }

    @Override
    public void process() throws Exception {
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        sendEvent(syncTRFContext);
    }

    @Override
    public TrfStatusResult doStatusControl(TrfStatusControlDO trfStatusControlDO) {
        return null;
    }

    @Override
    public void doStatusControlCustom(TrfFullDTO trfDOParam) {

    }

    private void sendEvent(SyncTRFContext syncTRFContext) {
        TrfSyncReq syncReq = syncTRFContext.getSyncReq();
        TrfFullDTO trfDOParam = syncTRFContext.getTrfDOParam();
        TrfStatusControlDO trfStatusControlDO = syncTRFContext.getTrfStatusControlDO();
        TrfStatusResult trfStatusResult = syncTRFContext.getTrfStatusResult();
        if (trfStatusResult.pendingFlagChanged()) {
            TrfEvent event = this.newTrfPendingEvent(trfStatusResult.getNewPendingFlag());
            baseSyncTrfAction.fillEvent(event, syncReq, trfDOParam, trfStatusControlDO, trfStatusResult);
            syncTRFContext.getTrfEventList().add(event);
        }
    }

}
