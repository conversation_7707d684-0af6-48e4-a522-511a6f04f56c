package com.sgs.customerbiz.app.component.validate.syncTrf;

import com.sgs.customerbiz.biz.convertor.TrfConvertor;
import com.sgs.customerbiz.biz.service.ruleengine.node.CheckReportExist;
import com.sgs.customerbiz.biz.service.synctrf.validator.CompletedSyncActionValidatorExtPt;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.core.constants.Constants;
import com.sgs.customerbiz.core.util.FieldUtil;
import com.sgs.customerbiz.domain.service.ReportDataService;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.integration.CustomerClient;
import com.sgs.customerbiz.integration.dto.CustomerAndGroupInfoReq;
import com.sgs.customerbiz.integration.dto.CustomerAndGroupInfoRsp;
import com.sgs.customerbiz.integration.dto.QueryExchangeRateReq;
import com.sgs.customerbiz.integration.dto.QueryExchangeRateRsp;
import com.sgs.customerbiz.model.trf.dto.*;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.customerbiz.model.trf.enums.CustomerUsage;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.facade.model.common.CustomResult;
import com.sgs.preorder.facade.model.info.LabInfo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.sgs.customerbiz.core.constants.Constants.SYSTEM_SGSMART;

@Slf4j
@Component
@LiteflowComponent(id = "completedSyncActionValidatorComponent", name = "SyncCompleted参数校验组件")
public class CompletedSyncActionValidatorComponent extends NormalSyncActionValidatorComponent{

    @Autowired
    private ReportDataService reportDataService;

    @Resource
    private ConfigClient configClient;

    @Autowired
    private CustomerClient customerClient;
    @Resource
    private CompletedSyncActionValidatorExtPt completedSyncActionValidatorExtPt;

    @Override
    public void doCoreProcess() throws Exception {
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        completedSyncActionValidatorExtPt.validate(syncTRFContext.getSyncReq());
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        SyncTRFContext syncTRFContext = (SyncTRFContext) requestContext;
        completedSyncActionValidatorExtPt.validate(syncTRFContext.getSyncReq());
        return syncTRFContext;
    }
}
