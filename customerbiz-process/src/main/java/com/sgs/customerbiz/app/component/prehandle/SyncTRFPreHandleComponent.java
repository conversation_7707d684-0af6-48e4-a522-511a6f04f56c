package com.sgs.customerbiz.app.component.prehandle;

import com.sgs.customerbiz.biz.dff.DFFConvertor;
import com.sgs.customerbiz.biz.service.SciTrfBizService;
import com.sgs.customerbiz.biz.utils.ScheduledWhiteList;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.model.trf.dto.TrfSyncHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Slf4j
@Component
@LiteflowComponent(id = "syncTRFPreHandleComponent", name = "syncTRF预处理组件")
public class SyncTRFPreHandleComponent extends BasePreHandleComponent {

    @Autowired
    private SciTrfBizService sciTrfBizService;
    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        SyncTRFContext syncTRFContext =(SyncTRFContext)requestContext;
        TrfSyncReq syncReq = syncTRFContext.getSyncReq();
        preHandle(syncReq);
        return syncTRFContext;
    }

    @Override
    public void process() throws Exception {
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        TrfSyncReq syncReq = syncTRFContext.getSyncReq();
        preHandle(syncReq);
    }

    private void preHandle(TrfSyncReq syncReq) throws Exception {
        sciTrfBizService.syncTrfPreHandle(syncReq);
    }
}
