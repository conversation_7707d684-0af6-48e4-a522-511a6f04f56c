package com.sgs.customerbiz.app.record;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.sgs.customerbiz.app.process.ProcessTypeEnum;
import com.sgs.customerbiz.context.ImportToTRFContext;
import com.sgs.customerbiz.context.Order2TRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.core.log.APILog;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.ProcessFlowInfoExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.ProcessFlowInfoMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.ProcessFlowStepInfoMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.ProcessFlowInfoPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.ProcessFlowStepInfoPO;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.flow.LiteflowResponse;
import com.yomahub.liteflow.flow.element.Node;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class ProcessRecordManager {

    @Resource
    private ProcessFlowInfoMapper processFlowInfoMapper;
    @Resource
    private ProcessFlowStepInfoMapper processFlowStepInfoMapper;
    @Resource
    private ProcessFlowInfoExtMapper processFlowInfoExtMapper;
    
    private @Resource
    TransactionTemplate transactionTemplate;

    public void recordAsync(APILog apiLog) {
        FlowInfo flowInfo =(FlowInfo) apiLog;
        ProcessTypeEnum processAction = flowInfo.getProcessType();
        RequestContext requestContext = flowInfo.getRequestContext();

        String requestId = null;
        String extId = null;
        switch (processAction){
            case IMPORT_TRF:
                ImportToTRFContext importToTRFContext = (ImportToTRFContext) requestContext;
                requestId = importToTRFContext.getTrfImportReq().getRequestId();
                extId = importToTRFContext.getTrfImportReq().getExtId();
                saveProcessFlow(flowInfo, requestId, extId, requestContext);
                break;
            case ORDER_TO_TRF:
                Order2TRFContext order2TRFContext = (Order2TRFContext) requestContext;
                requestId = order2TRFContext.getOrderToTrfReq().getRequestId();
                extId = order2TRFContext.getOrderToTrfReq().getExtId();
                saveProcessFlow(flowInfo, requestId, extId, requestContext);
                break;
            case SYNC_TRF:
                SyncTRFContext syncTRFContext = (SyncTRFContext) requestContext;
                extId = syncTRFContext.getSyncReq().getExtId();
                requestId = syncTRFContext.getSyncReq().getRequestId();
                if(Func.isEmpty(requestId)){
                    requestId = ProductLineContextHolder.retrieveSciRequestContext().getRequestId();
                }
                saveProcessFlow(flowInfo, requestId, extId, requestContext);
                break;
        }
    }

    private void saveProcessFlow(FlowInfo flowInfo, String requestId, String extId, RequestContext requestContext) {

        transactionTemplate.execute(transactionStatus -> {
            saveProcessFlowInfo(flowInfo, requestId, extId, requestContext);
            return null;
        });
    }

    private void saveProcessFlowInfo(FlowInfo flowInfo, String requestId, String extId, RequestContext requestContext) {

        LiteflowResponse response = flowInfo.getResponse();
        ProcessFlow processFlow = buildProcessFlow(response,requestId, extId,requestContext);
        Long flowId = saveProcessFlow(requestContext, processFlow);
        buildProcessFlowStepInfo(response, flowId, processFlow);
        saveProcessFlowStepInfo(processFlow);
    }

    private void saveProcessFlowStepInfo(ProcessFlow processFlow) {
        List<ProcessFlowStepInfoPO> processFlowStepInfoList = new ArrayList<>();
        processFlow.getProcessFlowStepList().forEach(step -> {
            ProcessFlowStepInfoPO processFlowStepInfoPO = new ProcessFlowStepInfoPO();
            BeanUtil.copyProperties(step,processFlowStepInfoPO);
            processFlowStepInfoPO.setCreateTime(new Date());
            processFlowStepInfoList.add(processFlowStepInfoPO);
        });
        processFlowStepInfoMapper.batchInsert(processFlowStepInfoList);
    }

    private void buildProcessFlowStepInfo(LiteflowResponse response, Long flowId, ProcessFlow processFlow) {
        List<ProcessFlowStep> processFlowStepList = new ArrayList<>();
        AtomicInteger sort = new AtomicInteger(1);
        response.getExecuteStepQueue().forEach(step -> {
            ProcessFlowStep processFlowStepInfo = new ProcessFlowStep();
            processFlowStepInfo.setFlowId(flowId);
            processFlowStepInfo.setNodeName(step.getNodeName());
            processFlowStepInfo.setNodeId(step.getNodeId());
            Node node = step.getRefNode();
            String chanId = node.getCurrChainId();
            processFlowStepInfo.setTag(node.getTag());
            int sortNum = sort.getAndIncrement();
            processFlowStepInfo.setSort(sortNum);
            processFlowStepInfo.setChainId(chanId);
            processFlowStepInfo.setClassName(node.getInstance().getClass().getName());
            processFlowStepInfo.setStatus(step.isSuccess()?"SUCCESS":"FAIL");
            processFlowStepInfo.setStepType(step.getStepType().name());
            processFlowStepInfo.setStartTime(step.getStartTime());
            processFlowStepInfo.setEndTime(step.getEndTime());
            processFlowStepInfo.setTimeCost(step.getTimeSpent());
            processFlowStepList.add(processFlowStepInfo);
        });
        processFlow.setProcessFlowStepList(processFlowStepList);
    }

    private @NotNull Long saveProcessFlow(RequestContext requestContext, ProcessFlow processFlow) {
        ProcessFlowInfoPO processFlowPO = new ProcessFlowInfoPO();
        BeanUtil.copyProperties(processFlow,processFlowPO);
        processFlowPO.setContextInfo(JSON.toJSONString(requestContext));
        processFlowPO.setCreateTime(new Date());
        processFlowInfoExtMapper.insertForSelectKey(processFlowPO);
        Long flowId = processFlowPO.getId();
        return flowId;
    }

    private @NotNull ProcessFlow buildProcessFlow(LiteflowResponse response,String requestId,String extId,RequestContext requestContext) {
        String chainId = response.getChainId();
        String status = response.isSuccess()?"SUCCESS":"FAIL";
        Integer stepCount = response.getExecuteStepQueue().size();
        String stepInfoStr = response.getExecuteStepStrWithTime();
        ProcessFlow processFlow = new ProcessFlow();
        if(requestContext.getAction()!=null){
            chainId = chainId+"-"+requestContext.getAction();
        }
        setErrorInfo(response, processFlow);
        processFlow.setTraceabilityId(requestContext.getTraceabilityId());
        processFlow.setRequestId(requestId);
        processFlow.setExtId(extId);
        processFlow.setChainId(chainId);
        processFlow.setStatus(status);
        processFlow.setRequestContext(requestContext);
        processFlow.setStepCount(stepCount);
        processFlow.setStepInfo(stepInfoStr);
        return processFlow;
    }

    private void setErrorInfo(LiteflowResponse response, ProcessFlow processFlow) {
        String errorCode = null;
        String errorMessage = null;
        if(!response.isSuccess()){
            Exception exception = response.getCause();
            if(exception instanceof CustomerBizException){
                CustomerBizException bizException = (CustomerBizException) exception;
                errorCode = bizException.getStdCode();
                errorMessage = bizException.getMessage();
            }else if(exception instanceof BizException){
                BizException bizException = (BizException) exception;
                errorCode = bizException.getErrorCode().getMessage();
                errorMessage = bizException.getMessage();
            }else {
                errorCode = response.getCode();
                errorMessage = response.getMessage();
            }
        }
        processFlow.setErrorMessage(errorMessage);
        processFlow.setErrorCode(errorCode);
    }
}
