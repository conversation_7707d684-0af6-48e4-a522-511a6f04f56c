package com.sgs.customerbiz.app.component.mapping;

import com.sgs.customerbiz.biz.service.SciTrfBizService;
import com.sgs.customerbiz.context.Order2TRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.integration.CustomerClient;
import com.sgs.customerbiz.model.trf.dto.TrfCustomerDTO;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.enums.TrfSourceType;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@LiteflowComponent(id = "refSystemMappingComponent", name = "order2TRF的RefSystemId映射")
public class RefSystemMappingComponent extends BaseMappingComponent{

    @Autowired
    private ConfigClient configClient;
    @Autowired
    private CustomerClient customerClient;
    @Autowired
    private SciTrfBizService sciTrfBizService;
    @Override
    public void process() throws Exception {
        Order2TRFContext requestContext = this.getContextBean(Order2TRFContext.class);
        setRefSystemMapping(requestContext);
    }

    private void setRefSystemMapping(Order2TRFContext order2TRFContext) {
        List<TrfCustomerDTO> customerDTOList = order2TRFContext.getOrderToTrfReq().getCustomerList();
        String productLineCode = order2TRFContext.getOrderToTrfReq().getProductLineCode();;
        String model = TrfSourceType.Order2TRF.getDescription();;
        TrfHeaderDTO headerDTO = order2TRFContext.getOrderToTrfReq().getHeader();;
        // CustomerGroupCode 映射 RefSystemId
        Integer refSystemId = sciTrfBizService.mappingRefSystemId(customerDTOList, productLineCode, model);
        if (Func.isNotEmpty(refSystemId)&& Func.isNotEmpty(headerDTO)) {
            headerDTO.setRefSystemId(refSystemId);
        }
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) {
        Order2TRFContext order2TRFContext = (Order2TRFContext)requestContext;
        setRefSystemMapping(order2TRFContext);
        return order2TRFContext;
    }
}
