package com.sgs.customerbiz.app.component.action.syncTrf.close;

import com.sgs.customerbiz.app.component.action.syncTrf.BaseSyncTRFActionComponent;
import com.sgs.customerbiz.biz.convertor.TrfConvertor;
import com.sgs.customerbiz.biz.service.synctrf.cmd.SyncClosedActionExtPt;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfOrderPO;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfHeaderDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfOrderDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.enums.PendingFlagEnum;
import com.sgs.customerbiz.model.trf.enums.TrfStatusEnum;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
@LiteflowComponent(id = "syncClosedActionComponent", name = "SyncClosed状态控制")
public class SyncClosedActionComponent extends BaseSyncTRFActionComponent {

    @Autowired
    private SyncClosedActionExtPt syncClosedActionExtPt;
    @Override
    public TrfStatusResult doStatusControl(TrfStatusControlDO trfStatusControlDO) {
        return syncClosedActionExtPt.doStatusControl(trfStatusControlDO);
    }

    @Override
    public void doStatusControlCustom(TrfFullDTO trfDOParam) {
        syncClosedActionExtPt.doStatusControlCustom(trfDOParam);
    }
}
