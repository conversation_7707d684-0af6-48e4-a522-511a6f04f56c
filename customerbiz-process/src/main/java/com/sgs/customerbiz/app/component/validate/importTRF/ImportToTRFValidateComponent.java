package com.sgs.customerbiz.app.component.validate.importTRF;

import cn.hutool.core.util.StrUtil;
import com.sgs.customerbiz.app.component.validate.AbstractValidateComponent;
import com.sgs.customerbiz.biz.helper.DfvHelper;
import com.sgs.customerbiz.biz.service.SciTrfBizService;
import com.sgs.customerbiz.context.ImportToTRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.customerbiz.model.trf.enums.TrfImportMode;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.tool.utils.StringUtil;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * Import2TRF验证组件 - Import2TRF_Main流程的第一个组件
 * 
 * <p><b>流程位置</b>：Import2TRF_Main流程的第一个节点，所有导入模式都必须通过此验证</p>
 * 
 * <p><b>核心功能</b>：
 * <ul>
 *   <li>对导入TRF请求进行基础参数验证和业务规则验证</li>
 *   <li>验证refSystemId和importMode等关键参数</li>
 *   <li>使用DFV动态表单验证框架进行参数校验</li>
 *   <li>设置默认导入模式（如果未指定则默认为PULL模式）</li>
 * </ul>
 * </p>
 * 
 * <p><b>关键判断参数</b>：
 * <ul>
 *   <li><code>importReq.getRefSystemId()</code> - 必须不为空，标识客户系统ID</li>
 *   <li><code>importReq.getImportMode()</code> - 如果为空则默认设置为PULL模式</li>
 * </ul>
 * </p>
 * 
 * <p><b>验证流程</b>：
 * <ol>
 *   <li>基本参数验证：验证请求对象和refSystemId不为空</li>
 *   <li>设置默认导入模式：如果importMode为空则设置为PULL</li>
 *   <li>DFV验证：使用动态表单验证模板进行参数校验</li>
 *   <li>验证模板：通用模板(sci-import) + 客户特定模板(sci-import-{refSystemId}-{systemId})</li>
 * </ol>
 * </p>
 * 
 * <p><b>调用的核心方法</b>：
 * <ul>
 *   <li>{@code SciTrfBizService.validateImportTrf(TrfImportReq)} - 主要验证逻辑</li>
 *   <li>{@code DfvHelper.validateParams()} - 动态表单验证</li>
 * </ul>
 * </p>
 * 
 * <p><b>后续流程</b>：验证通过后，流程将进入ImportTRFModelRouterComponent进行模式路由</p>
 * 
 * <AUTHOR> from Import2TRF_Main Process Analysis
 * @see com.sgs.customerbiz.biz.service.SciTrfBizService#validateImportTrf(TrfImportReq)
 * @see com.sgs.customerbiz.app.component.router.ImportTRFModelRouterComponent
 */
@Component
@LiteflowComponent(id = "importToTRFValidateComponent", name = "Import2TRF验证节点")
public class ImportToTRFValidateComponent extends AbstractValidateComponent {
    /** DFV验证模板 - 客户特定模板格式：sci-import-{refSystemId}-{systemId} */
    private static final String VALIDATE_IMPORT_TRF_TPL_CUSTOMER_FORMATTER = "sci-import-{}-{}";
    /** DFV验证模板 - 通用验证模板 */
    private static final String VALIDATE_IMPORT_TRF_TPL_GENERAL = "sci-import";
    /** 动态表单验证助手 - 用于执行DFV模板验证 */
    @Resource
    private DfvHelper dfvHelper;

    /** SciTrf业务服务 - 执行导入TRF的核心验证逻辑 */
    @Resource
    private SciTrfBizService sciTrfBizService;

    @Override
    protected void doPreProcess() throws Exception {

    }

    @Override
    protected void doPostProcess() throws Exception {

    }

    /**
     * 核心验证处理逻辑
     * 
     * <p>执行Import2TRF请求的完整验证流程：</p>
     * <ol>
     *   <li>获取导入请求上下文和参数</li>
     *   <li>调用SciTrfBizService执行验证逻辑</li>
     *   <li>包括基础参数验证、默认模式设置、DFV动态验证</li>
     * </ol>
     * 
     * @throws Exception 当验证失败时抛出异常
     */
    @Override
    public void doCoreProcess() throws Exception {
        ImportToTRFContext importToTRFContext = this.getContextBean(ImportToTRFContext.class);
        TrfImportReq importReq = importToTRFContext.getTrfImportReq();
        Integer refSystemId = sciTrfBizService.validateImportTrf(importReq);
    }


    /**
     * 测试组件方法
     * 
     * <p>提供独立的组件测试能力，执行相同的验证逻辑但不依赖LiteFlow上下文</p>
     * 
     * @param requestContext 请求上下文
     * @return 验证后的上下文对象
     */
    @Override
    public RequestContext testComponent(RequestContext requestContext) {
        ImportToTRFContext  importToTRFContext = (ImportToTRFContext)requestContext;
        TrfImportReq importReq = importToTRFContext.getTrfImportReq();
        Integer refSystemId = sciTrfBizService.validateImportTrf(importReq);
        return importToTRFContext;
    }
}
