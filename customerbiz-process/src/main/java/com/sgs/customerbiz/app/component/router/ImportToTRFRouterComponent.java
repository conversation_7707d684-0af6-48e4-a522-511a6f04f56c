package com.sgs.customerbiz.app.component.router;

import com.alibaba.cola.extension.BizScenario;
import com.sgs.customerbiz.context.ImportToTRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.StringUtil;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.flow.FlowBus;
import com.yomahub.liteflow.flow.element.Node;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * ImportToTRF子路由组件 - PULL模式内部的二级路由决策
 * 
 * <p><b>流程位置</b>：位于SubChain_PullImportTRF子流程的第一个节点</p>
 * 
 * <p><b>核心功能</b>：
 * <ul>
 *   <li>PULL模式内部的二级路由决策</li>
 *   <li>根据客户系统类型选择不同的PULL子模式</li>
 *   <li>支持普通PULL模式和Smart PULL模式</li>
 * </ul>
 * </p>
 * 
 * <p><b>路由决策逻辑</b>：
 * <ul>
 *   <li><b>PULL</b>：路由到PullImport2TRF普通PULL流程</li>
 *   <li><b>PULL_2</b>：路由到SmartImport2TRF流程（SGSmart系统专用）</li>
 *   <li><b>默认</b>：路由到PullImport2TRF普通流程</li>
 * </ul>
 * </p>
 * 
 * <p><b>关键判断参数</b>：
 * <ul>
 *   <li><code>importMode</code> - 导入模式（一般为PULL）</li>
 *   <li><code>refSystemId</code> - 参考系统ID，用于决定具体的子模式</li>
 *   <li><code>scenarioValue</code> - 通过getScenarioValue方法映射获得</li>
 * </ul>
 * </p>
 * 
 * <p><b>路由标签生成规则</b>：
 * <ul>
 *   <li>格式："tag:" + getRouter(scenarioValue, model)</li>
 *   <li>通过scenarioValue和model组合决定最终路由</li>
 *   <li>支持多层次的路由决策</li>
 * </ul>
 * </p>
 * 
 * <p><b>Smart vs 普通PULL模式区别</b>：
 * <ul>
 *   <li><b>普通PULL</b>：通用的拉取模式，适用于大多数客户系统</li>
 *   <li><b>Smart PULL</b>：专为SGSmart系统设计的拉取模式，有特殊的处理逻辑</li>
 * </ul>
 * </p>
 * 
 * <p><b>扩展点机制</b>：
 * <ul>
 *   <li>使用RefSystemIdAdapter.map进行系统ID映射</li>
 *   <li>支持基于refSystemId的灵活路由配置</li>
 * </ul>
 * </p>
 * 
 * <p><b>调用的核心方法</b>：
 * <ul>
 *   <li>{@code getScenarioValue(Integer refSystemId)} - 获取场景值</li>
 *   <li>{@code getRouter(String scenarioValue, String model)} - 生成路由标签</li>
 * </ul>
 * </p>
 * 
 * <AUTHOR> from Import2TRF_Main Process Analysis
 * @see com.sgs.customerbiz.app.component.router.BaseRouterComponent
 */
@Component
@LiteflowComponent(id = "importToTRFRouterComponent", name = "import2TRF路由决策节点")
public class ImportToTRFRouterComponent extends BaseRouterComponent{
    /**
     * 路由切换处理方法
     * 
     * <p>LiteFlow框架调用的路由决策方法，根据客户系统和导入模式返回相应的路由标签</p>
     * 
     * @return 路由标签（如"tag:PULL"或"tag:PULL_2"）
     * @throws Exception 处理异常
     */
    @Override
    public String processSwitch() throws Exception {
        ImportToTRFContext importToTRFContext = this.getContextBean(ImportToTRFContext.class);
        return getRouter(importToTRFContext);
    }

    /**
     * 获取路由标签
     * 
     * <p><b>路由决策流程</b>：
     * <ol>
     *   <li>从上下文获取导入模式</li>
     *   <li>通过refSystemId获取场景值</li>
     *   <li>结合scenarioValue和model生成路由标签</li>
     * </ol>
     * </p>
     * 
     * <p><b>路由规则</b>：
     * <ul>
     *   <li>基本格式："tag:" + getRouter(scenarioValue, model)</li>
     *   <li>支持复杂的多级路由决策</li>
     * </ul>
     * </p>
     * 
     * @param importToTRFContext 导入TRF上下文
     * @return 路由标签字符串
     */
    private @NotNull String getRouter(ImportToTRFContext importToTRFContext) {
        //List<Node> nodes= FlowBus.getNodesByChainId(this.getChainId());
        TrfImportReq trfImportReq = importToTRFContext.getTrfImportReq();
        String model = trfImportReq.getImportMode();
        String scenarioValue = getScenarioValue(trfImportReq.getRefSystemId());
        String router= "tag:"+getRouter(scenarioValue, model);
        return router;
    }

    /**
     * 测试组件方法
     * 
     * <p>提供独立的组件测试能力，执行相同的路由决策逻辑并将路由结果存储在上下文中</p>
     * 
     * @param requestContext 请求上下文
     * @return 处理后的上下文对象
     * @throws Exception 处理异常
     */
    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        ImportToTRFContext importToTRFContext = (ImportToTRFContext)requestContext;
        importToTRFContext.setRouter(getRouter(importToTRFContext));
        return importToTRFContext;
    }
}
