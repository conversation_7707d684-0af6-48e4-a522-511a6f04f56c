package com.sgs.customerbiz.app.component.router;

import com.sgs.customerbiz.context.ImportToTRFContext;
import com.sgs.customerbiz.context.Order2TRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.model.trf.dto.req.OrderToTrfReq;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

@Component
@LiteflowComponent(id = "orderToTRFRouterComponent", name = "order2TRF路由决策节点")
public class OrderToTRFRouterComponent extends BaseRouterComponent{
    @Override
    public String processSwitch() throws Exception {
        Order2TRFContext orderContext = this.getContextBean(Order2TRFContext.class);
        return getRouter(orderContext);
    }

    private @Nullable String getRouter(Order2TRFContext orderContext) {
        OrderToTrfReq orderToTrfReq = orderContext.getOrderToTrfReq();
        String routeId = getScenarioValue(orderToTrfReq.getHeader().getRefSystemId());
        if (Func.isBlank(routeId)) {
            return null;
        }
        return "tag:" + routeId;
    }

    public static void main(String[] args) {

    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        Order2TRFContext orderContext = (Order2TRFContext)requestContext;
        orderContext.setRouter(getRouter(orderContext));
        return orderContext;
    }
}
