package com.sgs.customerbiz.app.component.validate;

import com.sgs.customerbiz.biz.service.ruleengine.RuleExecutor;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@LiteflowComponent(id = "ruleValidateComponent", name = "规则校验组件")
public class RuleValidateComponent extends AbstractValidateComponent{


    @Resource
    private RuleExecutor ruleExecutor;
    @Override
    protected void doPreProcess() throws Exception {

    }

    @Override
    protected void doPostProcess() throws Exception {

    }

    @Override
    public void doCoreProcess() throws Exception {
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        checkRule(syncTRFContext);
    }

    private void checkRule(SyncTRFContext syncTRFContext) {
        TrfFullDTO trfDOParam = syncTRFContext.getTrfDOParam();
        TrfSyncReq syncReq = syncTRFContext.getSyncReq();
        // 业务规则校验
        boolean flag = ruleExecutor.evaluate(trfDOParam, syncReq.getAction());
        if (!flag) {
            ErrorCode errorCode1 = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SYNCACTION, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.PARAMETERERROR);
            throw new CustomerBizException(errorCode1, ResponseCode.FAIL.getCode(), "数据校验异常！");
        }
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        SyncTRFContext syncTRFContext = (SyncTRFContext)requestContext;
        checkRule(syncTRFContext);
        return requestContext;
    }
}
