package com.sgs.customerbiz.app.component.action.syncTrf.pending;

import com.sgs.customerbiz.app.component.action.syncTrf.BaseSyncTRFActionComponent;
import com.sgs.customerbiz.biz.service.synctrf.cmd.SyncPendingActionExtPt;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfOtherDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@LiteflowComponent(id = "syncPendingActionComponent", name = "SyncPending动作")
public class SyncPendingActionComponent extends BaseSyncTRFActionComponent {

    @Autowired
    private SyncPendingActionExtPt syncPendingActionExtPt;
    @Override
    public void doStatusControlCustom(TrfFullDTO trfDOParam) {
        syncPendingActionExtPt.doStatusControlCustom(trfDOParam);
    }
    @Override
    public TrfStatusResult doStatusControl(TrfStatusControlDO trfStatusControlDO) {

      return syncPendingActionExtPt.doStatusControl(trfStatusControlDO);
    }
}
