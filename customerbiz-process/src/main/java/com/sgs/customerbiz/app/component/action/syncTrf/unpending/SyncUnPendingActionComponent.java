package com.sgs.customerbiz.app.component.action.syncTrf.unpending;

import com.sgs.customerbiz.app.component.action.syncTrf.BaseSyncTRFActionComponent;
import com.sgs.customerbiz.biz.service.synctrf.cmd.SyncUnPendingActionExtPt;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@LiteflowComponent(id = "syncUnPendingActionComponent", name = "SyncUnPending")
public class SyncUnPendingActionComponent extends BaseSyncTRFActionComponent {

    @Autowired
    private SyncUnPendingActionExtPt syncUnPendingActionExtPt;
    @Override
    public void doStatusControlCustom(TrfFullDTO trfDOParam) {
        syncUnPendingActionExtPt.doStatusControlCustom(trfDOParam);
    }
    @Override
    public TrfStatusResult doStatusControl(TrfStatusControlDO trfStatusControlDO) {
        return syncUnPendingActionExtPt.doStatusControl(trfStatusControlDO);
    }
}
