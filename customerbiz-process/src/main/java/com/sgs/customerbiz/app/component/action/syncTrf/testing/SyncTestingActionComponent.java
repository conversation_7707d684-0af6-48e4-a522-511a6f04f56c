package com.sgs.customerbiz.app.component.action.syncTrf.testing;

import com.sgs.customerbiz.app.component.action.syncTrf.BaseSyncTRFActionComponent;
import com.sgs.customerbiz.biz.service.synctrf.cmd.SyncTestingActionExtPt;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfOrderDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.domain.enums.TrfActionEnum;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.enums.TrfStatusEnum;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@LiteflowComponent(id = "syncTestingActionComponent", name = "SyncTesting动作")
public class SyncTestingActionComponent extends BaseSyncTRFActionComponent {

    @Autowired
    private SyncTestingActionExtPt syncTestingActionExtPt;
    @Override
    public TrfStatusResult doStatusControl(TrfStatusControlDO trfStatusControlDO) {
        return syncTestingActionExtPt.doStatusControl(trfStatusControlDO);
    }

    @Override
    public void doStatusControlCustom(TrfFullDTO trfDOParam) {
        syncTestingActionExtPt.doStatusControlCustom(trfDOParam);
    }

}
