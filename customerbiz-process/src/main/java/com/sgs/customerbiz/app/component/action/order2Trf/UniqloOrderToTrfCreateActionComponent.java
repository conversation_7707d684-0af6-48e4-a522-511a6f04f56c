package com.sgs.customerbiz.app.component.action.order2Trf;

import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.ErrorAssert;
import com.sgs.customerbiz.model.trf.dto.req.OrderToTrfReq;
import com.sgs.customerbiz.validation.utils.UniqloUtils;
import com.sgs.framework.core.base.ResponseCode;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@LiteflowComponent(id = "unqloOrderToTrfCreateActionComponent", name = "order2TRF UNQLO CreateAction处理器")
public class UniqloOrderToTrfCreateActionComponent extends DefaultOrderToTrfCreateActionComponent{

    @Override
    public void validate(OrderToTrfReq orderToTrfReq) {
        String trfNo = orderToTrfReq.getHeader().getTrfNo();
        //SCI-637 : 1、TrfNo 字母必须大写 ；2、长度必须为22位
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.ORDERTOTRFACTION, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.isTrue(UniqloUtils.isUppercase(trfNo), errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "The trf no letters must be all uppercase character");
        ErrorAssert.isTrue(trfNo.length() == 22, errorCode,ResponseCode.ILLEGAL_ARGUMENT.getCode(), "Trf no must be 22 fields in length");
    }

}
