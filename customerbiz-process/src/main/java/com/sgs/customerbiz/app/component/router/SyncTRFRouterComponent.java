package com.sgs.customerbiz.app.component.router;

import com.alibaba.cola.extension.BizScenario;
import com.sgs.customerbiz.context.Order2TRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@LiteflowComponent(id = "syncTRFRouterComponent", name = "syncTRF路由组件")
public class SyncTRFRouterComponent  extends BaseRouterComponent{
    @Override
    public String processSwitch() throws Exception {

        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        return getRouter(syncTRFContext);
    }

    private @NotNull String getRouter(SyncTRFContext syncTRFContext) {
        TrfSyncReq trfImportReq = syncTRFContext.getSyncReq();
        String action = trfImportReq.getAction();
        return "tag:" + action;
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        SyncTRFContext syncTRFContext = (SyncTRFContext)requestContext;
        syncTRFContext.setRouter(getRouter(syncTRFContext));
        return syncTRFContext;
    }
}
