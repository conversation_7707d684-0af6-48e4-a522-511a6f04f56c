package com.sgs.customerbiz.app.process;

public enum ProcessTypeEnum {
    /**
     * importTRF TRF导入
     * orderToTRF 订单转TRF
     * syncTRF  TRF同步
     */
    IMPORT_TRF("importTRF", "TRF导入"),
    ORDER_TO_TRF("orderToTRF", "订单转TRF"),
    CAN_SYNC_TRF("canSyncTrf", "TRF同步前置校验"),
    SYNC_TRF("syncTRF", "TRF同步"),
    CUSTOMER_TRF("customerTrf", "客户TRF查询");

    private String code;
    private String desc;

    ProcessTypeEnum(String code, String desc)
    {
        this.code = code;
        this.desc = desc;
    }

    public String getCode()
    {
        return code;
    }

    public String getDesc()
    {
        return desc;
    }
}
