package com.sgs.customerbiz.app.component.action.importToTrf.customer;

import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.framework.core.exception.Assert;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.stereotype.Component;

/**
 * PUSH模式获取客户TRF组件 - SubChain_PushImport2TRF流程的第一个组件
 * 
 * <p><b>流程位置</b>：位于SubChain_PushImport2TRF子流程的第一个节点</p>
 * 
 * <p><b>核心功能</b>：
 * <ul>
 *   <li>PUSH模式下验证并获取客户TRF数据</li>
 *   <li>验证customerTrf字段不能为空</li>
 *   <li>直接返回请求中的客户TRF数据</li>
 * </ul>
 * </p>
 * 
 * <p><b>PUSH模式特点</b>：
 * <ul>
 *   <li>客户系统主动推送TRF数据</li>
 *   <li>数据在请求中直接提供，不需要从外部系统获取</li>
 *   <li>适用于客户系统能够主动发起请求的场景</li>
 * </ul>
 * </p>
 * 
 * <p><b>关键验证参数</b>：
 * <ul>
 *   <li><code>importReq.getCustomerTrf()</code> - 客户TRF数据，必须不为空</li>
 * </ul>
 * </p>
 * 
 * <p><b>与PULL模式的区别</b>：
 * <ul>
 *   <li><b>PUSH</b>：直接使用请求中的数据，无需外部调用</li>
 *   <li><b>PULL</b>：需要从客户系统主动拉取数据</li>
 * </ul>
 * </p>
 * 
 * <p><b>后续流程</b>：获取数据后，流程将进入importToTRFConvertComponent进行数据转换</p>
 * 
 * <AUTHOR> from Import2TRF_Main Process Analysis
 * @see GetCustomerTRFComponent
 * @see com.sgs.customerbiz.app.component.convert.ImportToTRFConvertComponent
 */
@Component
@LiteflowComponent(id = "pushImportGetCustomerActionComponent", name = "push模式获取客户的TRF信息")
public class PushImportGetCustomerActionComponent extends GetCustomerTRFComponent {

    /**
     * PUSH模式获取客户TRF数据
     * 
     * <p><b>核心逻辑</b>：
     * <ol>
     *   <li>验证customerTrf字段不能为空</li>
     *   <li>直接返回请求中的customerTrf数据</li>
     * </ol>
     * </p>
     * 
     * <p><b>PUSH模式优势</b>：
     * <ul>
     *   <li>无需外部API调用，响应速度快</li>
     *   <li>数据实时性高，由客户系统主动推送</li>
     *   <li>减少系统间耦合度</li>
     * </ul>
     * </p>
     * 
     * @param importReq 导入TRF请求，必须包含customerTrf数据
     * @return 客户TRF数据字符串（JSON格式）
     * @throws IllegalArgumentException 当customerTrf为空时抛出
     */
    @Override
    public String getCustomerTrf(TrfImportReq importReq) {
        Assert.notBlank(importReq.getCustomerTrf(), "customerTrf cannot be null in PUSH Mode !");

        return importReq.getCustomerTrf();
    }
}
