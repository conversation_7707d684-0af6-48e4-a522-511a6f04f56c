package com.sgs.customerbiz.app.component.action.syncTrf.completed;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.app.component.action.syncTrf.BaseSyncTRFActionComponent;
import com.sgs.customerbiz.biz.convertor.TrfConvertor;
import com.sgs.customerbiz.biz.exception.RepeatSyncCompletedException;
import com.sgs.customerbiz.biz.service.synctrf.SyncTrfContextHolder;
import com.sgs.customerbiz.biz.service.synctrf.cmd.SyncCompletedActionExtPt;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfInfoPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfOrderPO;
import com.sgs.customerbiz.domain.domainevent.TrfActionEvent;
import com.sgs.customerbiz.domain.domainevent.actionevent.TrfSupplementEvent;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfOrderDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfReportDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.domain.enums.TrfEventTriggerBy;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.TrfOrderDTO;
import com.sgs.customerbiz.model.trf.enums.PendingFlagEnum;
import com.sgs.customerbiz.model.trf.enums.TrfStatusEnum;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
@LiteflowComponent(id = "syncCompletedActionComponent", name = "SyncCompleted动作")
public class SyncCompletedActionComponent extends BaseSyncTRFActionComponent {

    @Autowired
    private SyncCompletedActionExtPt syncCompletedActionExtPt;
    @Override
    public TrfStatusResult doStatusControl(TrfStatusControlDO trfStatusControlDO) {
        return syncCompletedActionExtPt.doStatusControl(trfStatusControlDO);
    }

    @Override
    public void doStatusControlCustom(TrfFullDTO trfDOParam) {
        syncCompletedActionExtPt.doStatusControlCustom(trfDOParam);
    }

}
