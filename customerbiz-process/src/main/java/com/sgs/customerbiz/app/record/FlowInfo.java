package com.sgs.customerbiz.app.record;

import com.sgs.customerbiz.app.process.ProcessTypeEnum;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.core.log.APILog;
import com.yomahub.liteflow.flow.LiteflowResponse;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class FlowInfo implements APILog {
    private ProcessTypeEnum processType;
    private LiteflowResponse response;
    private RequestContext requestContext;

    @Override
    public String getApiLogName() {
        return "liteflowProcessRecord";
    }
}
