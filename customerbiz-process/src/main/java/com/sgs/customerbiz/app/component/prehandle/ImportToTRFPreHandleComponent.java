package com.sgs.customerbiz.app.component.prehandle;


import com.sgs.customerbiz.biz.service.importtrf.cmd.DefaultImportTrfActionExtPt;
import com.sgs.customerbiz.context.ImportToTRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.integration.FrameWorkClient;
import com.sgs.customerbiz.model.trf.dto.TrfDTO;
import com.sgs.customerbiz.model.trf.dto.TrfLabDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ObjectUtil;
import com.sgs.framework.tool.utils.StringUtil;
import com.sgs.preorder.facade.model.info.LabInfo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * ImportToTRF预处理组件 - 对转换后的TRF数据进行预处理，主要是实验室信息初始化
 * 
 * <p><b>流程位置</b>：位于各子流程中，在importToTRFConvertComponent之后</p>
 * 
 * <p><b>核心功能</b>：
 * <ul>
 *   <li>初始化TRF实验室信息</li>
 *   <li>处理实验室代码的优先级规则</li>
 *   <li>确保实验室信息的完整性和准确性</li>
 * </ul>
 * </p>
 * 
 * <p><b>处理逻辑</b>：
 * <ol>
 *   <li>从上下文获取TrfDTO和TrfImportReq</li>
 *   <li>调用DefaultImportTrfActionExtPt.initTrfLab初始化实验室信息</li>
 * </ol>
 * </p>
 * 
 * <p><b>实验室代码优先级规则</b>：
 * <ul>
 *   <li>如果importReq中有labCode，使用请求中的labCode</li>
 *   <li>如果importReq中没有labCode，使用默认的labCode</li>
 * </ul>
 * </p>
 * 
 * <p><b>关键参数</b>：
 * <ul>
 *   <li><code>TrfDTO trfDTO</code> - 转换后的TRF数据对象</li>
 *   <li><code>TrfImportReq importReq</code> - 导入请求参数</li>
 *   <li><code>importReq.getLabCode()</code> - 请求中的实验室代码</li>
 * </ul>
 * </p>
 * 
 * <p><b>调用的核心方法</b>：
 * <ul>
 *   <li>{@code DefaultImportTrfActionExtPt.initTrfLab(TrfDTO, TrfImportReq)} - 初始化实验室信息</li>
 * </ul>
 * </p>
 * 
 * <p><b>后续流程</b>：预处理完成后，流程将进入testLineMappingComponent进行测试项映射</p>
 * 
 * <AUTHOR> from Import2TRF_Main Process Analysis
 * @see com.sgs.customerbiz.biz.service.importtrf.cmd.DefaultImportTrfActionExtPt#initTrfLab(TrfDTO, TrfImportReq)
 * @see com.sgs.customerbiz.app.component.mapping.TestLineMappingComponent
 */
@Slf4j
@Component
@LiteflowComponent(id = "importToTRFPreHandleComponent", name = "importToTRF预处理组件")
public class ImportToTRFPreHandleComponent extends BasePreHandleComponent {

    /** 框架客户端 - 用于获取实验室信息 */
    @Resource
    protected FrameWorkClient frameWorkClient;
    /** 默认导入TRF操作扩展点 - 执行实验室初始化逻辑 */
    @Resource
    protected DefaultImportTrfActionExtPt defaultImportTrfActionExtPt;
    /**
     * 组件处理入口
     * 
     * <p>从上下文获取TRF数据和导入请求，调用扩展点初始化实验室信息</p>
     * 
     * @throws Exception 处理过程中的异常
     */
    @Override
    public void process() throws Exception {

        ImportToTRFContext importToTRFContext = this.getContextBean(ImportToTRFContext.class);
        TrfImportReq importReq = importToTRFContext.getTrfImportReq();
        TrfDTO trfDTO = importToTRFContext.getTrfDTO();
        defaultImportTrfActionExtPt.initTrfLab(trfDTO, importReq);
    }

    /**
     * 测试组件方法
     * 
     * <p>提供独立的组件测试能力，执行相同的预处理逻辑</p>
     * 
     * <p><b>执行步骤</b>：
     * <ol>
     *   <li>从上下文获取必要参数</li>
     *   <li>调用initTrfLab初始化实验室信息</li>
     * </ol>
     * </p>
     * 
     * @param requestContext 请求上下文
     * @return 处理后的上下文对象
     */
    @Override
    public RequestContext testComponent(RequestContext requestContext) {
        ImportToTRFContext importToTRFContext = (ImportToTRFContext) requestContext;
        TrfImportReq importReq = importToTRFContext.getTrfImportReq();
        TrfDTO trfDTO = importToTRFContext.getTrfDTO();
        defaultImportTrfActionExtPt.initTrfLab(trfDTO, importReq);
        return importToTRFContext;
    }
}
