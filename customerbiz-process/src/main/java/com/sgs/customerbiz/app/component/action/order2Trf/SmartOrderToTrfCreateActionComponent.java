package com.sgs.customerbiz.app.component.action.order2Trf;

import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.app.component.action.BaseActionComponent;
import com.sgs.customerbiz.biz.convertor.TrfConvertor;
import com.sgs.customerbiz.biz.convertor.TrfNoConvertor;
import com.sgs.customerbiz.biz.dto.TaskInfoDTO;
import com.sgs.customerbiz.biz.event.RefSystemIdAdapter;
import com.sgs.customerbiz.biz.service.SgsMartService;
import com.sgs.customerbiz.biz.service.order2trf.cmd.SgsMartOrderToTrfActionExtPt;
import com.sgs.customerbiz.biz.service.task.TaskService;
import com.sgs.customerbiz.context.Order2TRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.core.util.DateUtils;
import com.sgs.customerbiz.dbstorages.mybatis.model.CustomerTrfInfoPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfOrderPO;
import com.sgs.customerbiz.domain.domainobject.v2.TrfDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfHeaderDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfOrderDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.domain.domainservice.TrfOrderDomainService;
import com.sgs.customerbiz.domain.enums.TaskStatusEnum;
import com.sgs.customerbiz.domain.enums.TrfActionEnum;
import com.sgs.customerbiz.domain.enums.TrfOrderRelationshipRuleEnum;
import com.sgs.customerbiz.model.trf.dto.TrfDTO;
import com.sgs.customerbiz.model.trf.dto.TrfNoDTO;
import com.sgs.customerbiz.model.trf.dto.TrfOrderDTO;
import com.sgs.customerbiz.model.trf.dto.req.OrderToTrfReq;
import com.sgs.customerbiz.model.trf.enums.TrfSourceType;
import com.sgs.customerbiz.model.trf.enums.TrfStatusEnum;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.facade.model.enums.ActiveIndicatorEnum;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.sgs.customerbiz.biz.utils.TrfEventCodeMapping.TRF_UNBIND_EVENT;


@Slf4j
@Component
@LiteflowComponent(id = "smartOrderToTrfCreateActionComponent", name = "smartOrder2TrfCreateAction组件")
public class SmartOrderToTrfCreateActionComponent extends BaseActionComponent {

    private static final Integer SGSMart_DataEntry = 10002;

    private final SgsMartOrderToTrfActionExtPt sgsMartOrderToTrfActionExtPt;

    private final TransactionTemplate transactionTemplate;

    private final TaskService taskService;

    private final TrfOrderDomainService trfOrderDomainService;

    public SmartOrderToTrfCreateActionComponent(SgsMartOrderToTrfActionExtPt sgsMartOrderToTrfActionExtPt, TransactionTemplate transactionTemplate, TaskService taskService, TrfOrderDomainService trfOrderDomainService) {
        this.sgsMartOrderToTrfActionExtPt = sgsMartOrderToTrfActionExtPt;
        this.transactionTemplate = transactionTemplate;
        this.taskService = taskService;
        this.trfOrderDomainService = trfOrderDomainService;
    }

    @Override
    public void process() throws Exception {
        Order2TRFContext order2TRFContext = this.getContextBean(Order2TRFContext.class);
        OrderToTrfReq orderToTrfReq = order2TRFContext.getOrderToTrfReq();
        List<TrfOrderPO> orderList = trfOrderDomainService.selectAllByOrderNo(orderToTrfReq.getOrder().getOrderNo(), orderToTrfReq.getSystemId());
        if (Func.isNotEmpty(orderList)) {
            Set<String> trfNoList = orderList.stream()
                    .map(TrfOrderPO::getTrfNo)
                    .collect(Collectors.toSet());
            List<TaskInfoDTO> unbindTaskList = taskService.queryByExtIdsAndGroupKey(RefSystemIdAdapter.route(RefSystemIdEnum.SGSMart.getRefSystemId()), trfNoList, TRF_UNBIND_EVENT + "");
            Boolean unbindTaskAllSuccess = Optional.ofNullable(unbindTaskList)
                    .map(list -> list.stream()
                            .allMatch(task -> Objects.equals(task.getTaskStatus(), TaskStatusEnum.SUCCESS.getCode())))
                    .orElse(true);
            if(!unbindTaskAllSuccess) {
                throw new IllegalStateException("The unbind operation is not complete. Please try again later or contact the administrator.");
            }
        }
        createTrf(order2TRFContext);
    }

    private void createTrf(Order2TRFContext order2TRFContext) {
        OrderToTrfReq orderToTrfReq = order2TRFContext.getOrderToTrfReq();
        TrfDTO trfDTO = order2TRFContext.getTrfDTO();
        TrfDOV2 trfParam = order2TRFContext.getTrfDOV2();
        transactionTemplate.execute(transactionStatus -> {
            sgsMartOrderToTrfActionExtPt.saveSmartOrderInfo(orderToTrfReq, trfDTO, trfParam);
            return transactionStatus;
        });
        TrfNoDTO trfNoDTO = TrfNoConvertor.toTrfNo(trfParam);
        order2TRFContext.setTrfNoDTO(trfNoDTO);
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) {
        Order2TRFContext order2TRFContext = (Order2TRFContext) requestContext;
        createTrf(order2TRFContext);
        return order2TRFContext;
    }
}
