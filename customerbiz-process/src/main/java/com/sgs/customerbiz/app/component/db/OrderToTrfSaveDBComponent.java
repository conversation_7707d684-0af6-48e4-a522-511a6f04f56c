package com.sgs.customerbiz.app.component.db;


import com.sgs.customerbiz.biz.convertor.TrfNoConvertor;
import com.sgs.customerbiz.context.Order2TRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfInfoExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.CustomerTrfInfoPO;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.domain.enums.TrfActionEnum;
import com.sgs.customerbiz.infrastructure.api.IdService;
import com.sgs.customerbiz.model.trf.dto.TrfNoDTO;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
@LiteflowComponent(id = "orderToTrfSaveDBComponent", name = "order2TRF数据入库处理器")
public class OrderToTrfSaveDBComponent extends BaseSave2DBComponent {
    @Resource
    private TrfDomainService trfDomainService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private TrfInfoExtMapper trfInfoExtMapper;
    @Resource
    private IdService idService;
    @Override
    public void process() throws Exception {

        Order2TRFContext order2TRFContext = this.getContextBean(Order2TRFContext.class);
        saveData(order2TRFContext);
    }

    private void saveData(Order2TRFContext order2TRFContext) {
        TrfDOV2 trfParam = order2TRFContext.getTrfDOV2();
        List<CustomerTrfInfoPO> trfs = order2TRFContext.getTrfs();
        TrfStatusControlDO trfStatusControlDO = order2TRFContext.getTrfStatusControlDO();
        CustomerTrfInfoPO customerTrfInfoPO = order2TRFContext.getCustomerTrfInfoPO();
        transactionTemplate.execute(transactionStatus -> {
            TrfNoDTO trfNoDTO = saveToDB(trfParam, trfs, trfStatusControlDO,customerTrfInfoPO);
            order2TRFContext.setTrfNoDTO(trfNoDTO);
            return trfNoDTO;
        });
    }

    protected TrfNoDTO saveToDB(TrfDOV2 trfParam, List<CustomerTrfInfoPO> trfs, TrfStatusControlDO trfStatusControlDO,CustomerTrfInfoPO customerTrfInfoPO) {

        if(Func.notNull(customerTrfInfoPO)){
            trfDomainService.saveCustomerTrf(customerTrfInfoPO);
        }
        if (Func.notNull(trfs)){
            trfInfoExtMapper.batchInsert(trfs);
        }

        trfDomainService.createTrf(trfParam);
        // 先调用bind建立trf和order关系
        trfDomainService.bind(trfParam.getHeader(), trfParam.getOrder());
        trfDomainService.confirm(trfStatusControlDO);
        return TrfNoConvertor.toTrfNo(trfParam);
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) {
        Order2TRFContext order2TRFContext = (Order2TRFContext)requestContext;
        saveData(order2TRFContext);
        return order2TRFContext;
    }
}
