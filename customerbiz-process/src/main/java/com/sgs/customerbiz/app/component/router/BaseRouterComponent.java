package com.sgs.customerbiz.app.component.router;

import com.sgs.customerbiz.app.component.test.ComponentTest;
import com.sgs.customerbiz.biz.event.RefSystemIdAdapter;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.core.NodeSwitchComponent;


public abstract class BaseRouterComponent extends NodeSwitchComponent implements ComponentTest {

    protected  String getScenarioValue(Integer refSystemId) {
        Integer integer = RefSystemIdAdapter.map.get(refSystemId);
        return Func.isEmpty(integer) ? Func.toStr(refSystemId) : Func.toStr(integer);
    }
    protected static String getRouter(String scenarioValue, String model) {
        /**
         * 根据model和scenarioValue来判断走哪个路由
         * 判断逻辑：如果secenarioValue为空且model为空，则走默认路由,则返回默认值："default"，
         * 如果scenarioValue不为空且model不为空，则返回两个值的组合格式为：model"_"+scenarioValue，
         * 如果scenarioValue不为空且model为空，则返回值的格式为：scenarioValue
         * 如果scenarioValue为空且model不为空，则返回值的组合格式为：model
         */
        return Func.isEmpty(scenarioValue) && Func.isEmpty(model) ? "default" :
                Func.isEmpty(scenarioValue) ? model :
                        Func.isEmpty(model) ? scenarioValue :
                                model + "_" + scenarioValue;
    }
}
