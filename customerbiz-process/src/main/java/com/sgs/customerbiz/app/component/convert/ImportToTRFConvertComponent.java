package com.sgs.customerbiz.app.component.convert;

import com.sgs.customerbiz.biz.customertrf.service.CustomerTrfDomainService;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.model.trf.dto.TrfDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.customerbiz.model.trf.enums.TrfSourceType;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.stereotype.Component;
import com.sgs.customerbiz.context.ImportToTRFContext;
import javax.annotation.Resource;

/**
 * ImportToTRF转换组件 - 客户TRF数据转换为SGS内部数据结构
 * 
 * <p><b>流程位置</b>：位于各子流程中，在获取客户TRF数据后的第一个转换节点</p>
 * 
 * <p><b>核心功能</b>：
 * <ul>
 *   <li>将客户TRF JSON格式转换为SGS内部TRF结构</li>
 *   <li>使用扩展点机制根据refSystemId选择不同的转换实现</li>
 *   <li>设置TRF来源为TrfToOrder</li>
 *   <li>设置系统ID等元数据信息</li>
 * </ul>
 * </p>
 * 
 * <p><b>转换流程</b>：
 * <ol>
 *   <li>参数验证：验证customerTrfJson和refSystemId不为空</li>
 *   <li>扩展点选择：根据refSystemId选择对应的转换实现</li>
 *   <li>数据转换：调用客户特定的转换逻辑</li>
 *   <li>元数据设置：设置source、systemId等字段</li>
 * </ol>
 * </p>
 * 
 * <p><b>关键参数</b>：
 * <ul>
 *   <li><code>customerTrfJson</code> - 客户TRF JSON数据，不能为空</li>
 *   <li><code>refSystemId</code> - 参考系统ID，用于选择转换实现</li>
 *   <li><code>systemId</code> - 系统ID</li>
 *   <li><code>labCode</code> - 实验室代码</li>
 *   <li><code>buCode</code> - 业务单元代码</li>
 * </ul>
 * </p>
 * 
 * <p><b>扩展点机制</b>：
 * <ul>
 *   <li>使用COLA框架的ExtensionExecutor</li>
 *   <li>根据refSystemId构建BizScenario</li>
 *   <li>执行CustomerTrfImportActionExtPt.convert方法</li>
 * </ul>
 * </p>
 * 
 * <p><b>调用的核心方法</b>：
 * <ul>
 *   <li>{@code CustomerTrfDomainService.convert()} - 主要转换逻辑</li>
 *   <li>{@code ExtensionExecutor.execute()} - 扩展点执行</li>
 * </ul>
 * </p>
 * 
 * <p><b>后续流程</b>：转换完成后，流程将进入importToTRFPreHandleComponent进行预处理</p>
 * 
 * <AUTHOR> from Import2TRF_Main Process Analysis
 * @see com.sgs.customerbiz.biz.customertrf.service.CustomerTrfDomainService
 * @see com.sgs.customerbiz.app.component.prehandle.ImportToTRFPreHandleComponent
 */
@Component
@LiteflowComponent(id = "importToTRFConvertComponent", name = "importToTRFConvert组件")
public class ImportToTRFConvertComponent extends BaseConvertComponent {

    /** 客户TRF领域服务 - 执行客户TRF数据的核心转换逻辑 */
    @Resource
    protected CustomerTrfDomainService customerTrfDomainService;

    /**
     * 组件处理入口
     * 
     * <p>从上下文获取客户TRF数据和导入请求，执行转换操作</p>
     * 
     * @throws Exception 转换过程中的异常
     */
    @Override
    public void process() throws Exception {

        ImportToTRFContext importToTRFContext = this.getContextBean(ImportToTRFContext.class);
        convert(importToTRFContext);
    }

    /**
     * 执行转换操作
     * 
     * <p>从上下文中获取必要的参数，调用convert方法执行转换，并更新上下文</p>
     * 
     * @param importToTRFContext 导入TRF上下文
     */
    private void convert(ImportToTRFContext importToTRFContext) {
        TrfImportReq importReq = importToTRFContext.getTrfImportReq();
        String customerTrf = importToTRFContext.getCustomerTrf();
        TrfDTO trfDTO = convert(customerTrf, importReq);
        importToTRFContext.setTrfDTO(trfDTO);
        importToTRFContext.setSgsTRF(trfDTO);
    }

    /**
     * 客户TRF数据转换核心方法
     * 
     * <p><b>转换流程</b>：
     * <ol>
     *   <li>调用CustomerTrfDomainService.convert执行具体转换</li>
     *   <li>设置TRF来源为TrfToOrder</li>
     *   <li>设置系统ID</li>
     * </ol>
     * </p>
     * 
     * <p><b>扩展点调用</b>：根据refSystemId选择不同的CustomerTrfImportActionExtPt实现</p>
     * 
     * @param customerTrfJson 客户TRF JSON数据
     * @param importReq 导入请求参数
     * @return 转换后的SGS TRF数据对象
     */
    public TrfDTO convert(String customerTrfJson, TrfImportReq importReq) {
        //CustomerTrf Json 转换为SgsTrf结构
        TrfDTO trfDTO = customerTrfDomainService.convert(customerTrfJson,
                importReq.getRefSystemId(), importReq.getSystemId(), importReq.getLabCode(), importReq.getBuCode(),
                importReq.getTrfTemplateId(),importReq.getTrfTemplateType(),
                importReq.getFormId(), importReq.getGridId(), importReq.getLabContact());

        // createTrfByCustomerTrf 则trf.source = TrfToOrder
        trfDTO.getHeader().setSource(TrfSourceType.TRF2Order.getSourceType());
        //设置trf SystemId
        trfDTO.getHeader().setSystemId(importReq.getSystemId());


        return trfDTO;
    }

    /**
     * 测试组件方法
     * 
     * <p>提供独立的组件测试能力，执行相同的转换逻辑</p>
     * 
     * @param requestContext 请求上下文
     * @return 转换后的上下文对象
     */
    @Override
    public RequestContext testComponent(RequestContext requestContext) {
        ImportToTRFContext importToTRFContext = (ImportToTRFContext)requestContext;
        convert(importToTRFContext);
        return importToTRFContext;
    }
}
