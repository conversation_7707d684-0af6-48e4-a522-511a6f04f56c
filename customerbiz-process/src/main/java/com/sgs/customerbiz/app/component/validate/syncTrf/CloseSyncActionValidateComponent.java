package com.sgs.customerbiz.app.component.validate.syncTrf;

import com.sgs.customerbiz.app.component.validate.AbstractValidateComponent;
import com.sgs.customerbiz.biz.service.synctrf.validator.ClosedSyncActionValidatorExtPt;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.core.constants.Constants;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.integration.FrameWorkClient;
import com.sgs.customerbiz.integration.dto.QueryExchangeRateReq;
import com.sgs.customerbiz.integration.dto.QueryExchangeRateRsp;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.facade.model.common.CustomResult;
import com.sgs.preorder.facade.model.info.LabInfo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;


@Slf4j
@Component
@LiteflowComponent(id = "closeSyncActionValidateComponent", name = "SyncClosed参数校验组件")
public class CloseSyncActionValidateComponent extends NormalSyncActionValidatorComponent {

    @Autowired
    private TrfDomainService trfDomainService;

    @Autowired
    private FrameWorkClient frameWorkClient;

    @Autowired
    private ConfigClient configClient;
    @Resource
    private ClosedSyncActionValidatorExtPt closedSyncActionValidatorExtPt;

    @Override
    protected void doPreProcess() throws Exception {

    }

    @Override
    protected void doPostProcess() throws Exception {

    }

    @Override
    public void doCoreProcess() throws Exception {
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        closedSyncActionValidatorExtPt.validate(syncTRFContext.getSyncReq());
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        SyncTRFContext syncTRFContext = (SyncTRFContext) requestContext;
        closedSyncActionValidatorExtPt.validate(syncTRFContext.getSyncReq());
        return syncTRFContext;
    }
}
