package com.sgs.customerbiz.app.component.action.syncTrf.updateInfo;

import cn.hutool.core.util.ObjectUtil;
import com.sgs.customerbiz.app.component.action.syncTrf.BaseSyncTRFActionComponent;
import com.sgs.customerbiz.biz.service.synctrf.SyncTrfContextHolder;
import com.sgs.customerbiz.biz.service.synctrf.cmd.SyncUpdateInfoActionExtPt;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.dfv.enums.ActiveIndicatorEnum;
import com.sgs.customerbiz.domain.domainevent.TrfActionEvent;
import com.sgs.customerbiz.domain.domainevent.actionevent.TrfUpdateInfoEvent;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.domain.enums.TrfEventTriggerBy;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.TrfOrderDTO;
import com.sgs.customerbiz.model.trf.dto.TrfSyncHeaderDTO;
import com.sgs.customerbiz.model.trf.enums.TrfStatusEnum;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;

@Slf4j
@Component
@LiteflowComponent(id = "syncUpdateInfoActionComponent", name = "SyncUpdateInfo")
public class SyncUpdateInfoActionComponent extends BaseSyncTRFActionComponent {

    @Autowired
    private SyncUpdateInfoActionExtPt syncUpdateInfoActionExtPt;
    @Override
    public void doStatusControlCustom(TrfFullDTO trfDOParam) {
        syncUpdateInfoActionExtPt.doStatusControlCustom(trfDOParam);
    }

    @Override
    public TrfStatusResult doStatusControl(TrfStatusControlDO trfStatusControlDO) {
       return syncUpdateInfoActionExtPt.doStatusControl(trfStatusControlDO);
    }
    @Override
    public boolean processByAction(TrfFullDTO trfDOParam, TrfStatusControlDO trfStatusControlDO, TrfSyncHeaderDTO syncHeader) {
      return  syncUpdateInfoActionExtPt.processByAction(trfDOParam, trfStatusControlDO, syncHeader);
    }

}
