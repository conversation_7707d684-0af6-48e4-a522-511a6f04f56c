package com.sgs.customerbiz.app.process;

import com.sgs.customerbiz.context.ImportToTRFContext;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.model.trf.dto.importtrfresp.TrfImportResult;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.yomahub.liteflow.flow.LiteflowResponse;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class ImportToTRFProcess implements BaseProcess<TrfImportResult,TrfImportReq>{

    @Resource
    private ProcessFlowManager processFlowManager;
    @Override
    public TrfImportResult doProcess(TrfImportReq object) {

        ImportToTRFContext importToTRFContext = new ImportToTRFContext();
        importToTRFContext.setTrfImportReq(object);
        String chainName = "Import2TRF_Main";
        importToTRFContext.setChainName(chainName);
        // 执行流程
        LiteflowResponse response = processFlowManager.executeFlow(ProcessTypeEnum.IMPORT_TRF,chainName, importToTRFContext);
        if (response.isSuccess()) {
            ImportToTRFContext responseContext = response.getContextBean(ImportToTRFContext.class);
            return responseContext.getResult();
        }else{
            Exception ex = response.getCause();
            if(ex instanceof BizException){
                throw (BizException)ex;
            }else{
                ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.IMPORTPROCESS, ErrorFunctionTypeEnum.PROCESS, ErrorTypeEnum.SYSTEMERROR);
                throw new CustomerBizException(errorCode,ResponseCode.FAIL.getCode(),ex.getMessage());
            }
        }

    }
}
