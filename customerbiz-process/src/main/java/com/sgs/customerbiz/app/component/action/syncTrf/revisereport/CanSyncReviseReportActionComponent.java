package com.sgs.customerbiz.app.component.action.syncTrf.revisereport;

import com.sgs.customerbiz.app.component.action.syncTrf.BaseCanSyncTrfActionComponent;
import com.sgs.customerbiz.biz.service.synctrf.cmd.BaseSyncTrfAction;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@LiteflowComponent(id = "canSyncReviseReportActionComponent", name = "SyncReviseReport动作")
public class CanSyncReviseReportActionComponent extends BaseCanSyncTrfActionComponent {

    private final TrfDomainService trfDomainService;

    public CanSyncReviseReportActionComponent(BaseSyncTrfAction baseSyncTrfAction, TrfDomainService trfDomainService) {
        super(baseSyncTrfAction);
        this.trfDomainService = trfDomainService;
    }

    @Override
    public void doCheck(SyncTRFContext syncTRFContext) {
        trfDomainService.canReviseReport(syncTRFContext.getTrfStatusControlDO());
    }

}
