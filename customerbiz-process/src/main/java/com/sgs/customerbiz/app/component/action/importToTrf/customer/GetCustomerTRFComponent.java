package com.sgs.customerbiz.app.component.action.importToTrf.customer;

import com.sgs.customerbiz.app.component.action.BaseActionComponent;
import com.sgs.customerbiz.biz.customertrf.service.CustomerTrfDomainService;
import com.sgs.customerbiz.context.ImportToTRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 获取客户TRF组件 - PULL模式和默认模式的客户数据获取节点
 * 
 * <p><b>流程位置</b>：位于PullImport2TRF和Import2TRF流程的第一个节点</p>
 * 
 * <p><b>核心功能</b>：
 * <ul>
 *   <li>从客户系统主动拉取TRF数据</li>
 *   <li>使用扩展点机制根据refSystemId选择不同的获取实现</li>
 *   <li>为PushImportGetCustomerActionComponent的父类</li>
 *   <li>提供通用的客户TRF获取能力</li>
 * </ul>
 * </p>
 * 
 * <p><b>PULL模式特点</b>：
 * <ul>
 *   <li>系统主动从客户系统拉取数据</li>
 *   <li>需要客户系统提供API接口</li>
 *   <li>适用于客户系统无法主动推送的场景</li>
 * </ul>
 * </p>
 * 
 * <p><b>处理流程</b>：
 * <ol>
 *   <li>从上下文获取TrfImportReq</li>
 *   <li>调用getCustomerTrf方法获取客户TRF数据</li>
 *   <li>将获取的数据存储到上下文中</li>
 * </ol>
 * </p>
 * 
 * <p><b>关键参数</b>：
 * <ul>
 *   <li><code>TrfImportReq.getTrfNo()</code> - TRF编号，不能为空</li>
 *   <li><code>TrfImportReq.getRefSystemId()</code> - 参考系统ID，用于选择扩展点</li>
 * </ul>
 * </p>
 * 
 * <p><b>扩展点机制</b>：
 * <ul>
 *   <li>使用COLA框架的ExtensionExecutor</li>
 *   <li>根据refSystemId构建BizScenario</li>
 *   <li>执行CustomerTrfImportActionExtPt.getCustomerTrf方法</li>
 * </ul>
 * </p>
 * 
 * <p><b>调用的核心方法</b>：
 * <ul>
 *   <li>{@code CustomerTrfDomainService.getCustomerTrf(TrfImportReq)} - 主要获取逻辑</li>
 * </ul>
 * </p>
 * 
 * <p><b>后续流程</b>：获取数据后，流程将进入相应的ConvertComponent进行数据转换</p>
 * 
 * <AUTHOR> from Import2TRF_Main Process Analysis
 * @see com.sgs.customerbiz.biz.customertrf.service.CustomerTrfDomainService
 * @see PushImportGetCustomerActionComponent
 */
@Component
@LiteflowComponent(id = "getCustomerTRFComponent", name = "获取客户的TRF信息")
public class GetCustomerTRFComponent extends BaseActionComponent {

    /** 客户TRF领域服务 - 执行客户TRF数据获取的核心逻辑 */
    @Resource
    protected CustomerTrfDomainService customerTrfDomainService;

    /**
     * 组件处理入口
     * 
     * <p>从上下文获取导入请求，调用获取方法，并将结果存储到上下文中</p>
     * 
     * @throws Exception 获取过程中的异常
     */
    @Override
    public void process() throws Exception {

        ImportToTRFContext importToTRFContext = this.getContextBean(ImportToTRFContext.class);
        TrfImportReq importReq = importToTRFContext.getTrfImportReq();
        String customerTrf = getCustomerTrf(importReq);
        importToTRFContext.setCustomerTrf(customerTrf);
    }

    /**
     * 获取客户TRF数据
     * 
     * <p><b>核心方法</b>：通过领域服务调用扩展点获取客户TRF数据</p>
     * 
     * <p><b>扩展点处理</b>：
     * <ul>
     *   <li>根据refSystemId选择不同的CustomerTrfImportActionExtPt实现</li>
     *   <li>支持多种客户系统的不同获取方式</li>
     * </ul>
     * </p>
     * 
     * @param importReq 导入TRF请求，包含trfNo和refSystemId等关键信息
     * @return 客户TRF数据字符串（通常为JSON格式）
     */
    public String getCustomerTrf(TrfImportReq importReq) {
        return customerTrfDomainService.getCustomerTrf(importReq);
    }

    /**
     * 测试组件方法
     * 
     * <p>提供独立的组件测试能力，执行相同的获取逻辑</p>
     * 
     * @param requestContext 请求上下文
     * @return 处理后的上下文对象
     */
    @Override
    public RequestContext testComponent(RequestContext requestContext) {
        ImportToTRFContext importToTRFContext = (ImportToTRFContext) requestContext;
        TrfImportReq importReq = importToTRFContext.getTrfImportReq();
        String customerTrf = getCustomerTrf(importReq);
        importToTRFContext.setCustomerTrf(customerTrf);
        return importToTRFContext;
    }
}
