package com.sgs.customerbiz.app.component.action.syncTrf.quotation;

import com.sgs.customerbiz.app.component.action.syncTrf.BaseSyncTRFActionComponent;
import com.sgs.customerbiz.biz.service.synctrf.cmd.SyncToQuotationActionExtPt;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfOrderDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.enums.TrfStatusEnum;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@LiteflowComponent(id = "syncToQuotationActionComponent", name = "SyncToQuotation")
public class SyncToQuotationActionComponent extends BaseSyncTRFActionComponent {

    @Autowired
    private SyncToQuotationActionExtPt syncToQuotationActionExtPt;
    @Override
    public void doStatusControlCustom(TrfFullDTO trfDOParam) {
        syncToQuotationActionExtPt.doStatusControlCustom(trfDOParam);
    }
    @Override
    public TrfStatusResult doStatusControl(TrfStatusControlDO trfStatusControlDO) {
       return syncToQuotationActionExtPt.doStatusControl(trfStatusControlDO);
    }
}
