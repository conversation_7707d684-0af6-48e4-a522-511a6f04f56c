package com.sgs.customerbiz.app.component.router;

import com.sgs.customerbiz.context.ImportToTRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

/**
 * ImportTRF模式路由组件 - Import2TRF_Main流程的核心路由节点
 * 
 * <p><b>流程位置</b>：位于Import2TRF_Main流程中，在importToTRFValidateComponent之后</p>
 * 
 * <p><b>核心功能</b>：
 * <ul>
 *   <li>根据导入模式路由到不同的处理子流程</li>
 *   <li>支持PUSH模式和PULL模式的路由决策</li>
 *   <li>为不同客户系统提供统一的入口路由</li>
 * </ul>
 * </p>
 * 
 * <p><b>路由决策逻辑</b>：
 * <ul>
 *   <li><b>PUSH模式</b>：路由到SubChain_PushImport2TRF子流程</li>
 *   <li><b>PULL模式</b>：路由到SubChain_PullImportTRF子流程</li>
 *   <li><b>默认模式</b>：路由到Import2TRF默认子流程</li>
 * </ul>
 * </p>
 * 
 * <p><b>关键判断参数</b>：
 * <ul>
 *   <li><code>TrfImportReq.getImportMode()</code> - 导入模式（PUSH/PULL）</li>
 *   <li>路由标签格式："tag:" + importMode</li>
 * </ul>
 * </p>
 * 
 * <p><b>路由目标</b>：
 * <ol>
 *   <li><b>tag:PUSH</b> → SubChain_PushImport2TRF（客户主动推送数据模式）</li>
 *   <li><b>tag:PULL</b> → SubChain_PullImportTRF（系统主动拉取数据模式）</li>
 *   <li><b>default</b> → Import2TRF（默认处理流程）</li>
 * </ol>
 * </p>
 * 
 * <p><b>PUSH vs PULL模式区别</b>：
 * <ul>
 *   <li><b>PUSH</b>：客户系统主动推送TRF数据，数据在请求中直接提供</li>
 *   <li><b>PULL</b>：系统主动从客户系统拉取TRF数据，支持多种子模式</li>
 * </ul>
 * </p>
 * 
 * <AUTHOR> from Import2TRF_Main Process Analysis
 * @see com.sgs.customerbiz.model.trf.enums.TrfImportMode
 */
@Component
@LiteflowComponent(id = "importTRFModelRouterComponent", name = "importTRF调用模式路由处理器")
public class ImportTRFModelRouterComponent extends BaseRouterComponent{
    /**
     * 测试组件方法
     * 
     * <p>当前实现返回null，可能需要根据业务需要实现具体逻辑</p>
     * 
     * @param requestContext 请求上下文
     * @return 处理后的上下文
     * @throws Exception 处理异常
     */
    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        return null;
    }
    /**
     * 获取路由标签
     * 
     * <p>根据导入模式生成相应的路由标签，用于LiteFlow的SWITCH路由</p>
     * 
     * <p><b>路由规则</b>：
     * <ul>
     *   <li>格式："tag:" + importMode</li>
     *   <li>PUSH模式 → "tag:PUSH"</li>
     *   <li>PULL模式 → "tag:PULL"</li>
     * </ul>
     * </p>
     * 
     * @param importToTRFContext 导入TRF上下文
     * @return 路由标签字符串
     */
    private @NotNull String getRouter(ImportToTRFContext importToTRFContext) {
        TrfImportReq trfImportReq = importToTRFContext.getTrfImportReq();
        String model = trfImportReq.getImportMode();
        String router= "tag:"+ model;
        return router;
    }

    /**
     * 路由切换处理方法
     * 
     * <p>LiteFlow框架调用的路由决策方法，根据导入模式返回相应的路由标签</p>
     * 
     * <p><b>执行流程</b>：
     * <ol>
     *   <li>从上下文获取ImportToTRFContext</li>
     *   <li>调用getRouter方法生成路由标签</li>
     *   <li>返回标签供LiteFlow进行路由</li>
     * </ol>
     * </p>
     * 
     * @return 路由标签（如"tag:PUSH"或"tag:PULL"）
     * @throws Exception 处理异常
     */
    @Override
    public String processSwitch() throws Exception {
        ImportToTRFContext importToTRFContext = this.getContextBean(ImportToTRFContext.class);
        return getRouter(importToTRFContext);
    }
}
