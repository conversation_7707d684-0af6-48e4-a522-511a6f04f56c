package com.sgs.customerbiz.app.component.special.syncTRF;

import com.alibaba.fastjson.JSON;
import com.sgs.customerbiz.app.component.special.AbstractSpecialComponent;
import com.sgs.customerbiz.biz.service.SciTrfBizService;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.ErrorAssert;
import com.sgs.customerbiz.model.trf.dto.TrfReportDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.customerbiz.model.trf.enums.ReportStatusEnum;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Slf4j
@Component
@LiteflowComponent(id = "syncTRFSpecialComponent", name = "syncTRF特殊处理组件")
public class SyncTRFSpecialComponent extends AbstractSpecialComponent {
    @Autowired
    private SciTrfBizService sciTrfBizService;
    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        SyncTRFContext syncTRFContext =(SyncTRFContext)requestContext;
        TrfSyncReq syncReq = syncTRFContext.getSyncReq();
        doSpec(syncReq);
        return syncTRFContext;
    }

    @Override
    public void process() throws Exception {
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        TrfSyncReq syncReq = syncTRFContext.getSyncReq();
        doSpec(syncReq);
    }
    private void doSpec(TrfSyncReq syncReq) throws Exception {
        sciTrfBizService.checkSyncTrfData(syncReq);
    }
}
