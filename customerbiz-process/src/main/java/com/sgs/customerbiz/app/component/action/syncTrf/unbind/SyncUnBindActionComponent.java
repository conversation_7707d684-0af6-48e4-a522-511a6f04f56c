package com.sgs.customerbiz.app.component.action.syncTrf.unbind;

import com.sgs.customerbiz.app.component.action.syncTrf.BaseSyncTRFActionComponent;
import com.sgs.customerbiz.biz.service.synctrf.cmd.SyncUnBindActionExtPt;
import com.sgs.customerbiz.core.common.SciRequestContext;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfInfoPO;
import com.sgs.customerbiz.domain.domainevent.TrfEvent;
import com.sgs.customerbiz.domain.domainevent.TrfUnBindEvent;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.domain.enums.TrfEventTriggerBy;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.enums.TrfSourceType;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.TrfStatusEnum;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;


@Slf4j
@Component
@LiteflowComponent(id = "syncUnBindActionComponent", name = "SyncUnBind")
public class SyncUnBindActionComponent extends BaseSyncTRFActionComponent {

    @Autowired
    private SyncUnBindActionExtPt syncUnBindActionExtPt;

    private static final Integer SODA = 2;
    @Override
    public void doStatusControlCustom(TrfFullDTO trfDOParam) {
        syncUnBindActionExtPt.doStatusControlCustom(trfDOParam);
    }
    @Override
    public TrfStatusResult doStatusControl(TrfStatusControlDO trfStatusControlDO) {
       return  syncUnBindActionExtPt.doStatusControl(trfStatusControlDO);
    }

}
