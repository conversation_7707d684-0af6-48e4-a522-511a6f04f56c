package com.sgs.customerbiz.app.component.action.event;


import com.sgs.customerbiz.app.component.action.BaseActionComponent;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.domain.domainevent.ObjectEvent;
import com.sgs.customerbiz.domain.domainevent.TrfEvent;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 事件通知组件 - 通用的事件发布和消息通知组件
 * 
 * <p><b>流程位置</b>：位于多个子流程的最后一个节点，如PullImport2TRF和Import2TRF</p>
 * 
 * <p><b>核心功能</b>：
 * <ul>
 *   <li>发布TRF相关的业务事件</li>
 *   <li>触发后续的业务处理流程</li>
 *   <li>支持多种类型的事件发布</li>
 *   <li>为PushEventNotifyActionComponent的父类</li>
 * </ul>
 * </p>
 * 
 * <p><b>事件类型支持</b>：
 * <ul>
 *   <li><b>TrfEvent</b>：TRF基础事件，存储在requestContext.getTrfEventList()中</li>
 *   <li><b>TrfActionEvent</b>：TRF操作事件，存储在requestContext.getTrfActionEventList()中</li>
 * </ul>
 * </p>
 * 
 * <p><b>事件处理逻辑</b>：
 * <ol>
 *   <li>从上下文获取RequestContext</li>
 *   <li>优先处理TrfEventList中的事件</li>
 *   <li>如果TrfEventList为空，则处理TrfActionEventList中的事件</li>
 *   <li>使用ApplicationEventPublisher发布每个事件</li>
 * </ol>
 * </p>
 * 
 * <p><b>事件发布特点</b>：
 * <ul>
 *   <li>基于Spring的ApplicationEventPublisher机制</li>
 *   <li>支持异步事件处理</li>
 *   <li>支持事件监听器的模式</li>
 *   <li>支持事件的过滤和路由</li>
 * </ul>
 * </p>
 * 
 * <p><b>应用场景</b>：
 * <ul>
 *   <li>TRF状态变更通知</li>
 *   <li>数据同步事件</li>
 *   <li>业务流程触发</li>
 *   <li>第三方系统集成通知</li>
 * </ul>
 * </p>
 * 
 * <p><b>调用的核心方法</b>：
 * <ul>
 *   <li>{@code ApplicationEventPublisher.publishEvent(Object)} - Spring事件发布</li>
 * </ul>
 * </p>
 * 
 * <p><b>后续流程</b>：事件发布后，整个导入流程结束，由相应的事件监听器处理后续逻辑</p>
 * 
 * <AUTHOR> from Import2TRF_Main Process Analysis
 * @see org.springframework.context.ApplicationEventPublisher
 * @see com.sgs.customerbiz.domain.domainevent.TrfEvent
 * @see com.sgs.customerbiz.domain.domainevent.TrfActionEvent
 */
@Component
@LiteflowComponent(id = "eventNotifyActionComponent", name = "消息事件通知组件")
public class EventNotifyActionComponent extends BaseActionComponent {

    /** Spring事件发布器 - 用于发布各种业务事件 */
    @Resource
    protected ApplicationEventPublisher applicationEventPublisher;
    /**
     * 组件处理入口
     * 
     * <p>从上下文获取请求对象，调用事件发布方法</p>
     * 
     * @throws Exception 事件发布过程中的异常
     */
    @Override
    public void process() throws Exception {

        RequestContext requestContext = this.getContextBean(RequestContext.class);
        publishEvent(requestContext);
    }

    /**
     * 发布事件
     * 
     * <p><b>事件处理优先级</b>：
     * <ol>
     *   <li>优先处理TrfEventList中的事件</li>
     *   <li>如果TrfEventList为空，则处理TrfActionEventList中的事件</li>
     * </ol>
     * </p>
     * 
     * <p><b>事件发布方式</b>：使用Java 8 Stream API逐个发布事件</p>
     * 
     * @param requestContext 请求上下文，包含待发布的事件列表
     */
    protected void publishEvent(RequestContext requestContext) {
        if(!requestContext.getTrfEventList().isEmpty()){
            requestContext.getTrfEventList().stream().forEach(event -> applicationEventPublisher.publishEvent(event));
        }else if(!requestContext.getTrfActionEventList().isEmpty()){
            requestContext.getTrfActionEventList().stream().forEach(event -> applicationEventPublisher.publishEvent(event));
        }
    }


    /**
     * 测试组件方法
     * 
     * <p>提供独立的组件测试能力，执行相同的事件发布逻辑</p>
     * 
     * @param requestContext 请求上下文
     * @return 处理后的上下文对象
     */
    @Override
    public RequestContext testComponent(RequestContext requestContext) {

        publishEvent(requestContext);
        return requestContext;
    }
}
