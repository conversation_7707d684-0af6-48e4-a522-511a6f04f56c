package com.sgs.customerbiz.app.component.action.syncTrf.revisereport;

import com.sgs.customerbiz.app.component.action.syncTrf.BaseSyncTRFActionComponent;
import com.sgs.customerbiz.biz.service.synctrf.cmd.SyncReviseReportActionExtPt;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfInfoPO;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfOrderDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.enums.TrfStatusEnum;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
@LiteflowComponent(id = "syncReviseReportActionComponent", name = "SyncReviseReport动作")
public class SyncReviseReportActionComponent extends BaseSyncTRFActionComponent {

    @Autowired
    private SyncReviseReportActionExtPt syncReviseReportActionExtPt;
    @Override
    public void doStatusControlCustom(TrfFullDTO trfDOParam) {
        syncReviseReportActionExtPt.doStatusControlCustom(trfDOParam);
    }
    @Override
    public TrfStatusResult doStatusControl(TrfStatusControlDO trfStatusControlDO) {
        return syncReviseReportActionExtPt.doStatusControl(trfStatusControlDO);
    }
}
