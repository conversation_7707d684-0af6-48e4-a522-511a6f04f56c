package com.sgs.customerbiz.app.router;

import com.fasterxml.jackson.databind.JsonNode;
import com.sgs.customerbiz.app.process.*;
import com.sgs.customerbiz.biz.service.SciTrfBizService;
import com.sgs.customerbiz.biz.service.todolist.TodoListService;
import com.sgs.customerbiz.core.common.SciRequestContext;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.domain.enums.TrfActionEnum;
import com.sgs.customerbiz.facade.model.dto.TodoListBaseDataDTO;
import com.sgs.customerbiz.model.trf.dto.TrfNoDTO;
import com.sgs.customerbiz.model.trf.dto.importtrfresp.TrfImportResult;
import com.sgs.customerbiz.model.trf.dto.req.*;
import com.sgs.customerbiz.model.trf.dto.resp.TrfSyncResult;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.tool.utils.Func;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ProcessRouterService {

    @Autowired
    private SciTrfBizService sciTrfBizService;

    @Autowired
    private ImportToTRFProcess importToTRFProcess;
    @Autowired
    private OrderToTRFProcess orderToTRFProcess;
    @Autowired
    private SyncTRFProcess syncTRFProcess;
    @Autowired
    private ProcessSwitch processSwitch;
    @Autowired
    private TodoListImportToTRFProcess todoListImportToTRFProcess;
    @Autowired
    private TodoListService todoListService;
    @Autowired
    private CustomerTRFProcess customerTRFProcess;
    public TrfImportResult importToTrfRouter(TrfImportReq importReq){
        Integer refSystemId = importReq.getRefSystemId();
        String testToken = importReq.getTestToken();
        //如果refSystemId为空则走默认流程
        if(processSwitch.isProcessSwitch(TrfActionEnum.IMPORT.getCode(),refSystemId,testToken)){
            return importToTRFProcess.doProcess(importReq);
        }
        return sciTrfBizService.importTrf(importReq);
    }

    public TrfImportResult bindTrf(TrfBindReq trfBindReq) {
        Integer refSystemId = trfBindReq.getRefSystemId();
        String testToken = trfBindReq.getTestToken();
        if(processSwitch.isProcessSwitch(TrfActionEnum.QUERY_CUSTOMER_TRF.getCode(),refSystemId,testToken)){
            return customerTRFProcess.doProcess(trfBindReq);
        }
        return sciTrfBizService.bindTrf(trfBindReq);
    }
    public TrfImportResult getCustomerInfo(CustomerTrfReq customerTrfReq) {
        TrfBindReq trfBindReq = new TrfBindReq();
        BeanUtils.copyProperties(customerTrfReq,trfBindReq);
        return bindTrf(trfBindReq);
    }

    public CustomResult importTrfNo(JsonNode reqObject) {
        CustomResult<Object> rspResult = new CustomResult<>();
        CustomResult<TodoListBaseDataDTO> rspResultData = todoListService.checkJsonNode(reqObject);
        if (!rspResultData.isSuccess()) {
            return rspResult.fail(rspResultData.getMsg());
        }
        if(Func.isNotEmpty(rspResultData)&&Func.isNotEmpty(rspResultData.getData())){
            TodoListBaseDataDTO dataDTO =  rspResultData.getData();
            Integer refSystemId = dataDTO.getRefSystemId();
            SciRequestContext requestContext = ProductLineContextHolder.retrieveSciRequestContext();
            String testToken = requestContext.getTestToken();
            //testToken = "test_liteflow";
            //如果refSystemId为空则走默认流程
            if(processSwitch.isProcessSwitch(TrfActionEnum.IMPORT.getCode(),refSystemId,testToken)){
                return todoListImportToTRFProcess.importTrfNo(reqObject);
            }
            return todoListService.importTrfNoOld(reqObject);
        }
       return rspResult;
    }

    public TrfNoDTO orderToTrfRouter(OrderToTrfReq orderToTrfReq){
        Integer refSystemId = orderToTrfReq.getRefSystemId();
        String testToken = orderToTrfReq.getTestToken();
        //如果refSystemId为空则走默认流程
        if(processSwitch.isProcessSwitch(TrfActionEnum.ORDER_TO_TRF.getCode(),refSystemId,testToken)){
            return orderToTRFProcess.doProcess(orderToTrfReq);
        }
        return sciTrfBizService.orderToTrf(orderToTrfReq);
    }

    public TrfSyncResult syncToTrfRouter(TrfSyncReq syncReq){
        Integer refSystemId = syncReq.getRefSystemId();
        String testToken = syncReq.getTestToken();
        //如果refSystemId为空则走默认流程
        if(processSwitch.isProcessSwitch(TrfActionEnum.SYNC_TRF.getCode(),refSystemId,testToken)){
            return syncTRFProcess.doProcess(syncReq);
        }
        return sciTrfBizService.syncTrf(syncReq);
    }
}
