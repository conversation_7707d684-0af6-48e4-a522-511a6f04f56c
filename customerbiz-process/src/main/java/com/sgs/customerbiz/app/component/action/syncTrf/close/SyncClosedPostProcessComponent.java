package com.sgs.customerbiz.app.component.action.syncTrf.close;

import com.sgs.customerbiz.app.component.action.BaseActionComponent;
import com.sgs.customerbiz.biz.service.synctrf.cmd.SyncClosedActionExtPt;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.service.ReportDataService;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@LiteflowComponent(id = "syncClosedPostProcessComponent", name = "保存Invoice&Quatation")
public class SyncClosedPostProcessComponent extends BaseActionComponent {
    @Autowired
    private SyncClosedActionExtPt syncClosedActionExtPt;
    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        return null;
    }

    @Override
    public void process() throws Exception {
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        TrfStatusResult trfStatusResult = syncTRFContext.getTrfStatusResult();
        TrfFullDTO trfDOParam = syncTRFContext.getTrfDOParam();
        //保存Invoice&Quatation
        syncSingleTrfPostprocess(trfStatusResult, trfDOParam);
    }
    public void syncSingleTrfPostprocess(TrfStatusResult trfStatusResult, TrfFullDTO trfDOParam) {
        syncClosedActionExtPt.syncSingleTrfPostprocess(trfStatusResult, trfDOParam);
    }
}
