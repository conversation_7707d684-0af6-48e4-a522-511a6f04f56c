package com.sgs.customerbiz.app.process;

import com.sgs.customerbiz.app.record.FlowInfo;
import com.sgs.customerbiz.app.record.ProcessRecordManager;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.core.log.APILog;
import com.sgs.customerbiz.core.log.RecordLogManager;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class ProcessFlowManager extends RecordLogManager {
    @Resource
    private FlowExecutor flowExecutor;
    @Resource
    private  ProcessRecordManager processRecordManager;

    public LiteflowResponse executeFlow(ProcessTypeEnum processType,String chainName, RequestContext requestContext) {
        FlowInfo flowInfo = null;
        try {

            // 执行流程
            LiteflowResponse response = flowExecutor.execute2Resp(chainName, null, requestContext);
            String traceabilityId = MDC.get("traceabilityId");
            requestContext.setTraceabilityId(traceabilityId);
            log.info("executeFlow traceabilityId={},chainName={},requestContext={},response={}",traceabilityId,chainName,requestContext,response);
            flowInfo = FlowInfo.builder().processType(processType)
                            .requestContext(requestContext).response(response)
                            .build();

            return response;
        }catch (Exception e){
            log.error("executeFlow error,chainName={},requestContext={}",chainName,requestContext,e);
        }finally {
            putRecord2Queue(flowInfo);
        }
        return null;
    }

    @Override
    public void recordLog(APILog apiLog) {
        // 异步记录流程日志
        processRecordManager.recordAsync(apiLog);
    }
}
