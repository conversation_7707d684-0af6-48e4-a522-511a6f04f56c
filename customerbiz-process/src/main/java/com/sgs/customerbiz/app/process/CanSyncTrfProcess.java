package com.sgs.customerbiz.app.process;

import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.yomahub.liteflow.flow.LiteflowResponse;
import org.springframework.stereotype.Component;

@Component
public class CanSyncTrfProcess implements BaseProcess<Boolean, TrfSyncReq>{

    private final ProcessFlowManager processFlowManager;

    public CanSyncTrfProcess(ProcessFlowManager processFlowManager) {
        this.processFlowManager = processFlowManager;
    }

    @Override
    public Boolean doProcess(TrfSyncReq object) {
        SyncTRFContext syncTRFContext = new SyncTRFContext();
        syncTRFContext.setSyncReq(object);
        syncTRFContext.setAction(object.getAction());
        String chainName = "CanSync2TRF";
        syncTRFContext.setChainName(chainName);
        // 执行流程
        LiteflowResponse response = processFlowManager.executeFlow(ProcessTypeEnum.CAN_SYNC_TRF,chainName, syncTRFContext);
        if (response.isSuccess()) {
            return true;
        } else {
            Exception ex = response.getCause();
            if (ex instanceof BizException) {
                BizException bizException = (BizException) ex;
                throw bizException;
            } else {
                ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.IMPORTPROCESS, ErrorFunctionTypeEnum.PROCESS, ErrorTypeEnum.SYSTEMERROR);
                throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(), ex.getMessage());
            }
        }
    }
}
