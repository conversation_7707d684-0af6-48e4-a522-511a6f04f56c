package com.sgs.customerbiz.app.component.validate.orderToTRF;

import com.sgs.customerbiz.app.component.validate.AbstractValidateComponent;
import com.sgs.customerbiz.biz.service.SciTrfBizService;
import com.sgs.customerbiz.context.Order2TRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.model.trf.dto.req.OrderToTrfReq;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
@Component
@LiteflowComponent(id = "orderToTRFValidateComponent", name = "order2TRF验证节点")
public class OrderToTRFValidateComponent extends AbstractValidateComponent {

    @Resource
    private SciTrfBizService sciTrfBizService;
    @Override
    protected void doPreProcess() throws Exception {

    }

    @Override
    protected void doPostProcess() throws Exception {

    }

    @Override
    public void doCoreProcess() throws Exception {
        Order2TRFContext orderContext = this.getContextBean(Order2TRFContext.class);
        checkData(orderContext);
    }

    private void checkData(Order2TRFContext orderContext) {
        OrderToTrfReq orderToTrfReq = (OrderToTrfReq) orderContext.getOrderToTrfReq();
        sciTrfBizService.validateParamForOrderToTrf(orderToTrfReq);
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) {
        Order2TRFContext orderContext = this.getContextBean(Order2TRFContext.class);
        checkData(orderContext);
        return orderContext;
    }
}
