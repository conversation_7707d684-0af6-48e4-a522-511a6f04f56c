package com.sgs.customerbiz.app.component.action.syncTrf.unbind;

import com.sgs.customerbiz.app.component.action.BaseActionComponent;
import com.sgs.customerbiz.biz.service.synctrf.cmd.SyncUnBindActionExtPt;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.core.common.SciRequestContext;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.domain.domainevent.TrfEvent;
import com.sgs.customerbiz.domain.domainevent.TrfUnBindEvent;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.domain.enums.TrfEventTriggerBy;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.framework.model.enums.TrfStatusEnum;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
@LiteflowComponent(id = "syncUnBindEventComponent", name = "SyncUnBind事件通知")
public class SyncUnBindEventComponent extends BaseActionComponent {


    @Autowired
    private SyncUnBindActionExtPt syncUnBindActionExtPt;
    public void syncSingleTrfPostprocess(SyncTRFContext syncTRFContext,TrfStatusResult trfStatusResult, TrfFullDTO trfDOParam) {
//        if (!trfStatusResult.trfStatusChanged()) {
//            return;
//        }
////        TRF 状态回到New的时候才需要往外发布TrfUnBindEvent事件
//        if (Objects.equals(TrfStatusEnum.ToBeBound.getStatus(), trfStatusResult.getNewTrfStatus())) {
//            TrfEvent event = syncUnBindActionExtPt.getTrfEvent(trfStatusResult, trfDOParam);
//            syncTRFContext.getTrfEventList().add(event);
//        }
        syncUnBindActionExtPt.syncSingleTrfPostprocess(trfStatusResult, trfDOParam);
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        SyncTRFContext syncTRFContext = (SyncTRFContext)requestContext;
        TrfFullDTO trfDOParam = syncTRFContext.getTrfDOParam();
        TrfStatusResult trfStatusResult = syncTRFContext.getTrfStatusResult();
        TrfStatusControlDO trfStatusControlDO = syncTRFContext.getTrfStatusControlDO();
        syncSingleTrfPostprocess(syncTRFContext,trfStatusResult, trfDOParam);
        return syncTRFContext;
    }

    @Override
    public void process() throws Exception {
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        TrfFullDTO trfDOParam = syncTRFContext.getTrfDOParam();
        TrfStatusResult trfStatusResult = syncTRFContext.getTrfStatusResult();
        TrfStatusControlDO trfStatusControlDO = syncTRFContext.getTrfStatusControlDO();
        syncSingleTrfPostprocess(syncTRFContext,trfStatusResult, trfDOParam);
    }
}
