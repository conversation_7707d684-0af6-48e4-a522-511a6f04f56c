package com.sgs.customerbiz.app.component.action.syncTrf.cancel;

import com.alibaba.cola.catchlog.CatchAndLog;
import com.sgs.customerbiz.app.component.action.syncTrf.BaseSyncTRFActionComponent;
import com.sgs.customerbiz.biz.service.synctrf.cmd.SyncCancelActionExtPt;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfOrderDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.enums.TrfStatusEnum;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@LiteflowComponent(id = "syncCancelActionComponent", name = "syncCancel动作")
public class SyncCancelActionComponent extends BaseSyncTRFActionComponent {

    @Autowired
    private SyncCancelActionExtPt syncCancelActionExtPt;
    @Override
    public TrfStatusResult doStatusControl(TrfStatusControlDO trfStatusControlDO) {
        return syncCancelActionExtPt.doStatusControl(trfStatusControlDO);
    }

    @Override
    public void doStatusControlCustom(TrfFullDTO trfDOParam) {
        syncCancelActionExtPt.doStatusControlCustom(trfDOParam);
    }
}
