package com.sgs.customerbiz.app.component.convert;

import com.sgs.customerbiz.app.component.prehandle.BasePreHandleComponent;
import com.sgs.customerbiz.biz.service.importtrf.cmd.DefaultImportTrfActionExtPt;
import com.sgs.customerbiz.context.ImportToTRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.model.trf.dto.TrfDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * ImportToTRF转换后置处理组件 - 转换完成后的后置处理
 * 
 * <p><b>流程位置</b>：位于各子流程中，在testLineMappingComponent之后</p>
 * 
 * <p><b>核心功能</b>：
 * <ul>
 *   <li>执行转换完成后的后置处理逻辑</li>
 *   <li>根据refSystemId选择不同的后置处理逻辑</li>
 *   <li>调用afterConvertProcessor进行额外处理</li>
 *   <li>完成数据转换后的最后一步处理</li>
 * </ul>
 * </p>
 * 
 * <p><b>处理逻辑</b>：
 * <ol>
 *   <li>从上下文获取TrfDTO和导入请求</li>
 *   <li>调用DefaultImportTrfActionExtPt.afterConvert进行后置处理</li>
 *   <li>处理可能的数据校验、补充、标准化等操作</li>
 * </ol>
 * </p>
 * 
 * <p><b>扩展点处理</b>：
 * <ul>
 *   <li>根据refSystemId选择不同的后置处理实现</li>
 *   <li>可能包含客户特定的业务逻辑</li>
 *   <li>支持afterConvertProcessor链式处理</li>
 * </ul>
 * </p>
 * 
 * <p><b>调用的核心方法</b>：
 * <ul>
 *   <li>{@code DefaultImportTrfActionExtPt.afterConvert(TrfDTO, TrfImportReq)} - 主要后置处理逻辑</li>
 * </ul>
 * </p>
 * 
 * <p><b>后续流程</b>：后置处理完成后，流程将进入相应的CreateAction组件</p>
 * 
 * <AUTHOR> from Import2TRF_Main Process Analysis
 * @see com.sgs.customerbiz.biz.service.importtrf.cmd.DefaultImportTrfActionExtPt#afterConvert(TrfDTO, TrfImportReq)
 */
@Slf4j
@Component
@LiteflowComponent(id = "importToTRFAfterConvertComponent", name = "importToTRFConvert后置处理")
public class ImportToTRFAfterConvertComponent extends BasePreHandleComponent {

    /** 默认导入TRF操作扩展点 - 执行转换后的后置处理逻辑 */
    @Resource
    protected DefaultImportTrfActionExtPt defaultImportTrfActionExtPt;
    /**
     * 测试组件方法
     * 
     * <p>提供独立的组件测试能力，执行相同的后置处理逻辑</p>
     * 
     * @param requestContext 请求上下文
     * @return 处理后的上下文对象
     * @throws Exception 处理异常
     */
    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        ImportToTRFContext importToTRFContext = (ImportToTRFContext)requestContext;
        TrfImportReq importReq = importToTRFContext.getTrfImportReq();
        TrfDTO  trfDTO = importToTRFContext.getTrfDTO();
        defaultImportTrfActionExtPt.afterConvert(trfDTO, importReq);
        return requestContext;
    }

    /**
     * 组件处理入口
     * 
     * <p><b>执行流程</b>：
     * <ol>
     *   <li>从上下文获取TRF数据和导入请求</li>
     *   <li>调用扩展点执行后置处理</li>
     *   <li>处理可能的数据完善和验证</li>
     * </ol>
     * </p>
     * 
     * @throws Exception 处理过程中的异常
     */
    @Override
    public void process() throws Exception {
        ImportToTRFContext importToTRFContext = this.getContextBean(ImportToTRFContext.class);
        TrfImportReq importReq = importToTRFContext.getTrfImportReq();
        TrfDTO  trfDTO = importToTRFContext.getTrfDTO();
        defaultImportTrfActionExtPt.afterConvert(trfDTO, importReq);
    }
}
