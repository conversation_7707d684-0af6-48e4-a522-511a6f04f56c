package com.sgs.customerbiz.app.component.db;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sgs.config.api.dto.ConfigInfo;
import com.sgs.config.api.service.ConfigService;
import com.sgs.customerbiz.biz.exception.RejectImportException;
import com.sgs.customerbiz.biz.service.importtrf.cmd.DefaultImportTrfActionExtPt;
import com.sgs.customerbiz.biz.service.todolist.TodoListService;
import com.sgs.customerbiz.biz.utils.TempSendEmailExecutor;
import com.sgs.customerbiz.context.ImportToTRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.core.config.EmailRecipientConfig;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.core.util.AF;
import com.sgs.customerbiz.core.util.DateUtils;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfInfoExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfTodoInfoExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfInfoPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfTodoInfoPO;
import com.sgs.customerbiz.dfv.enums.ActiveIndicatorEnum;
import com.sgs.customerbiz.domain.domainobject.v2.TrfDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfHeaderDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfLabDOV2;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.integration.EmailClient;
import com.sgs.customerbiz.integration.dto.EMailRequest;
import com.sgs.customerbiz.model.trf.dto.TrfDTO;
import com.sgs.customerbiz.model.trf.dto.importtrfresp.TrfImportResult;
import com.sgs.customerbiz.model.trf.dto.req.GetCustomerConfigReq;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.customerbiz.model.trf.dto.resp.CustomerGeneralConfig;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.model.enums.TrfStatusEnum;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

@Slf4j
@Component("importToTrfSaveDBComponent")
@LiteflowComponent(id = "importToTrfSaveDBComponent", name = "ImportToTRF数据入库处理器")
public class ImportToTrfSaveDBComponent extends BaseSave2DBComponent {
    @Resource
    private TrfDomainService trfDomainService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Autowired
    protected TodoListService todoListService;
    @Autowired
    private DefaultImportTrfActionExtPt defaultImportTrfActionExtPt;
    @Autowired
    private ConfigClient configClient;
    @Autowired
    private EmailClient emailClient;
    @Autowired
    private ConfigService configService;
    @Autowired
    private EmailRecipientConfig emailRecipientConfig;
    @Autowired
    private TrfTodoInfoExtMapper trfTodoInfoExtMapper;

    @Override
    public void process() throws Exception {

        ImportToTRFContext importToTRFContext = this.getContextBean(ImportToTRFContext.class);
        saveData(importToTRFContext);
    }

    private void saveData(ImportToTRFContext importToTRFContext) {
        TrfImportReq importReq = importToTRFContext.getTrfImportReq();
        TrfDOV2 trfDOV2 = importToTRFContext.getTrfDOV2();
        TrfDTO sgsTRF = importToTRFContext.getTrfDTO();
        checkAllowUpdateOrThrow(trfDOV2);
        transactionTemplate.execute(status -> {
            TrfImportResult result  = saveTrf(sgsTRF,trfDOV2,importToTRFContext.getCustomerTrf(),importReq);
            importToTRFContext.setResult(result);
            return result;
        });
        Optional<Integer> refSystemIdOpt = Optional.ofNullable(trfDOV2).map(TrfDOV2::getHeader).map(TrfHeaderDOV2::getRefSystemId);
        Optional<String> trfNoOpt = Optional.ofNullable(trfDOV2).map(TrfDOV2::getHeader).map(TrfHeaderDOV2::getTrfNo).filter(StringUtils::isNotBlank);
        AF.forEach2((refSystemId, trfNo) -> {
            if(!RefSystemIdEnum.check(refSystemId,
                    RefSystemIdEnum.SGSMart, RefSystemIdEnum.F21, RefSystemIdEnum.JO_ANN, RefSystemIdEnum.Walmart, RefSystemIdEnum.Walmart_Group,
                    RefSystemIdEnum.Target,RefSystemIdEnum.BigLots,RefSystemIdEnum.DollarTree,RefSystemIdEnum.Veyer)) {
                return;
            }
            Optional.ofNullable(getTrfInfoPO(trfDOV2)).ifPresent(trfInfo -> {
                try {
                    TrfTodoInfoPO trfTodoInfoPO = new TrfTodoInfoPO();
                    trfTodoInfoPO.setTrfNo(trfNo);
                    trfTodoInfoPO.setProductLineCode(importReq.getProductLineCode());
                    trfTodoInfoPO.setRefSystemId(refSystemId);
                    trfTodoInfoPO.setLabCode(importReq.getLabCode());
                    trfTodoInfoPO.setContent(null);
                    trfTodoInfoPO.setCreatedBy(importReq.getOperator());
                    trfTodoInfoPO.setCreatedDate(DateUtils.getNow());
                    trfTodoInfoPO.setModifiedBy(importReq.getOperator());
                    trfTodoInfoPO.setModifiedDate(DateUtils.getNow());
                    TrfTodoInfoPO trfTodoInfo = trfTodoInfoExtMapper.getTrfTodoInfoList(refSystemId, trfNo);
                    if (trfTodoInfo == null && TrfStatusEnum.check(trfInfo.getStatus(), TrfStatusEnum.ToBeBound)) {
                        trfTodoInfoPO.setTrfStatus(TrfStatusEnum.ToBeBound.getStatus());
                        trfTodoInfoExtMapper.batchInsert(Lists.newArrayList(trfTodoInfoPO));
                    }
                } catch (Throwable t) {
                    //ignore
                }
            });

        }, refSystemIdOpt, trfNoOpt);

    }

    private void checkAllowUpdateOrThrow(TrfDOV2 trfDOV2) {
        TrfHeaderDOV2 trfHeaderDOV2 = trfDOV2.getHeader();
        TrfInfoPO trfInfoPO = getTrfInfoPO(trfDOV2);
        if(Objects.isNull(trfInfoPO)) {
            return;
        }
        GetCustomerConfigReq req = new GetCustomerConfigReq();
        req.setRefSystemId(trfHeaderDOV2.getRefSystemId());
        ConfigInfo customerConfig = configClient.getCustomerConfig(req);
        if (Func.isEmpty(customerConfig)) {
            return;
        }
        CustomerGeneralConfig customerGeneralConfig = JSON.parseObject(customerConfig.getConfigValue(), CustomerGeneralConfig.class);
        if (Func.isEmpty(customerGeneralConfig)) {
            return;
        }
        if(customerGeneralConfig.rejectImportForDiffLabCode() && Objects.equals(trfInfoPO.getActiveIndicator(), ActiveIndicatorEnum.Active.getStatus())) {
            String labCodeOfRequest = Optional.ofNullable(trfHeaderDOV2.getLab()).map(TrfLabDOV2::getLabCode).orElse(null);
            String labCodeOfDb = trfInfoPO.getLabCode();
            if(Objects.isNull(labCodeOfRequest) && Objects.isNull(labCodeOfDb)) {
                return;
            }
            if(Objects.isNull(labCodeOfRequest) || Objects.isNull(labCodeOfDb) || !Objects.equals(labCodeOfRequest, labCodeOfDb)) {
                ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(
                        ErrorCategoryEnum.BUSINESS_ERROR,
                        ErrorBizModelEnum.IMPORTPROCESS,
                        ErrorFunctionTypeEnum.PROCESS,
                        ErrorTypeEnum.DATANOTMATCH);
                if(emailRecipientConfig.isEnabledEmail()) {
                    try {
                        EMailRequest emailRequest = buildEmailRequest(trfDOV2, trfInfoPO);
                        TempSendEmailExecutor.asyncExecute(() -> emailClient.sendEmail(emailRequest));
                    } catch (Exception e) {
                        log.error("reject import trf send alert email error", e);
                    }
                }
                throw new RejectImportException(errorCode, "reject import trf for try update labCode from " + labCodeOfDb + " to " + labCodeOfRequest);
            }
        }
    }

    private EMailRequest buildEmailRequest(TrfDOV2 trfDOV2, TrfInfoPO trfInfo) {
        // Build email content
        String toLab = Optional.ofNullable(trfDOV2.getHeader()).map(TrfHeaderDOV2::getLab).map(TrfLabDOV2::getLabCode).orElse(null);
        String mailContent = "TRF No: "+trfInfo.getTrfNo()+" 发起了Lab 更新，"+trfInfo.getLabCode()+" -> "+toLab+"。请前往SMART进行Cancel TRF并手动触发重推流程";
        Set<String> supportEmailList = new HashSet<>();
        Optional.ofNullable(configService.getSupportEmailList(2)).ifPresent(supportEmailList::addAll);
        Optional.ofNullable(configService.getSupportEmailList(40)).ifPresent(supportEmailList::addAll);
        // Create base email request
        String profile = Optional.ofNullable(System.getProperty("spring.profiles.active")).orElse("");
        return EMailRequest.builder()
                .mailSubject("【SCI "+profile+" 】TRF No: "+trfInfo.getTrfNo()+" " +trfInfo.getLabCode()+" -> "+toLab+" 失败")
                .mailTo(supportEmailList)
                .mailText(mailContent)
                .build();
    }

    @Nullable
    private TrfInfoPO getTrfInfoPO(TrfDOV2 trfDOV2) {
        TrfHeaderDOV2 trfHeaderDOV2 = trfDOV2.getHeader();
        TrfInfoPO trfInfoPO = null;
        if (Objects.nonNull(trfHeaderDOV2.getTrfId())) {
            trfInfoPO = trfDomainService.selectByTrfId(trfHeaderDOV2.getTrfId());
        } else if (Func.isNotEmpty(trfHeaderDOV2.getRefSystemId()) && Func.isNotEmpty(trfHeaderDOV2.getTrfNo())) {
            trfInfoPO = trfDomainService.selectByTrfNo(trfHeaderDOV2.getRefSystemId(), trfHeaderDOV2.getTrfNo());
        }
        return trfInfoPO;
    }

//    protected TrfNewEvent publishTrfCreateEvent(TrfDTO trfDTO, TrfImportReq importReq) {
//        TrfNewEvent createEvent = new TrfNewEvent(TrfEventTriggerBy.CUSTOMER);
//
//        createEvent.setPayload(trfDTO);
//        createEvent.setRefSystemId(importReq.getRefSystemId());
//        createEvent.setProductLineCode(Func.isEmpty(ProductLineContextHolder.getProductLineCode()) ? importReq.getProductLineCode() : ProductLineContextHolder.getProductLineCode());
//        createEvent.setSystemId(importReq.getSystemId());
//        createEvent.setSourceId(importReq.getRequestId());
//        createEvent.setTrfNo(importReq.getTrfNo());
//        createEvent.setStatusFrom(TrfStatusEnum.New.getStatus());
//        createEvent.setStatusTo(TrfStatusEnum.New.getStatus());
//        createEvent.setOperator(importReq.getOperator());
//        createEvent.setOperationTime(importReq.getOperationTime());
//        return createEvent;
//    }

    private TrfImportResult saveTrf(TrfDTO sgsTRF,TrfDOV2 trfDOV2,String customerTRFJson, TrfImportReq importReq) {
        //todo todolist中的双写保存逻辑接口后续也需要重构，本次重构importTRF暂时不动
        defaultImportTrfActionExtPt.saveTodolistTRF(sgsTRF, customerTRFJson, importReq,needLabCode());
        // 2.save SCI TRF
        trfDomainService.createTrf(trfDOV2);
        return JSONObject.parseObject(JSONObject.toJSONString(sgsTRF), TrfImportResult.class);
    }
    protected boolean needLabCode() {
        return Boolean.TRUE;
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) {
        ImportToTRFContext importToTRFContext = (ImportToTRFContext)requestContext;
        saveData(importToTRFContext);
        return importToTRFContext;
    }
}
