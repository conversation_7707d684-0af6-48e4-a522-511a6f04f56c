package com.sgs.customerbiz.app.component.filter;

import com.sgs.customerbiz.biz.service.SciTrfBizService;
import com.sgs.customerbiz.context.Order2TRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.model.trf.dto.TrfFileDTO;
import com.sgs.customerbiz.model.trf.dto.req.OrderToTrfReq;
import com.sgs.framework.model.enums.ActiveType;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 过滤附件
 */
@Component
@LiteflowComponent(id = "orderToTrfAttachmentFilter", name = "order2TRF附件过滤")
public class OrderToTrfAttachmentFilter extends FilterComponent{


    @Resource
    private SciTrfBizService sciTrfBizService;
    @Override
    public void process() throws Exception {
        Order2TRFContext orderContext = this.getContextBean(Order2TRFContext.class);
        OrderToTrfReq orderToTrfReq = orderContext.getOrderToTrfReq();
        sciTrfBizService.filterData(orderToTrfReq);
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) {
        Order2TRFContext orderContext = (Order2TRFContext)requestContext;
        OrderToTrfReq orderToTrfReq = orderContext.getOrderToTrfReq();
        sciTrfBizService.filterData(orderToTrfReq);
        return requestContext;
    }
}
