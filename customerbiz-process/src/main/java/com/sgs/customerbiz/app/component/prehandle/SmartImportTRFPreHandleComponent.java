package com.sgs.customerbiz.app.component.prehandle;

import com.sgs.core.domain.UserInfo;
import com.sgs.customerbiz.context.ImportToTRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.core.util.UserHelper;
import com.sgs.customerbiz.integration.FrameWorkClient;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.Func;
import com.sgs.preorder.facade.model.info.LabInfo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.sgs.customerbiz.core.constants.Constants.USER_DEFAULT;
/**
 * Smart导入TRF预处理组件 - SmartImport2TRF流程的预处理节点
 * 
 * <p><b>流程位置</b>：位于SmartImport2TRF子流程的第一个节点</p>
 * 
 * <p><b>核心功能</b>：
 * <ul>
 *   <li>SGSmart系统专用的预处理逻辑</li>
 *   <li>用户信息初始化和验证</li>
 *   <li>实验室信息获取和设置</li>
 *   <li>业务单元信息处理</li>
 * </ul>
 * </p>
 * 
 * <p><b>Smart模式特点</b>：
 * <ul>
 *   <li>专为SGSmart系统设计，有特殊的业务逻辑</li>
 *   <li>需要特殊的用户信息处理</li>
 *   <li>需要从框架服务获取实验室信息</li>
 * </ul>
 * </p>
 * 
 * <p><b>处理逻辑</b>：
 * <ol>
 *   <li>获取当前用户信息</li>
 *   <li>如果用户信息为空，创建默认用户信息</li>
 *   <li>如果用户信息存在，获取实验室信息并设置相关参数</li>
 * </ol>
 * </p>
 * 
 * <p><b>用户信息处理</b>：
 * <ul>
 *   <li><b>用户为空</b>：创建默认用户，使用请求中的labCode</li>
 *   <li><b>用户存在</b>：获取实验室信息，设置labCode、labId、buCode</li>
 * </ul>
 * </p>
 * 
 * <p><b>关键参数</b>：
 * <ul>
 *   <li><code>UserHelper.getLocalUser()</code> - 获取当前用户信息</li>
 *   <li><code>reqObject.getLabCode()</code> - 请求中的实验室代码</li>
 *   <li><code>frameWorkClient.getLabByLabCode()</code> - 获取实验室信息</li>
 * </ul>
 * </p>
 * 
 * <p><b>调用的核心方法</b>：
 * <ul>
 *   <li>{@code UserHelper.getLocalUser()} - 获取当前用户</li>
 *   <li>{@code UserHelper.setLocalUser(UserInfo)} - 设置用户信息</li>
 *   <li>{@code FrameWorkClient.getLabByLabCode(String)} - 获取实验室信息</li>
 * </ul>
 * </p>
 * 
 * <p><b>后续流程</b>：预处理完成后，流程将进入smartImportTrfValidateComponent进行验证</p>
 * 
 * <AUTHOR> from Import2TRF_Main Process Analysis
 * @see com.sgs.customerbiz.core.util.UserHelper
 * @see com.sgs.customerbiz.integration.FrameWorkClient
 * @see com.sgs.customerbiz.app.component.validate.importTRF.SmartImportTrfValidateComponent
 */
@Slf4j
@Component
@LiteflowComponent(id = "smartImportTRFPreHandleComponent", name = "smartImportTRF预处理组件")
public class SmartImportTRFPreHandleComponent extends BasePreHandleComponent {
    /** 框架客户端 - 用于获取实验室信息和相关配置 */
    @Resource
    protected FrameWorkClient frameWorkClient;
    /**
     * 组件处理入口
     * 
     * <p>Smart模式下的预处理逻辑，主要处理用户信息和实验室信息</p>
     * 
     * @throws Exception 处理过程中的异常
     */
    @Override
    public void process() throws Exception {
        ImportToTRFContext importToTRFContext = this.getContextBean(ImportToTRFContext.class);
        TrfImportReq reqObject = importToTRFContext.getTrfImportReq();
        preHandler(reqObject);
    }

    /**
     * Smart模式预处理逻辑
     * 
     * <p><b>用户信息处理逻辑</b>：
     * <ol>
     *   <li>获取当前用户信息</li>
     *   <li>如果用户为空：创建默认用户并设置到上下文</li>
     *   <li>如果用户存在：获取实验室信息并更新请求参数</li>
     * </ol>
     * </p>
     * 
     * <p><b>默认用户创建</b>：使用USER_DEFAULT常量设置用户名和账号</p>
     * 
     * <p><b>实验室信息处理</b>：从框架服务获取完整的实验室信息并设置到请求中</p>
     * 
     * @param reqObject TRF导入请求对象
     * @throws BizException 当获取实验室信息失败时抛出
     */
    private void preHandler(TrfImportReq reqObject){
        UserInfo localUser = UserHelper.getLocalUser();
        if (Func.isEmpty(localUser)) {
            localUser = new UserInfo();
            localUser.setRegionAccount(USER_DEFAULT);
            localUser.setName(USER_DEFAULT);
            localUser.setCurrentLabCode(reqObject.getLabCode());
            UserHelper.setLocalUser(localUser);
        } else {
            LabInfo labByLabCode = frameWorkClient.getLabByLabCode(localUser.getCurrentLabCode());
            if (Func.isEmpty(labByLabCode)) {
                throw new BizException("Get LabInfo Error!");
            }
            reqObject.setLabCode(labByLabCode.getLaboratoryCode());
            reqObject.setLabId(Func.toInteger(labByLabCode.getLaboratoryID(), null));
            reqObject.setBuCode(labByLabCode.getProductLineAbbr());
        }
    }

    /**
     * 测试组件方法
     * 
     * <p>提供独立的组件测试能力，执行相同的Smart预处理逻辑</p>
     * 
     * @param requestContext 请求上下文
     * @return 处理后的上下文对象
     */
    @Override
    public RequestContext testComponent(RequestContext requestContext) {
        ImportToTRFContext importToTRFContext = (ImportToTRFContext)requestContext;
        TrfImportReq reqObject = importToTRFContext.getTrfImportReq();
        preHandler(reqObject);
        return importToTRFContext;
    }
}
