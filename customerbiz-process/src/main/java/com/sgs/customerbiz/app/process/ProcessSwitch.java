package com.sgs.customerbiz.app.process;

import com.sgs.customerbiz.core.config.nacos.LiteflowNacosConfig;
import com.sgs.framework.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ProcessSwitch {

    @Autowired
    private LiteflowNacosConfig liteflowNacosConfig;

    private static final String TEST_TOKEN_PREX = "testToken_";

    public boolean isEnableProcess()
    {
        return liteflowNacosConfig.isEnableProcess();
    }

    public boolean isEnableAction(String action,String refSystemId){
        if(Func.isEmpty(liteflowNacosConfig.getActionList())){
            return false;
        }
        //判断Action是否满足配置，同时FlowAction里的enable是true，且如果includeRefSystemIds不为空，则refSystemId必须包含在内
        return liteflowNacosConfig.getActionList().stream().filter(flowAction -> flowAction.getActionType().equals(action)).anyMatch(flowAction -> flowAction.isEnable() && (Func.isEmpty(flowAction.getIncludeRefSystemIds())|| flowAction.getIncludeRefSystemIds().contains(refSystemId)));
    }

    public String getRealRefSystemId(Integer refSystemId){
        Map<String, String> testRefSystemIdMap = liteflowNacosConfig.getTestFlow().getTestRefSystemIdMap();
        if(Func.isNotEmpty(testRefSystemIdMap) && testRefSystemIdMap.containsKey(refSystemId.toString())){
            return testRefSystemIdMap.get(refSystemId.toString());
        }
        return refSystemId.toString();
    }

    /**
     * 测试开关判定
     * 1. 先判定总开关和测试开关是否都打开
     * 2. 如果总开关和测试开关都打开，则判断测试token是否正确
     * @param testToken
     * @return
     */
    public boolean isTestEnable(String testToken){
        boolean testEnable = false;
        if(Func.isEmpty(liteflowNacosConfig.getTestFlow())){
            return false;
        }
        //判断总开关和测试开关是否都打开
        boolean testProcessEnable = liteflowNacosConfig.isEnableProcess() && liteflowNacosConfig.getTestFlow().isEnable();
        //如果总开关和测试开关都打开，则判断测试token是否正确
        if(testProcessEnable) {
            if (Func.isNotEmpty(liteflowNacosConfig.getTestFlow())) {
                String testTokenConfig = liteflowNacosConfig.getTestFlow().getTestToken();
                testEnable = Func.isNotEmpty(testTokenConfig) && testTokenConfig.equals(testToken);
            }
        }
        return testEnable;
    }

    public boolean isProcessSwitch(String action,Integer refSystemId,String testToken){
        if(refSystemId == null){
            return false;
        }
        if(Func.isEmpty(liteflowNacosConfig)){
            return false;
        }
        if(isEnableProcess()){
            return isEnableAction(action,String.valueOf(refSystemId)) || isTestEnable(testToken);
        }
        return false;
    }

}
