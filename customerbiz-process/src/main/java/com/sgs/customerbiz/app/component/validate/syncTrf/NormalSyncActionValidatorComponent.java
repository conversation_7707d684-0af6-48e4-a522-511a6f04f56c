package com.sgs.customerbiz.app.component.validate.syncTrf;

import cn.hutool.core.util.StrUtil;
import com.sgs.customerbiz.app.component.validate.AbstractValidateComponent;
import com.sgs.customerbiz.biz.helper.DfvHelper;
import com.sgs.customerbiz.biz.service.synctrf.validator.NormalSyncActionValidatorExtPt;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@LiteflowComponent(id = "normalSyncActionValidatorComponent", name = "syncTRF通用参数校验组件")
public class NormalSyncActionValidatorComponent extends AbstractValidateComponent {
    
    @Resource
    private NormalSyncActionValidatorExtPt normalSyncActionValidatorExtPt;

    @Override
    protected void doPreProcess() throws Exception {

    }

    @Override
    protected void doPostProcess() throws Exception {

    }

    @Override
    public void doCoreProcess() throws Exception {
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        normalSyncActionValidatorExtPt.validate(syncTRFContext.getSyncReq());
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        SyncTRFContext syncTRFContext = (SyncTRFContext) requestContext;
        normalSyncActionValidatorExtPt.validate(syncTRFContext.getSyncReq());
        return syncTRFContext;
    }
}
