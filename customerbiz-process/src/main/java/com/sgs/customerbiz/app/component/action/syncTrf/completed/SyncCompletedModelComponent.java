package com.sgs.customerbiz.app.component.action.syncTrf.completed;

import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeBooleanComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@LiteflowComponent(id = "syncCompletedModelComponent", name = "SyncCompleted的light模式判断")
public class SyncCompletedModelComponent extends NodeBooleanComponent {

    @Autowired
    private ConfigClient configClient;
    @Override
    public boolean processBoolean() throws Exception {
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        TrfFullDTO trfDOParam = syncTRFContext.getTrfDOParam();
        return syncTRFContext.isHostModel();
    }


    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        SyncTRFContext syncTRFContext = (SyncTRFContext)requestContext;
        TrfFullDTO trfDOParam = syncTRFContext.getTrfDOParam();
        return syncTRFContext;
    }

//    public boolean isLightModel(TrfFullDTO trfDOParam, SyncTRFContext syncTRFContext) {
//        // TODO 校验completed时数据完整性 - By Config校验
//        String productLineCode = Func.isEmpty(ProductLineContextHolder.getProductLineCode()) ? trfDOParam.getProductLineCode() : ProductLineContextHolder.getProductLineCode();
//        if(configClient.notLightMode(trfDOParam.getHeader().getRefSystemId(), trfDOParam.getSystemId(), productLineCode, trfDOParam.getOrder().getCustomerList())) {
//            return false;
//        }
//        return true;
//    }
}
