package com.sgs.customerbiz.app.component.action.importToTrf.customer;

import com.sgs.customerbiz.app.component.action.BaseActionComponent;
import com.sgs.customerbiz.biz.service.importtrf.cmd.DefaultImportTrfActionExtPt;
import com.sgs.customerbiz.context.ImportToTRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.yomahub.liteflow.annotation.LiteflowComponent;

@LiteflowComponent(id = "addRequestCtxToCustomerTrfComponent", name = "将ImportTrf请求的属性mix进customerTrf.content")
public class AddRequestCtxToCustomerTrfComponent extends BaseActionComponent {


    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        ImportToTRFContext ctx = (ImportToTRFContext) requestContext;
        TrfImportReq importReq = ctx.getTrfImportReq();
        String contentOriginal = ctx.getCustomerTrf();
        return ctx;
    }

    @Override
    public void process() throws Exception {
        ImportToTRFContext ctx = this.getContextBean(ImportToTRFContext.class);
        TrfImportReq importReq = ctx.getTrfImportReq();
        String contentOriginal = ctx.getCustomerTrf();
    }
}
