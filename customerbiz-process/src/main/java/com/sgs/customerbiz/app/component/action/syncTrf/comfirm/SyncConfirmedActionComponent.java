package com.sgs.customerbiz.app.component.action.syncTrf.comfirm;

import com.sgs.customerbiz.app.component.action.syncTrf.BaseSyncTRFActionComponent;
import com.sgs.customerbiz.biz.service.synctrf.cmd.SyncConfirmedActionExtPt;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfOrderDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.enums.TrfStatusEnum;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
@LiteflowComponent(id = "syncConfirmedActionComponent", name = "SyncConfirmed动作")
public class SyncConfirmedActionComponent extends BaseSyncTRFActionComponent {

    @Autowired
    private SyncConfirmedActionExtPt syncConfirmedActionExtPt;
    @Override
    public void doStatusControlCustom(TrfFullDTO trfDOParam) {
        syncConfirmedActionExtPt.doStatusControlCustom(trfDOParam);
    }
    @Override
    public TrfStatusResult doStatusControl(TrfStatusControlDO trfStatusControlDO) {
        return syncConfirmedActionExtPt.doStatusControl(trfStatusControlDO);
    }
}
