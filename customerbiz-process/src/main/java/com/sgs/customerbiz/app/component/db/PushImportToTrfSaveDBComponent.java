package com.sgs.customerbiz.app.component.db;

import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * PUSH模式数据入库组件 - SubChain_PushImport2TRF流程的数据保存节点
 * 
 * <p><b>流程位置</b>：位于SubChain_PushImport2TRF子流程中，在pushImportToTRFCreateActionComponent之后</p>
 * 
 * <p><b>核心功能</b>：
 * <ul>
 *   <li>将PUSH模式下创建的TRF数据保存到数据库</li>
 *   <li>继承了ImportToTrfSaveDBComponent的通用保存逻辑</li>
 *   <li>重写了needLabCode方法，指定PUSH模式不需要LabCode验证</li>
 * </ul>
 * </p>
 * 
 * <p><b>PUSH模式特点</b>：
 * <ul>
 *   <li>数据由客户系统主动推送，可信度较高</li>
 *   <li>不需要验证LabCode，简化保存流程</li>
 *   <li>复用父类的核心保存逻辑，保证数据一致性</li>
 * </ul>
 * </p>
 * 
 * <p><b>与父类的差异</b>：
 * <ul>
 *   <li><b>needLabCode()</b>：返回false，表示PUSH模式不需要LabCode验证</li>
 *   <li>其他所有逻辑都继承自ImportToTrfSaveDBComponent</li>
 * </ul>
 * </p>
 * 
 * <p><b>保存流程</b>：
 * <ol>
 *   <li>继承父类的主要保存逻辑</li>
 *   <li>跳过LabCode相关的验证步骤</li>
 *   <li>执行数据库事务操作</li>
 *   <li>处理异常和回滚</li>
 * </ol>
 * </p>
 * 
 * <p><b>数据库操作包括</b>：
 * <ul>
 *   <li>TRF基本信息入库</li>
 *   <li>客户信息入库</li>
 *   <li>产品信息入库</li>
 *   <li>测试项信息入库</li>
 *   <li>样品信息入库</li>
 * </ul>
 * </p>
 * 
 * <p><b>后续流程</b>：数据保存完成后，流程将进入pushEventNotifyActionComponent进行事件通知</p>
 * 
 * <AUTHOR> from Import2TRF_Main Process Analysis
 * @see ImportToTrfSaveDBComponent
 * @see com.sgs.customerbiz.app.component.action.event.PushEventNotifyActionComponent
 */
@Slf4j
@Component
@LiteflowComponent(value = "pushImportToTrfSaveDBComponent", name = "PushImportToTRF数据入库处理器")
public class PushImportToTrfSaveDBComponent extends ImportToTrfSaveDBComponent{

    /**
     * PUSH模式是否需要LabCode验证
     * 
     * <p><b>PUSH模式特点</b>：由于数据由客户系统主动推送，可信度较高，
     * 因此跳过LabCode验证步骤以提高处理效率</p>
     * 
     * <p>这个设置会影响父类中的验证逻辑，让PUSH模式下的数据保存更加简化</p>
     * 
     * @return false - PUSH模式不需要LabCode验证
     */
    @Override
    protected boolean needLabCode() {
        return Boolean.FALSE;
    }
}
