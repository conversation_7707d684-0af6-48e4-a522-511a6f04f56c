package com.sgs.customerbiz.app.component.mapping;

import com.sgs.config.api.service.ConfigService;
import com.sgs.customerbiz.app.component.router.BaseRouterComponent;
import com.sgs.customerbiz.biz.service.importtrf.cmd.DefaultImportTrfActionExtPt;
import com.sgs.customerbiz.context.ImportToTRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.facade.model.req.QueryTestLineMappingReq;
import com.sgs.customerbiz.facade.model.rsp.QueryTestLineMappingRsp;
import com.sgs.customerbiz.facade.model.rsp.ReturnListRsp;
import com.sgs.customerbiz.integration.LocalILayerClient;
import com.sgs.customerbiz.model.trf.dto.*;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.tool.utils.BeanUtil;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Component
@LiteflowComponent(id = "testLineMappingComponent", name = "testLine映射处理器")
public class TestLineMappingComponent extends BaseMappingComponent {

    @Autowired
    protected ConfigService configService;
    @Resource
    private DefaultImportTrfActionExtPt defaultImportTrfActionExtPt;

    @Override
    public void process() throws Exception {

        ImportToTRFContext importToTRFContext = this.getContextBean(ImportToTRFContext.class);
        TrfImportReq importReq = importToTRFContext.getTrfImportReq();
        TrfDTO trfDTO = importToTRFContext.getTrfDTO();
        defaultImportTrfActionExtPt.tlMapping(trfDTO, importReq);
        importToTRFContext.setTrfDTO(trfDTO);
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) {
        ImportToTRFContext importToTRFContext = (ImportToTRFContext)requestContext;
        TrfImportReq importReq = importToTRFContext.getTrfImportReq();
        TrfDTO trfDTO = importToTRFContext.getTrfDTO();
        defaultImportTrfActionExtPt.tlMapping(trfDTO, importReq);
        importToTRFContext.setTrfDTO(trfDTO);
        return requestContext;
    }
}
