package com.sgs.customerbiz.app.component.validate.importTRF;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.sgs.customerbiz.app.component.validate.AbstractValidateComponent;
import com.sgs.customerbiz.biz.service.importtrf.cmd.SGSMartImportTrfActionExtPt;
import com.sgs.customerbiz.context.ImportToTRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.facade.model.req.SyncTrfInfoReq;
import com.sgs.customerbiz.integration.CustomerConfigClient;
import com.sgs.customerbiz.integration.LocalILayerClient;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.extsystem.facade.model.customer.rsp.CustomerConfigRsp;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

import static com.sgs.customerbiz.core.constants.Constants.DETAIL;


@Component
@LiteflowComponent(id = "smartImportTrfValidateComponent", name = "smartImport2TRF验证节点")
public class SmartImportTrfValidateComponent extends AbstractValidateComponent {

    protected static final String RELATIONSHIP_LIST_PATH = "$.relationShipList[relType=1][0].systemId";
    @Resource
    private LocalILayerClient iLayerClient;

    @Resource
    private CustomerConfigClient customerConfigClient;
    @Resource
    private SGSMartImportTrfActionExtPt sgsMartImportTrfActionExtPt;
    @Override
    protected void doPreProcess() throws Exception {

    }

    @Override
    protected void doPostProcess() throws Exception {

    }

    @Override
    public void doCoreProcess() throws Exception {

        ImportToTRFContext importToTRFContext = this.getContextBean(ImportToTRFContext.class);
        TrfImportReq importReq = importToTRFContext.getTrfImportReq();
        sgsMartImportTrfActionExtPt.checkConfig(importReq);
        BaseResponse<JSONObject> rspResult = sgsMartImportTrfActionExtPt.setRefSystemId(importReq);
        importToTRFContext.setRspResult(rspResult);
    }

//    private void check(TrfImportReq reqObject){
//        CustomerConfigRsp conf = customerConfigClient.getCustomerConfig(reqObject.getRefSystemId(), ProductLineContextHolder.getProductLineCode());
//
//        if (conf == null) {
//            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.SYSTEM_ERROR, ErrorBizModelEnum.ABSTRACTCUSTOMERTRFIMPORT, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
//            throw new CustomerBizException(errorCode,ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "Get Customer Config Fail!");
//        }
//    }
//    private void setRefSystemId(ImportToTRFContext importToTRFContext,TrfImportReq reqObject){
//        SyncTrfInfoReq syncTrf = new SyncTrfInfoReq();
//        syncTrf.setRefSystemId(reqObject.getRefSystemId());
//        syncTrf.setProductLineCode("SL");
//        syncTrf.setTrfNo(reqObject.getTrfNo());
//
//        syncTrf.setTrfNo(String.format("%s?isAll=true", reqObject.getTrfNo()));
//        syncTrf.setAction("GetSGSMartTrfInfo");
//        syncTrf.setCustomerGroupCode("12");
//
//        BaseResponse<JSONObject> rspResult = iLayerClient.syncGetInfo(syncTrf, null);
//        if (!rspResult.isSuccess() || rspResult.getData() == null) {
//            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.SYSTEM_ERROR, ErrorBizModelEnum.ABSTRACTCUSTOMERTRFIMPORT, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.EXTERNALCALLFAILED);
//            throw new CustomerBizException(errorCode,ResponseCode.INTERNAL_SERVER_ERROR.getCode(),rspResult.getMessage());
//        }
//        Integer refSystemId = checkData(reqObject.getRefSystemId(), rspResult.getData(), reqObject.getTrfNo(), reqObject.getSource());
//        reqObject.setRefSystemId(refSystemId);
//        importToTRFContext.setRspResult(rspResult);
//    }
//    protected Integer checkData(Integer refSystemId, JSONObject data, String trfNo, String source) {
//        if (isEmpty(data)) {
//            return refSystemId;
//        }
//
//        JSONObject detailJsonObject = data.getJSONObject(DETAIL);
//
//        if (isEmpty(detailJsonObject)) {
//            return refSystemId;
//        }
//
//        Optional<Integer> systemIdOptional = getSystemIdFromJson(detailJsonObject, RELATIONSHIP_LIST_PATH);
//
//        int refSystemIdBySgsMart = RefSystemIdEnum.SGSMart.getRefSystemId();
//
//        if (systemIdOptional.isPresent()) {
//            refSystemIdBySgsMart = systemIdOptional.get();
//        }
//
//        if (!Objects.equals(refSystemIdBySgsMart, refSystemId) && Objects.equals(source,"todoList")) {
//            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.ABSTRACTCUSTOMERTRFIMPORT, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.DATANOTMATCH);
//            throw new CustomerBizException(errorCode,ResponseCode.FAIL.getCode(),buildErrorMessage(trfNo, refSystemId, refSystemIdBySgsMart));
//        }
//
//        return refSystemIdBySgsMart;
//    }
//    protected boolean isEmpty(JSONObject jsonObject) {
//        return jsonObject == null || jsonObject.isEmpty();
//    }
//    protected Optional<Integer> getSystemIdFromJson(JSONObject jsonObject, String jsonPath) {
//        try {
//            Object result = JSONPath.eval(jsonObject, jsonPath);
//            if (Func.isEmpty(result)) {
//                return Optional.empty();
//            }
//            String systemIdStr = result.toString();
//            return Optional.ofNullable(com.alibaba.druid.util.StringUtils.isEmpty(systemIdStr) ? null : Integer.parseInt(systemIdStr));
//        } catch (NumberFormatException e) {
//            return Optional.empty();
//        }
//    }
//
//    protected String buildErrorMessage(String trfNo, Integer expectedSystemId, Integer actualSystemId) {
//        return StrUtil.format("Trf No: {} 属于{}的Trf，无法在当前页面import! (Expected: {}, Actual: {})",
//                trfNo, RefSystemIdEnum.getRefSystemId(actualSystemId).getName(), expectedSystemId, actualSystemId);
//    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) {
        ImportToTRFContext importToTRFContext = (ImportToTRFContext)requestContext;
        TrfImportReq importReq = importToTRFContext.getTrfImportReq();
        ProductLineContextHolder.setProductLineCode(importReq.getProductLineCode());

        sgsMartImportTrfActionExtPt.checkConfig(importReq);
        BaseResponse<JSONObject> rspResult = sgsMartImportTrfActionExtPt.setRefSystemId(importReq);
        importToTRFContext.setRspResult(rspResult);
        return importToTRFContext;
    }
}
