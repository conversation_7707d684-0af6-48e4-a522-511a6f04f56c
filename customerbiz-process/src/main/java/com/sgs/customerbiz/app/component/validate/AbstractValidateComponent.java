package com.sgs.customerbiz.app.component.validate;

import com.sgs.customerbiz.app.component.test.ComponentTest;
import com.yomahub.liteflow.core.NodeComponent;

/**
 * 基础组件
 */
public abstract class AbstractValidateComponent extends NodeComponent implements ComponentTest {

    public void process() throws Exception {
        doPreProcess();
        doCoreProcess();
        doPostProcess();
    }

    protected abstract void doPreProcess() throws Exception;
    protected abstract void doPostProcess() throws Exception;

    public abstract void doCoreProcess() throws Exception;
}
