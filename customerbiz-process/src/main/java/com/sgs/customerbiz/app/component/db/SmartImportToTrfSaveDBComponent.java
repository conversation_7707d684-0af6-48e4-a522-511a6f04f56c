package com.sgs.customerbiz.app.component.db;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.sgs.customerbiz.biz.convertor.TrfConvertor;
import com.sgs.customerbiz.biz.service.todolist.TodoListService;
import com.sgs.customerbiz.context.ImportToTRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.domain.domainobject.v2.TrfDOV2;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.domain.enums.TrfOrderRelationshipRuleEnum;
import com.sgs.customerbiz.facade.model.trf.sgsmart.*;
import com.sgs.customerbiz.model.trf.dto.*;
import com.sgs.customerbiz.model.trf.dto.importtrfresp.TrfImportResult;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.customerbiz.model.trf.enums.TrfSourceType;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Component
@LiteflowComponent(id = "smartImportToTrfSaveDBComponent", name = "smartImportToTrf数据入库处理器")
public class SmartImportToTrfSaveDBComponent extends BaseSave2DBComponent{

    @Resource
    private TodoListService todoListService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private TrfDomainService trfDomainService;
    @Override
    public void process() throws Exception {

        ImportToTRFContext importToTRFContext = this.getContextBean(ImportToTRFContext.class);
        saveData(importToTRFContext);
    }

    private void saveData(ImportToTRFContext importToTRFContext) {
        TrfImportReq reqObject = importToTRFContext.getTrfImportReq();
        BaseResponse<JSONObject> rspResult = importToTRFContext.getRspResult();
        transactionTemplate.execute(transactionStatus -> {
            TrfImportResult result = saveTRFToDB(reqObject, rspResult);
            importToTRFContext.setResult(result);
            return result;
        });
    }

    private TrfImportResult saveTRFToDB(TrfImportReq reqObject, BaseResponse<JSONObject> rspResult){

        //保存trf原始数据结构
        saveTodoListToDB(reqObject, rspResult);
        //构建TRF对象
        TrfDTO trf = buildTrf(reqObject, rspResult);
        /**
         * TODO
         * 1、保存原始JSON数据
         * 2、保存标准数据
         */
        TrfDOV2 trfParam = TrfConvertor.toTrfDOV2(trf);
//        // TODO 无法保存
        trfDomainService.createTrf(trfParam);
        TrfImportResult importResult = JSONObject.parseObject(JSONObject.toJSONString(trf), TrfImportResult.class);
        return importResult;
    }

    private @Nullable TrfDTO buildTrf(TrfImportReq reqObject, BaseResponse<JSONObject> rspResult) {
        TrfDetailTempDto trfDetail = JSONObject.toJavaObject(rspResult.getData(), TrfDetailTempDto.class);
        if (trfDetail == null || trfDetail.getDetail() == null) {
            // TODO Error Log

            return null;
        }
        TrfSgsMartTempDto temp = trfDetail.getDetail();

        TrfDTO trf = new TrfDTO();

        // region 【Header】

        TrfHeaderTempDto head = temp.getHeaders();
        TrfHeaderDTO header = new TrfHeaderDTO();
        header.setSystemId(reqObject.getSystemId());

        BeanUtils.copyProperties(head, header);
        header.setRefSystemId(reqObject.getRefSystemId());
        header.setSource(TrfSourceType.TRF2Order.getSourceType());
        //
        header.setIntegrationLevel(TrfOrderRelationshipRuleEnum.ONE_VS_MORE.getRule().toString());

        TrfLabTempDto lab = temp.getLab();
        if (lab != null) {
            TrfLabDTO trfLab = new TrfLabDTO();
            BeanUtils.copyProperties(lab, trfLab);
            trfLab.setBuCode(reqObject.getBuCode());
            trfLab.setBuId(reqObject.getBuId());
            trfLab.setLabCode(reqObject.getLabCode());
            trfLab.setLabId(Func.toLongObject(reqObject.getLabId(), null));
            trfLab.setOtherCode(lab.getLabCode());
            header.setLab(trfLab);
        }
        trf.setHeader(header);
        // endregion

        trf.setCustomerList(temp.getCustomerList());
        // endregion

        // region 【Service Requirement】

        trf.setServiceRequirement(this.getTrfServiceRequirementInfo(temp.getServiceRequirement()));
        // endregion
        Integer reportLanguage = null;
        if (Func.isNotEmpty(trf.getServiceRequirement()) && Func.isNotEmpty(trf.getServiceRequirement().getReport())) {
            TrfServiceRequirementDTO serviceRequirement = trf.getServiceRequirement();
            reportLanguage = serviceRequirement.getReport().getReportLanguage();
        }

        if (Func.isEmpty(reportLanguage)) {
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.ABSTRACTCUSTOMERTRFIMPORT, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
            throw new CustomerBizException(errorCode,ResponseCode.FAIL.getCode(),String.format("SGSMart TRF No: %s ReportLanguage Cannot Null!" + reqObject.getTrfNo()));
            //throw new BizException(String.format("SGSMart TRF No: %s ReportLanguage Cannot Null!" + reqObject.getTrfNo()));
        }

        // region 【Product List】

        trf.setProduct(temp.getProduct());
        // endregion
//
//        // region 【Sample List】
//
        trf.setSampleList(temp.getSampleList());
        // endregion

        // region 【Care Label List】

        trf.setCareLabelList(this.getCareLabelInfoList(temp.getProductSampleRspList()));
        // endregion

        // region 【Attachment List】

        trf.setAttachmentList(this.getAttachmentList(temp.getTrfAttachments()));
        return trf;
    }
    private void saveTodoListToDB(TrfImportReq reqObject, BaseResponse<JSONObject> rspResult){
        CustomResult customResult = todoListService.importTrfInfoData(reqObject.getTrfNo(), reqObject.getRefSystemId(), rspResult.getData(), true);
        boolean success = customResult.isSuccess();
        if (!success) {
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.SYSTEM_ERROR, ErrorBizModelEnum.ABSTRACTCUSTOMERTRFIMPORT, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.EXTERNALCALLFAILED);
            //throw new BizException(customResult.getMsg());
            throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(),customResult.getMsg());
        }
    }
    private TrfServiceRequirementDTO getTrfServiceRequirementInfo(TrfServiceRequirementTempDto serviceRequirement) {
        if (serviceRequirement == null) {
            return null;
        }
        TrfServiceRequirementDTO trfServiceRequirement = new TrfServiceRequirementDTO();

        // region 【Report】
        TrfServiceRequirementReportDTO report = new TrfServiceRequirementReportDTO();
        report.setReportLanguage(serviceRequirement.getReportLanguage());
        report.setReportHeader(serviceRequirement.getReportHeaderEn());
        report.setReportAddress(serviceRequirement.getReportAddressEn());

        // Language
        List<TrfServiceRequirementLangDTO> reportLangs = Lists.newArrayList();
        TrfServiceRequirementLangDTO reportLang = new TrfServiceRequirementLangDTO();
        reportLang.setLanguageId(2);
        reportLang.setReportHeader(serviceRequirement.getReportHeader());
        reportLang.setReportAddress(serviceRequirement.getReportAddress());

        reportLangs.add(reportLang);
        report.setLanguageList(reportLangs);

        // softcopyDelivery
        TrfDeliveryDTO softcopyDelivery = new TrfDeliveryDTO();
        softcopyDelivery.setDeliveryTo(serviceRequirement.getReportDeliveredTo());
        report.setSoftcopy(softcopyDelivery);

        // hardcopyDelivery
        TrfDeliveryDTO hardcopyDelivery = new TrfDeliveryDTO();
        hardcopyDelivery.setRequired(serviceRequirement.getIsHardCopy());
        report.setHardcopy(hardcopyDelivery);

        trfServiceRequirement.setReport(report);
        // endregion

        // region 【Invoice】
        TrfServiceRequirementInvoiceDTO invoice = new TrfServiceRequirementInvoiceDTO();


        // endregion

        // region 【Sample】
        TrfServiceRequirementSampleDTO sample = new TrfServiceRequirementSampleDTO();
        TrfDeliveryDTO testedSampleDelivery = new TrfDeliveryDTO();
        testedSampleDelivery.setRequired(serviceRequirement.getReturnSampleTestRequirement());
        sample.setReturnTestSample(testedSampleDelivery);

        TrfDeliveryDTO residueSampleDelivery = new TrfDeliveryDTO();
        residueSampleDelivery.setRequired(serviceRequirement.getReturnSampleResidueRequirement());
        sample.setReturnResidueSample(residueSampleDelivery);

        trfServiceRequirement.setSample(sample);
        // endregion

        return trfServiceRequirement;
    }
    /**
     * @param productSamples
     * @return
     */
    private TrfProductDTO getTrfProductInfo(List<TrfProductSampleListTempDto> productSamples, Integer reportLanguage) {
        if (productSamples == null || productSamples.isEmpty()) {
            return null;
        }
        if (Objects.equals(reportLanguage, LanguageType.EnglishAndChinese.getLanguageId())) {
            reportLanguage = LanguageType.English.getLanguageId();
        }
        Integer finalReportLanguage = reportLanguage;
        TrfProductSampleListTempDto trfProductSampleListTempDto = productSamples.stream().filter(l -> Objects.equals(l.getLanguageID(), finalReportLanguage)).findFirst().orElse(null);
        // 非默认语言
//        List<TrfProductSampleListTempDto> otherProductSampleList = productSamples.stream().filter(l -> !Objects.equals(l.getLanguageID(), LanguageType.English.getLanguageId())).collect(Collectors.toList());
        ArrayListMultimap<String, TrfProductAttrDTO> listMultimap = ArrayListMultimap.create();
        for (TrfProductSampleListTempDto productSampleListTempDto : productSamples) {
            TrfProductSampleTempDto productItem = productSampleListTempDto.getProduct();
            Map<String, Object> attrMap = BeanUtil.beanToMap(productItem, false, true);
            for (Map.Entry<String, Object> attrEntry : attrMap.entrySet()) {
                String value = Func.toStr(attrEntry.getValue(), null);
                if (StrUtil.length(value) == 0) {
                    continue;
                }
                TrfProductAttrDTO trfProductAttrDTO = new TrfProductAttrDTO();
                trfProductAttrDTO.setLanguageId(productSampleListTempDto.getLanguageID());
                trfProductAttrDTO.setLabelCode(attrEntry.getKey());
                trfProductAttrDTO.setLabelValue(value);
                listMultimap.put(attrEntry.getKey(), trfProductAttrDTO);
            }
        }

        TrfProductDTO trfProducts = new TrfProductDTO();
        if (Func.isNotEmpty(trfProductSampleListTempDto)) {
            List<TrfProductAttrDTO> attrDTOList = new ArrayList<>();
            trfProducts.setProductAttrList(attrDTOList);
            TrfProductSampleTempDto product = trfProductSampleListTempDto.getProduct();
            if (Func.isEmpty(product)) {
                product = new TrfProductSampleTempDto();
            }
            trfProducts.setTemplateId(product.getdFFFormID());
            int i = 0;
            Map<String, Object> attrMap = BeanUtil.beanToMap(product, false, true);
            for (Map.Entry<String, Object> attrEntry : attrMap.entrySet()) {
                String value = Func.toStr(attrEntry.getValue(), null);

                List<TrfProductAttrDTO> list = listMultimap.get(attrEntry.getKey());
                if (StrUtil.length(value) == 0 && Func.isEmpty(list)) { //多余的判断
                    continue;
                }
                TrfProductAttrDTO trfProductAttrDTO = new TrfProductAttrDTO();
                trfProductAttrDTO.setProductInstanceId(product.getId());
                trfProductAttrDTO.setLanguageId(trfProductSampleListTempDto.getLanguageID());
                trfProductAttrDTO.setAttrSeq(i++);
                trfProductAttrDTO.setLabelCode(attrEntry.getKey());
                trfProductAttrDTO.setLabelValue(value);
                trfProductAttrDTO.setFieldCode(attrEntry.getKey());
                if (Func.isNotEmpty(list)) { //多余的判断
                    List<TrfProductAttrLangDTO> productAttrLangDTOS = new ArrayList<>();
                    list.forEach(
                            l -> {
                                TrfProductAttrLangDTO productAttrLangDTO = new TrfProductAttrLangDTO();
                                productAttrLangDTO.setLanguageId(l.getLanguageId());
//                                    productAttrLangDTO.setLabelName();
                                productAttrLangDTO.setLabelValue(l.getLabelValue());
//                                    productAttrLangDTO.setCustomerLabel();
                                productAttrLangDTOS.add(productAttrLangDTO);
                            }
                    );
                    trfProductAttrDTO.setLanguageList(productAttrLangDTOS);
                }
                attrDTOList.add(trfProductAttrDTO);
            }
        }

        return trfProducts;
    }

    private List<TrfSampleDffDTO> getSampleList(List<TrfProductSampleListTempDto> productSamples) {
        if (productSamples == null || productSamples.isEmpty()) {
            return null;
        }
        List<TrfSampleDffDTO> trfSampleDffDTOS = Lists.newArrayList();
        List<TrfProductSampleTempDto> sampleTempDtos = productSamples.stream().map(l -> {
            List<TrfProductSampleTempDto> list = new ArrayList<>();
            List<TrfProductSampleTempDto> samples = l.getProductSamples();
            if (Func.isNotEmpty(samples)) {
                list.addAll(samples);
            }
            return list;
        }).findFirst().orElse(null);
        if (Func.isNotEmpty(sampleTempDtos)) {

            ArrayListMultimap<String, TrfSampleAttrDTO> sampleMap = ArrayListMultimap.create();
            for (TrfProductSampleListTempDto sampleListTempDto : productSamples) {
                Integer languageID = sampleListTempDto.getLanguageID();
                List<TrfProductSampleTempDto> samples = sampleListTempDto.getProductSamples();
                for (TrfProductSampleTempDto sample : samples) {
                    Map<String, Object> productAttrList = BeanUtil.beanToMap(sample, false, true);
                    for (Map.Entry<String, Object> attrPair : productAttrList.entrySet()) {
                        String value = Func.toStr(attrPair.getValue(), null);
                        if (StrUtil.length(value) == 0) {
                            continue;
                        }
                        TrfSampleAttrDTO trfProductAttrDTO = new TrfSampleAttrDTO();
                        trfProductAttrDTO.setLanguageId(languageID);
                        String fieldCode = StrUtil.lowerFirst(attrPair.getKey());
                        trfProductAttrDTO.setLabelCode(fieldCode);
//                        trfProductAttrDTO.setLabelName();
                        trfProductAttrDTO.setLabelValue(StrUtil.toString(attrPair.getValue()));
//                        trfProductAttrDTO.setDataType();
//                        trfProductAttrDTO.setCustomerLabel();
                        sampleMap.put(fieldCode, trfProductAttrDTO);
                    }
                }
            }


            for (TrfProductSampleTempDto sample : sampleTempDtos) {
                TrfSampleDffDTO sampleDffDTO = new TrfSampleDffDTO();
                trfSampleDffDTOS.add(sampleDffDTO);
//                    sampleDffDTO.setSampleInstanceId();
//                    sampleDffDTO.setSampleNo();
//                    sampleDffDTO.setExternalSampleNo();
                sampleDffDTO.setTemplateId(sample.getdFFFormID());
                Map<String, Object> productAttrList = BeanUtil.beanToMap(sample, false, true);
                if (Func.isEmpty(productAttrList)) {
                    continue;
                }

                List<TrfSampleAttrDTO> attrList = new ArrayList<>();
                sampleDffDTO.setSampleAttrList(attrList);

                int i = 0;
                for (Map.Entry<String, Object> attrPair : productAttrList.entrySet()) {
                    String value = Func.toStr(attrPair.getValue(), null);
                    String fieldCode = StrUtil.lowerFirst(attrPair.getKey());
                    List<TrfSampleAttrDTO> trfSampleAttrDTOS = sampleMap.get(fieldCode);
                    if (StrUtil.length(value) == 0 && Func.isEmpty(trfSampleAttrDTOS)) {
                        continue;
                    }

                    TrfSampleAttrDTO trfProductAttrDTO = new TrfSampleAttrDTO();
                    trfProductAttrDTO.setSampleInstanceId(sample.getId());
//                        trfProductAttrDTO.setLanguageId(trfProductSampleListTempDto.getLanguageID());
                    trfProductAttrDTO.setAttrSeq(i++);
                    trfProductAttrDTO.setLabelCode(fieldCode);
//                        trfProductAttrDTO.setLabelName();
                    trfProductAttrDTO.setLabelValue(StrUtil.toString(attrPair.getValue()));
                    trfProductAttrDTO.setFieldCode(fieldCode);
//                        trfProductAttrDTO.setDataType();
//                        trfProductAttrDTO.setCustomerLabel();
                    if (Func.isNotEmpty(trfSampleAttrDTOS)) {
                        List<TrfProductAttrLangDTO> list = new ArrayList<>();
                        trfSampleAttrDTOS.forEach(
                                l -> {
                                    TrfProductAttrLangDTO trfProductAttrLangDTO = new TrfProductAttrLangDTO();
//                                        trfProductAttrLangDTO.setLabelName();
                                    trfProductAttrLangDTO.setLabelValue(l.getLabelValue());
//                                        trfProductAttrLangDTO.setCustomerLabel();
                                    trfProductAttrLangDTO.setLanguageId(l.getLanguageId());
                                    list.add(trfProductAttrLangDTO);
                                }
                        );
                        trfProductAttrDTO.setLanguageList(list);
                    }

                    attrList.add(trfProductAttrDTO);
                }
            }
//            }
        }
        return trfSampleDffDTOS;
    }
    /**
     * @param trfAttachments
     * @return
     */
    private List<TrfFileDTO> getAttachmentList(List<TrfAttachmentTempDto> trfAttachments) {
        if (trfAttachments == null || trfAttachments.isEmpty()) {
            return null;
        }
        List<TrfFileDTO> attachmentList = Lists.newArrayList();

        for (TrfAttachmentTempDto trfAttachment : trfAttachments) {
            TrfFileDTO trfFile = new TrfFileDTO();
            trfFile.setFileName(trfAttachment.getFileName());
            trfFile.setFilePath(trfAttachment.getCloudId());

            attachmentList.add(trfFile);
        }
        return attachmentList;
    }

    /**
     * @param productSamples
     * @return
     */
    private List<TrfCareLabelDTO> getCareLabelInfoList(List<TrfProductSampleListTempDto> productSamples) {
        if (productSamples == null || productSamples.isEmpty()) {
            return null;
        }
        List<TrfCareLabelDTO> careLabelList = Lists.newArrayList();

        for (TrfProductSampleListTempDto productSample : productSamples) {
            List<TrfCareLabelTempDto> careLabels = productSample.getCareLabels();
            if (careLabels == null || careLabels.isEmpty()) {
                continue;
            }
            List<TrfProductSampleTempDto> trfProductSamples = productSample.getProductSamples();
            if (trfProductSamples == null || trfProductSamples.isEmpty()) {
                continue;
            }
            for (TrfCareLabelTempDto careLabel : careLabels) {
                Set<String> productItemNos = careLabel.getProductItemNo();
                if (productItemNos == null || productItemNos.isEmpty()) {
                    continue;
                }
                TrfCareLabelDTO trfCareLabel = new TrfCareLabelDTO();
                BeanUtils.copyProperties(careLabel, trfCareLabel);

                if (trfCareLabel.getSampleIds() == null) {
                    trfCareLabel.setSampleIds(Lists.newArrayList());
                }
//                List<TrfCareLabelSampleDTO> careLabelSampleList = trfCareLabel.getCareLabelSampleList();
                List<String> testSampleIds = trfCareLabel.getSampleIds();

                trfProductSamples.forEach(ps -> {
                    if (!productItemNos.contains(ps.getProductItemNo())) {
                        return;
                    }
                    testSampleIds.add(ps.getId());
                });
                careLabelList.add(trfCareLabel);
            }
        }
        return careLabelList;
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) {
        ImportToTRFContext importToTRFContext = (ImportToTRFContext)requestContext;
        saveData(importToTRFContext);
        return importToTRFContext;
    }
}
