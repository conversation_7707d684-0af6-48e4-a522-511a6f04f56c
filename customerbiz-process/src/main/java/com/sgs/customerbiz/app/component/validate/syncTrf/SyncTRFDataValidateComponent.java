package com.sgs.customerbiz.app.component.validate.syncTrf;

import cn.hutool.core.util.StrUtil;
import com.sgs.customerbiz.app.component.validate.AbstractValidateComponent;
import com.sgs.customerbiz.biz.exception.RepeatSyncCompletedException;
import com.sgs.customerbiz.biz.service.synctrf.cmd.BaseSyncTrfAction;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.ErrorAssert;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfInfoPO;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.domain.enums.TrfActionEnum;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.customerbiz.model.trf.enums.PendingFlagEnum;
import com.sgs.customerbiz.model.trf.enums.TrfStatusEnum;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
@LiteflowComponent(id = "syncTRFDataValidateComponent", name = "SyncTrfCheckData校验组件")
public class SyncTRFDataValidateComponent extends AbstractValidateComponent {

    @Resource
    protected BaseSyncTrfAction baseSyncTrfAction;
    @Override
    protected void doPreProcess() throws Exception {

    }

    @Override
    protected void doPostProcess() throws Exception {

    }

    @Override
    public void doCoreProcess() throws Exception {
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        TrfSyncReq syncReq = syncTRFContext.getSyncReq();
        TrfHeaderDTO trfHeaderDTO = this.getCurrLoopObj();
        trfHeaderDTO.setRefSystemId(syncReq.getHeader().getRefSystemId());
        syncTRFContext.setTrfHeaderDTO(trfHeaderDTO);
        checkDataInfo(syncTRFContext, trfHeaderDTO);
    }

    private void checkDataInfo(SyncTRFContext syncTRFContext, TrfHeaderDTO trfHeaderDTO) {
        TrfSyncReq syncReq = syncTRFContext.getSyncReq();
        TrfInfoPO trfInfoPO = baseSyncTrfAction.checkTrfinfoData(syncReq, trfHeaderDTO);
        syncTRFContext.setTrfInfoPO(trfInfoPO);
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        TrfHeaderDTO trfHeaderDTO = null;
        List<TrfHeaderDTO> trfList= syncTRFContext.getSyncReq().getHeader().getTrfList();
        if (Func.notNull(trfList)) {
            trfHeaderDTO = trfList.get(0);
            checkDataInfo(syncTRFContext, trfHeaderDTO);
            syncTRFContext.setTrfHeaderDTO(trfHeaderDTO);
        }
        return syncTRFContext;
    }
}
