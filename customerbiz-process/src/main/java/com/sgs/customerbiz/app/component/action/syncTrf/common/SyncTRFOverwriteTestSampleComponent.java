package com.sgs.customerbiz.app.component.action.syncTrf.common;

import com.sgs.customerbiz.app.component.action.BaseActionComponent;
import com.sgs.customerbiz.biz.convertor.TrfConvertor;
import com.sgs.customerbiz.biz.service.synctrf.cmd.BaseSyncTrfAction;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.domain.domainobject.v2.TrfDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfTestSampleDOV2;
import com.sgs.customerbiz.domain.domainservice.TrfTestSampleDomainService;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
@LiteflowComponent(id = "syncTRFOverwriteTestSampleComponent", name = "更新testSample信息")
public class SyncTRFOverwriteTestSampleComponent extends BaseActionComponent {

    @Resource
    private BaseSyncTrfAction syncTrfAction;
    @Resource
    private TrfTestSampleDomainService trfTestSampleDomainService;
    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        SyncTRFContext syncTRFContext = (SyncTRFContext)requestContext;
        TrfFullDTO trfDOParam = syncTRFContext.getTrfDOParam();
        TrfDOV2 trfDO = TrfConvertor.toTrfDOV2(trfDOParam);

        overwriteTrfTestSample(trfDO.getHeader().getTrfId(), trfDO.getTestSampleList());
        return syncTRFContext;
    }

    @Override
    public void process() throws Exception {
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        TrfFullDTO trfDOParam = syncTRFContext.getTrfDOParam();
        TrfDOV2 trfDO = TrfConvertor.toTrfDOV2(trfDOParam);

        overwriteTrfTestSample(trfDO.getHeader().getTrfId(), trfDO.getTestSampleList());
    }

    protected final void overwriteTrfTestSample(Long trfId, List<TrfTestSampleDOV2> testSampleDOList) {
        syncTrfAction.overwriteTrfTestSample(trfId, testSampleDOList);
    }
}
