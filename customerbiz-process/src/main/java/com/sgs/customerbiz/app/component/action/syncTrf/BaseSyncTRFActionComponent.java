package com.sgs.customerbiz.app.component.action.syncTrf;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.app.component.action.BaseActionComponent;
import com.sgs.customerbiz.biz.convertor.TrfConvertor;
import com.sgs.customerbiz.biz.convertor.TrfSyncConvertor;
import com.sgs.customerbiz.biz.service.ruleengine.RuleExecutor;
import com.sgs.customerbiz.biz.service.synctrf.SyncTrfContextHolder;
import com.sgs.customerbiz.biz.service.synctrf.cmd.BaseSyncTrfAction;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfInfoPO;
import com.sgs.customerbiz.domain.domainevent.TrfEvent;
import com.sgs.customerbiz.domain.domainevent.TrfPendingEvent;
import com.sgs.customerbiz.domain.domainevent.TrfUnPendingEvent;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfLabDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.domain.domainservice.*;
import com.sgs.customerbiz.domain.enums.TrfEventTriggerBy;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.TrfLabDTO;
import com.sgs.customerbiz.model.trf.dto.TrfSyncHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.customerbiz.model.trf.dto.resp.TrfSyncResult;
import com.sgs.customerbiz.model.trf.enums.PendingFlagEnum;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
public abstract class BaseSyncTRFActionComponent extends BaseActionComponent {

    @Resource
    protected TransactionTemplate transactionTemplate;

    @Autowired
    protected TrfLabDomainService trfLabDomainService;

    @Resource
    protected TrfInvoiceDomainService trfInvoiceDomainService;

    @Resource
    protected TrfDomainService trfDomainService;

    @Resource
    protected TrfOrderDomainService trfOrderDomainService;

    @Resource
    protected TrfQuotationDomainService trfQuotationDomainService;
    @Autowired
    private BaseSyncTrfAction baseSyncTrfAction;

    @Resource
    private RuleExecutor ruleExecutor;
    public boolean processByAction(TrfFullDTO trfDOParam,TrfStatusControlDO trfStatusControlDO, TrfSyncHeaderDTO syncHeader) {
        return true;
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        SyncTRFContext syncTRFContext = (SyncTRFContext)requestContext;
        doBaseProcess(syncTRFContext);
        return syncTRFContext;
    }

    @Override
    public void process() throws Exception {
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        doBaseProcess(syncTRFContext);
    }

    private void doBaseProcess(SyncTRFContext syncTRFContext) {
        TrfFullDTO trfDOParam = syncTRFContext.getTrfDOParam();
        TrfSyncReq syncReq = syncTRFContext.getSyncReq();
        TrfHeaderDTO trfHeaderDTO = syncTRFContext.getTrfHeaderDTO();
        TrfStatusControlDO trfStatusControlDO = syncTRFContext.getTrfStatusControlDO();

        String action = syncReq.getAction();
        boolean processByAction = processByAction(trfDOParam, trfStatusControlDO,syncReq.getHeader());
        if (!processByAction) {
            log.info("===================processByAction action:{}", action);
            syncTRFContext.setReturn(true);
            return;
        }
        baseSyncTrfAction.checkHostModelAndValidate(syncReq);
        baseSyncTrfAction.checkBizRule(syncReq, trfDOParam);

        //fixme doStatusControl里会有很多逻辑，事务太大数据库很容易拖垮数据库
        TrfLabDTO labDTO = trfHeaderDTO.getLab();
        TrfLabDOV2 trfLabDOV2 = JSONObject.parseObject(JSONObject.toJSONString(labDTO), TrfLabDOV2.class);
        TrfStatusResult trfStatusResult = transactionTemplate.execute(transactionStatus -> {
                    // update lab contact info
                    trfLabDomainService.updateTrfLab(trfLabDOV2, trfHeaderDTO.getTrfNo(), trfDOParam.getHeader().getRefSystemId());
                    TrfStatusResult result = doStatusControl(trfStatusControlDO);
                    if (result == null) {
                        transactionStatus.setRollbackOnly();
                        return null;
                    }
                    syncTRFContext.setTrfStatusResult(result);
                    doStatusControlCustom(trfDOParam);
                    return result;
                }
        );


        if (trfStatusResult == null) {
            log.error("status control error,trfStatusResult is null. syncReq:[{}]", JSON.toJSONString(syncReq));
            ErrorCode errorCode1 = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SYNCACTION, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.STATUSERROR);
            throw new CustomerBizException(errorCode1, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "status control error");
        }
        syncTRFContext.setTrfStatusResult(trfStatusResult);
    }

    protected TrfEvent newTrfPendingEvent(Integer pendingFlag) {
        if (PendingFlagEnum.check(pendingFlag, PendingFlagEnum.Pending)) {
            return new TrfPendingEvent(TrfEventTriggerBy.PREORDER);
        } else {
            return new TrfUnPendingEvent(TrfEventTriggerBy.PREORDER);
        }
    }

    public abstract TrfStatusResult doStatusControl(TrfStatusControlDO trfStatusControlDO);

    public abstract void doStatusControlCustom(TrfFullDTO trfDOParam);
}
