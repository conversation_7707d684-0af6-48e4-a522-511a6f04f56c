package com.sgs.customerbiz.app.component.action.syncTrf;

import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeForComponent;
import com.yomahub.liteflow.core.NodeIteratorComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.iterators.EmptyIterator;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;

@Slf4j
@LiteflowComponent(id = "syncTrfForActionComponent", name = "syncTrf列表循环组件")
public class SyncTrfForActionComponent extends NodeIteratorComponent {

    @Override
    public Iterator<?> processIterator() throws Exception {

        SyncTRFContext requestContext = this.getContextBean(SyncTRFContext.class);
        TrfSyncReq syncReq = requestContext.getSyncReq();
        List<TrfHeaderDTO> trfList = syncReq.getHeader().getTrfList();
        if (Func.notNull(trfList)){
            return trfList.iterator();
        }else{
            log.error("syncTrf列表为空,syncReq:{}",syncReq);
            return EmptyIterator.INSTANCE;
        }
    }
}
