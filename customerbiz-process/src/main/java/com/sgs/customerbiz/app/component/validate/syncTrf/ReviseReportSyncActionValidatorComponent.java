package com.sgs.customerbiz.app.component.validate.syncTrf;

import com.sgs.customerbiz.biz.service.synctrf.validator.ReviseReportSyncActionValidatorExtPt;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@LiteflowComponent(id = "reviseReportSyncActionValidatorComponent", name = "SyncReviseReport参数校验组件")
public class ReviseReportSyncActionValidatorComponent extends NormalSyncActionValidatorComponent{

    @Resource
    private ReviseReportSyncActionValidatorExtPt reviseReportSyncActionValidatorExtPt;

    @Override
    public void doCoreProcess() throws Exception {
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        reviseReportSyncActionValidatorExtPt.validate(syncTRFContext.getSyncReq());
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        SyncTRFContext syncTRFContext = (SyncTRFContext) requestContext;
        reviseReportSyncActionValidatorExtPt.validate(syncTRFContext.getSyncReq());
        return syncTRFContext;
    }
}