package com.sgs.customerbiz.app.component.validate.syncTrf;

import com.sgs.customerbiz.app.component.validate.AbstractValidateComponent;
import com.sgs.customerbiz.biz.service.ValidationBizService;
import com.sgs.customerbiz.biz.service.synctrf.cmd.BaseSyncTrfAction;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.domain.enums.TrfActionEnum;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@LiteflowComponent(id = "syncTRFCompletedValidateComponent", name = "SyncCompletedValidate")
public class SyncTRFCompletedValidateComponent extends AbstractValidateComponent {

    @Autowired
    private ValidationBizService validationBizService;
    @Autowired
    private ConfigClient configClient;
    @Autowired
    private BaseSyncTrfAction baseSyncTrfAction;
    @Override
    protected void doPreProcess() throws Exception {

    }

    @Override
    protected void doPostProcess() throws Exception {

    }

    @Override
    public void doCoreProcess() throws Exception {
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        validate(syncTRFContext);
    }

    private void validate(SyncTRFContext syncTRFContext) {
        TrfSyncReq syncReq = syncTRFContext.getSyncReq();
        if(baseSyncTrfAction.checkHostModelAndValidate(syncReq)){
            syncTRFContext.setHostModel(true);
        }
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        SyncTRFContext syncTRFContext = (SyncTRFContext)requestContext;
        validate(syncTRFContext);
        return syncTRFContext;
    }
}
