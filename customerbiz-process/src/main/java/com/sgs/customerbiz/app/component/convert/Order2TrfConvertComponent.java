package com.sgs.customerbiz.app.component.convert;

import com.sgs.customerbiz.biz.convertor.TrfConvertor;
import com.sgs.customerbiz.context.Order2TRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.domain.domainobject.v2.TrfDOV2;
import com.sgs.customerbiz.model.trf.dto.TrfDTO;
import com.sgs.customerbiz.model.trf.dto.req.OrderToTrfReq;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.stereotype.Component;

@Component
@LiteflowComponent(id = "order2TrfConvertComponent", name = "Order2TRF的转换组件")
public class Order2TrfConvertComponent extends BaseConvertComponent{
    @Override
    public void process() throws Exception {

        Order2TRFContext order2TRFContext = this.getContextBean(Order2TRFContext.class);
        OrderToTrfReq orderToTrfReq = order2TRFContext.getOrderToTrfReq();
        // 数据结构转化
        TrfDTO trfDTO = TrfConvertor.toTrfDTO(orderToTrfReq);
        TrfDOV2 trfParam = TrfConvertor.toTrfDOV2(trfDTO);
        order2TRFContext.setTrfDTO(trfDTO);
        order2TRFContext.setTrfDOV2(trfParam);
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) {
        Order2TRFContext order2TRFContext = (Order2TRFContext)requestContext;
        OrderToTrfReq orderToTrfReq = order2TRFContext.getOrderToTrfReq();
        // 数据结构转化
        TrfDTO trfDTO = TrfConvertor.toTrfDTO(orderToTrfReq);
        order2TRFContext.setTrfDTO(trfDTO);
        return order2TRFContext;
    }
}
