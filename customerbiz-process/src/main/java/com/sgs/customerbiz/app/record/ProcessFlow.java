package com.sgs.customerbiz.app.record;

import com.sgs.customerbiz.context.RequestContext;
import lombok.Data;
import org.slf4j.MDC;

import java.util.Date;
import java.util.List;

@Data
public class ProcessFlow {
    private Integer flowId;
    private String requestId;
    private String extId;
    private String chainId;
    private String status;
    private String errorCode;
    private String errorMessage;
    private Integer stepCount;
    private String stepInfo;
    private String traceabilityId;
    private List<ProcessFlowStep> processFlowStepList;
    private RequestContext requestContext;
}
