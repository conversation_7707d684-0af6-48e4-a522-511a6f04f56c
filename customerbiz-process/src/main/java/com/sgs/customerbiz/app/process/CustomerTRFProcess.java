package com.sgs.customerbiz.app.process;

import com.sgs.customerbiz.context.CustomerTRFContext;
import com.sgs.customerbiz.context.ImportToTRFContext;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.model.trf.dto.importtrfresp.TrfImportResult;
import com.sgs.customerbiz.model.trf.dto.req.TrfBindReq;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.yomahub.liteflow.flow.LiteflowResponse;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class CustomerTRFProcess  implements BaseProcess<TrfImportResult, TrfBindReq>{
    @Resource
    private ProcessFlowManager processFlowManager;
    @Override
    public TrfImportResult doProcess(TrfBindReq trfBindReq){
        CustomerTRFContext customerTRFContext = new CustomerTRFContext();
        customerTRFContext.setTrfBindReq(trfBindReq);
        String chainName = "customerTRFInfo";
        customerTRFContext.setChainName(chainName);
        // 执行流程
        LiteflowResponse response = processFlowManager.executeFlow(ProcessTypeEnum.CUSTOMER_TRF,chainName, customerTRFContext);
        if (response.isSuccess()) {
            ImportToTRFContext responseContext = response.getContextBean(ImportToTRFContext.class);
            return responseContext.getResult();
        }else{
            Exception ex = response.getCause();
            if(ex instanceof BizException){
                throw (BizException)ex;
            }else{
                ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.IMPORTPROCESS, ErrorFunctionTypeEnum.PROCESS, ErrorTypeEnum.SYSTEMERROR);
                throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(),ex.getMessage());
            }
        }
    }
}
