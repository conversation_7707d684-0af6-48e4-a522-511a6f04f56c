package com.sgs.customerbiz.app.component.validate.orderToTRF;

import com.sgs.customerbiz.app.component.validate.AbstractValidateComponent;
import com.sgs.customerbiz.biz.service.SciTrfBizService;
import com.sgs.customerbiz.context.Order2TRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfInfoPO;
import com.sgs.customerbiz.domain.domainobject.v2.TrfOrderDOV2;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.domain.enums.TrfOrderRelationshipRuleEnum;
import com.sgs.customerbiz.model.trf.dto.req.OrderToTrfReq;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 验证Trf是否存在
 */
@Component
@LiteflowComponent(id = "validateTRFExistComponent", name = "验证TRF是否存在")
public class ValidateTRFExistComponent extends AbstractValidateComponent {

    @Resource
    private SciTrfBizService sciTrfBizService;

    @Override
    protected void doPreProcess() throws Exception {

    }

    @Override
    protected void doPostProcess() throws Exception {

    }

    @Override
    public void doCoreProcess() throws Exception {
        Order2TRFContext orderContext = this.getContextBean(Order2TRFContext.class);
        OrderToTrfReq orderToTrfReq = orderContext.getOrderToTrfReq();
        sciTrfBizService.validateTrfExistForOrderToTrf(orderToTrfReq);
    }


    @Override
    public RequestContext testComponent(RequestContext requestContext) {
        Order2TRFContext orderContext = (Order2TRFContext)requestContext;
        OrderToTrfReq orderToTrfReq = orderContext.getOrderToTrfReq();
        sciTrfBizService.validateTrfExistForOrderToTrf(orderToTrfReq);
        return orderContext;
    }
}
