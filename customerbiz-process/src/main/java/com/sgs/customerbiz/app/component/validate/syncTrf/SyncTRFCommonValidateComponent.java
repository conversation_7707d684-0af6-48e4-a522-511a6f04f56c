package com.sgs.customerbiz.app.component.validate.syncTrf;

import com.alibaba.cola.extension.ExtensionExecutor;
import com.sgs.customerbiz.app.component.validate.AbstractValidateComponent;
import com.sgs.customerbiz.biz.helper.DfvHelper;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Slf4j
@Component
@LiteflowComponent(id = "syncTRFCommonValidateComponent", name = "syncTRF基础参数校验组件")
public class SyncTRFCommonValidateComponent extends AbstractValidateComponent {

    private static final String DFV_CODE_FUNC_LEVEL_SYNC_TRF = "sci-syncTrf";

    @Resource
    private DfvHelper dfvHelper;
    @Override
    protected void doPreProcess() throws Exception {

    }

    @Override
    protected void doPostProcess() throws Exception {

    }

    @Override
    public void doCoreProcess() throws Exception {
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        TrfSyncReq syncReq = syncTRFContext.getSyncReq();
        validate(syncReq);
    }
    private void validate(TrfSyncReq syncReq ) throws Exception {
        //基本参数验证
        dfvHelper.validateParams(syncReq, DFV_CODE_FUNC_LEVEL_SYNC_TRF);
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        SyncTRFContext syncTRFContext =(SyncTRFContext)requestContext;
        TrfSyncReq syncReq = syncTRFContext.getSyncReq();
        validate(syncReq);
        return requestContext;
    }
}
