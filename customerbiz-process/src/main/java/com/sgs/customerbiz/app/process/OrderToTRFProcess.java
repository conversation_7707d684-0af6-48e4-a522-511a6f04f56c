package com.sgs.customerbiz.app.process;

import com.sgs.customerbiz.context.Order2TRFContext;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.model.trf.dto.TrfNoDTO;
import com.sgs.customerbiz.model.trf.dto.req.OrderToTrfReq;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.yomahub.liteflow.flow.LiteflowResponse;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
@Component
public class OrderToTRFProcess implements BaseProcess<TrfNoDTO,OrderToTrfReq>{

    @Resource
    private ProcessFlowManager processFlowManager;

    @Override
    public TrfNoDTO doProcess(OrderToTrfReq orderToTrfReq) {
        Order2TRFContext trfContext = new Order2TRFContext();
        trfContext.setOrderToTrfReq(orderToTrfReq);
        String chainName = "order2TRF";
        LiteflowResponse response = processFlowManager.executeFlow(ProcessTypeEnum.ORDER_TO_TRF,chainName, trfContext);
        if (response.isSuccess()) {
            Order2TRFContext responseContext = response.getContextBean(Order2TRFContext.class);
            return responseContext.getTrfNoDTO();
        }else{
            Exception ex = response.getCause();
            if(ex instanceof BizException){
                throw (BizException)ex;
            }else{
                ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.IMPORTPROCESS, ErrorFunctionTypeEnum.PROCESS, ErrorTypeEnum.SYSTEMERROR);
                throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(),ex.getMessage());
            }
        }
    }

}
