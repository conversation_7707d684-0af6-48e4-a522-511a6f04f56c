package com.sgs.customerbiz.app.component.action.syncTrf.modify;

import com.sgs.customerbiz.app.component.action.syncTrf.BaseSyncTRFActionComponent;
import com.sgs.customerbiz.biz.service.synctrf.cmd.SyncModifyActionExtPt;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@LiteflowComponent(id = "syncModifyActionComponent", name = "SyncModify动作")
public class SyncModifyActionComponent extends BaseSyncTRFActionComponent {

    @Autowired
    private SyncModifyActionExtPt syncModifyActionExtPt;
    @Override
    public TrfStatusResult doStatusControl(TrfStatusControlDO trfStatusControlDO) {

        return syncModifyActionExtPt.doStatusControl(trfStatusControlDO);
    }

    @Override
    public void doStatusControlCustom(TrfFullDTO trfDOParam) {
        syncModifyActionExtPt.doStatusControlCustom(trfDOParam);
    }
}
