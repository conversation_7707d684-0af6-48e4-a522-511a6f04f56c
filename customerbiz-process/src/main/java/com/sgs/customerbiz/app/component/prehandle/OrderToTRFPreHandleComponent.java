package com.sgs.customerbiz.app.component.prehandle;

import com.sgs.customerbiz.biz.service.SciTrfBizService;
import com.sgs.customerbiz.context.Order2TRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.model.trf.dto.req.OrderToTrfReq;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@LiteflowComponent(id = "orderToTRFPreHandleComponent", name = "order2TRF预处理组件")
public class OrderToTRFPreHandleComponent extends BasePreHandleComponent {

    @Resource
    private SciTrfBizService sciTrfBizService;
    @Override
    public void process() throws Exception {
        Order2TRFContext orderContext = this.getContextBean(Order2TRFContext.class);
        OrderToTrfReq orderToTrfReq = orderContext.getOrderToTrfReq();
        sciTrfBizService.validateTrfNoForOrderToTrf(orderToTrfReq);
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) {
        Order2TRFContext orderContext = (Order2TRFContext)requestContext;
        OrderToTrfReq orderToTrfReq = orderContext.getOrderToTrfReq();
        sciTrfBizService.validateTrfNoForOrderToTrf(orderToTrfReq);
        return orderContext;
    }
}
