package com.sgs.customerbiz.app.component.convert;

import cn.hutool.core.util.ObjectUtil;
import com.sgs.customerbiz.app.component.action.importToTrf.ImportToTRFCreateActionComponent;
import com.sgs.customerbiz.biz.service.importtrf.cmd.PullImportTrfActionExtPt;
import com.sgs.customerbiz.context.ImportToTRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.model.trf.dto.TrfDTO;
import com.sgs.customerbiz.model.trf.dto.TrfLabDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * PULL模式导入TRF转换组件 - PullImport2TRF流程的数据转换节点
 * 
 * <p><b>流程位置</b>：位于PullImport2TRF子流程中，在getCustomerTRFComponent之后</p>
 * 
 * <p><b>核心功能</b>：
 * <ul>
 *   <li>继承ImportToTRFConvertComponent的通用转换逻辑</li>
 *   <li>扩展PULL模式特有的转换处理</li>
 *   <li>对实验室信息进行默认值处理</li>
 * </ul>
 * </p>
 * 
 * <p><b>PULL模式特点</b>：
 * <ul>
 *   <li>系统主动从客户系统拉取数据</li>
 *   <li>可能需要特殊的数据补充和校验</li>
 *   <li>需要处理实验室信息的默认值设置</li>
 * </ul>
 * </p>
 * 
 * <p><b>转换流程</b>：
 * <ol>
 *   <li>调用父类的convert方法执行通用转换</li>
 *   <li>调用PullImportTrfActionExtPt.defaultLabInfoIfNull进行后置处理</li>
 *   <li>处理实验室信息的缺失情况</li>
 * </ol>
 * </p>
 * 
 * <p><b>与父类的关系</b>：
 * <ul>
 *   <li>继承ImportToTRFConvertComponent的所有功能</li>
 *   <li>重写convert方法增加PULL模式特有的处理</li>
 *   <li>重写testComponent方法保证测试一致性</li>
 * </ul>
 * </p>
 * 
 * <p><b>调用的核心方法</b>：
 * <ul>
 *   <li>{@code super.convert(String, TrfImportReq)} - 父类的通用转换方法</li>
 *   <li>{@code PullImportTrfActionExtPt.defaultLabInfoIfNull(TrfDTO)} - PULL模式特有的后置处理</li>
 * </ul>
 * </p>
 * 
 * <p><b>后续流程</b>：转换完成后，流程将进入importToTRFPreHandleComponent进行预处理</p>
 * 
 * <AUTHOR> from Import2TRF_Main Process Analysis
 * @see ImportToTRFConvertComponent
 * @see com.sgs.customerbiz.biz.service.importtrf.cmd.PullImportTrfActionExtPt
 */
@Component
@LiteflowComponent(id = "pullImportTrfConvertComponent", name = "pullImportToTRFConvertComponent")
public class PullImportTrfConvertComponent extends ImportToTRFConvertComponent {

    /** PULL模式导入TRF操作扩展点 - 执行PULL模式特有的转换后处理 */
    @Autowired
    private PullImportTrfActionExtPt pullImportTrfActionExtPt;
    /**
     * PULL模式转换方法
     * 
     * <p><b>转换流程</b>：
     * <ol>
     *   <li>调用父类的convert方法执行通用转换</li>
     *   <li>调用PULL模式扩展点进行后置处理</li>
     *   <li>处理实验室信息的缺失情况</li>
     * </ol>
     * </p>
     * 
     * @param customerTrfJson 客户TRF JSON数据
     * @param importReq 导入请求参数
     * @return 转换并处理后的SGS TRF数据对象
     */
    @Override
    public TrfDTO convert(String customerTrfJson, TrfImportReq importReq) {
        TrfDTO sgsTRF = super.convert(customerTrfJson, importReq);
        pullImportTrfActionExtPt.defaultLabInfoIfNull(sgsTRF);
        return sgsTRF;
    }

    /**
     * 测试组件方法
     * 
     * <p>提供独立的组件测试能力，执行相同的PULL模式转换逻辑</p>
     * 
     * <p><b>注意</b>：此方法直接调用父类的convert而非当前类的convert，需要调整</p>
     * 
     * @param requestContext 请求上下文
     * @return 处理后的上下文对象
     */
    @Override
    public RequestContext testComponent(RequestContext requestContext) {
        ImportToTRFContext importToTRFContext = (ImportToTRFContext)requestContext;
        TrfImportReq importReq = importToTRFContext.getTrfImportReq();
        String customerTrf = importToTRFContext.getCustomerTrf();
        TrfDTO sgsTRF = super.convert(customerTrf, importReq);
        pullImportTrfActionExtPt.defaultLabInfoIfNull(sgsTRF);
        return importToTRFContext;
    }
}
