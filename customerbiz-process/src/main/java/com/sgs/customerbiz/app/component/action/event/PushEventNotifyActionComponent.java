package com.sgs.customerbiz.app.component.action.event;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.sgs.config.api.dto.EventSubscribeDTO;
import com.sgs.config.api.service.EventSubscribeService;
import com.sgs.customerbiz.biz.dto.DataSyncExtra;
import com.sgs.customerbiz.biz.event.handler.MessageNotifyHandler;
import com.sgs.customerbiz.biz.service.importtrf.cmd.PushImportTrfActionExtPt;
import com.sgs.customerbiz.biz.service.synctrf.SyncTrfContextHolder;
import com.sgs.customerbiz.biz.utils.DataSyncExtraUtils;
import com.sgs.customerbiz.biz.utils.TrfEventCodeMapping;
import com.sgs.customerbiz.context.ImportToTRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.core.constants.Constants;
import com.sgs.customerbiz.core.util.DateUtils;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.LocalEventMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.LocalEventPO;
import com.sgs.customerbiz.domain.domainevent.TrfActionEvent;
import com.sgs.customerbiz.domain.domainevent.TrfEvent;
import com.sgs.customerbiz.domain.domainevent.actionevent.TrfPushEvent;
import com.sgs.customerbiz.domain.enums.TrfEventTriggerBy;
import com.sgs.customerbiz.integration.LocalILayerClient;
import com.sgs.customerbiz.integration.dto.SystemLabInfoReq;
import com.sgs.customerbiz.integration.dto.SystemLabInfoRsp;
import com.sgs.customerbiz.model.trf.dto.TrfDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.customerbiz.model.trf.enums.TrfStatusEnum;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * PUSH模式事件通知组件 - SubChain_PushImport2TRF流程的最后一个组件
 * 
 * <p><b>流程位置</b>：位于SubChain_PushImport2TRF子流程的最后一个节点</p>
 * 
 * <p><b>核心功能</b>：
 * <ul>
 *   <li>PUSH模式完成后的事件通知处理</li>
 *   <li>执行PUSH模式特定的后置处理逻辑</li>
 *   <li>发送相关的业务事件和消息通知</li>
 *   <li>完成整个PUSH模式的最后收尾工作</li>
 * </ul>
 * </p>
 * 
 * <p><b>PUSH模式特点</b>：
 * <ul>
 *   <li>由客户系统主动推送数据完成导入</li>
 *   <li>可能需要特殊的事件通知机制</li>
 *   <li>可能需要回调客户系统告知处理结果</li>
 * </ul>
 * </p>
 * 
 * <p><b>处理逻辑</b>：
 * <ol>
 *   <li>从上下文获取TrfDTO和导入请求</li>
 *   <li>调用PushImportTrfActionExtPt.postImoprtExt进行后置处理</li>
 *   <li>可能包含事件发布、消息通知等操作</li>
 * </ol>
 * </p>
 * 
 * <p><b>与基类的关系</b>：
 * <ul>
 *   <li>继承EventNotifyActionComponent，复用通用事件通知能力</li>
 *   <li>扩展PUSH模式特有的事件处理逻辑</li>
 * </ul>
 * </p>
 * 
 * <p><b>调用的核心方法</b>：
 * <ul>
 *   <li>{@code PushImportTrfActionExtPt.postImoprtExt(TrfDTO, TrfImportReq)} - PUSH模式后置处理</li>
 * </ul>
 * </p>
 * 
 * <p><b>事件类型</b>：
 * <ul>
 *   <li>TRF创建事件</li>
 *   <li>数据同步事件</li>
 *   <li>客户系统通知事件</li>
 * </ul>
 * </p>
 * 
 * <AUTHOR> from Import2TRF_Main Process Analysis
 * @see EventNotifyActionComponent
 * @see com.sgs.customerbiz.biz.service.importtrf.cmd.PushImportTrfActionExtPt#postImoprtExt(TrfDTO, TrfImportReq)
 */
@Slf4j
@Component
@LiteflowComponent(id = "pushEventNotifyActionComponent", name = "push消息事件通知组件")
public class PushEventNotifyActionComponent extends EventNotifyActionComponent{

    /** PUSH模式导入TRF操作扩展点 - 执行PUSH模式特有的后置处理逻辑 */
    @Autowired
    private PushImportTrfActionExtPt pushImportTrfActionExtPt;
    /**
     * PUSH模式事件通知处理
     * 
     * <p><b>执行流程</b>：
     * <ol>
     *   <li>从上下文获取TRF数据和导入请求</li>
     *   <li>调用PUSH模式扩展点进行后置处理</li>
     *   <li>可能包含事件发布、数据同步等操作</li>
     * </ol>
     * </p>
     * 
     * <p><b>PUSH模式特殊处理</b>：
     * <ul>
     *   <li>可能需要向客户系统回调结果</li>
     *   <li>可能需要特殊的数据同步机制</li>
     *   <li>可能需要记录特殊的操作日志</li>
     * </ul>
     * </p>
     * 
     * @throws Exception 处理过程中的异常
     */
    @Override
    public void process() throws Exception {
        ImportToTRFContext requestContext = this.getContextBean(ImportToTRFContext.class);
        TrfImportReq importReq = requestContext.getTrfImportReq();
        TrfDTO trfDTO = requestContext.getTrfDTO();
        pushImportTrfActionExtPt.postImoprtExt(trfDTO, importReq);
    }
}
