-- 七匹狼上线SQL
INSERT INTO `tb_dict_value`(`id`, `system_id`, `dict_type`, `dict_value`, `dict_label`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (29, 40, 'RefSystemId', '10019', 'Septwolves', 1, 'system', '2024-01-29 16:36:02', 'system', '2024-01-29 16:36:08', '2024-01-29 16:36:10');
INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (36, NULL, '', '10019', 'SL', 2, 'TrfStatusControl', '{\"trfOrderRelationshipRule\":4,\"statusRule\":4,\"statusFlow\":[1,3,4,5,6,7,100],\"statusMapping\":{\"1\":1,\"3\":3,\"4\":4,\"5\":5,\"6\":6,\"7\":7,\"100\":100},\"actionStatusMapping\":{\"Import\":{\"orderRefRule\":0,\"orderOpType\":1,\"ctrlMapping\":{}},\"SyncToOrder\":{\"orderRefRule\":1,\"orderOpType\":1,\"ctrlMapping\":{\"3\":0}},\"SyncConfirmed\":{\"orderRefRule\":1,\"orderOpType\":1,\"ctrlMapping\":{\"4\":0}},\"SyncTesting\":{\"orderRefRule\":1,\"orderOpType\":1,\"ctrlMapping\":{\"5\":0}},\"SyncCompleted\":{\"orderRefRule\":1,\"orderOpType\":7,\"ctrlMapping\":{\"6\":0}},\"SyncClosed\":{\"orderRefRule\":1,\"orderOpType\":7,\"ctrlMapping\":{\"7\":0}},\"SyncPending\":{\"orderRefRule\":1,\"orderOpType\":7,\"ctrlMapping\":{\"1\":1,\"3\":3,\"4\":4,\"5\":5}},\"SyncUnPending\":{\"orderRefRule\":1,\"orderOpType\":7,\"ctrlMapping\":{\"1\":1,\"3\":3,\"4\":4,\"5\":5}},\"SyncUnBind\":{\"orderRefRule\":1,\"orderOpType\":2,\"ctrlMapping\":{\"3\":3,\"4\":4,\"5\":5}},\"SyncReviseReport\":{\"orderOpType\":7,\"ctrlMapping\":{\"6\":0,\"100\":0}},\"PreOrderReturnTrf\":{\"orderRefRule\":1,\"orderOpType\":3,\"ctrlMapping\":{\"1\":0}},\"CustomerReturnTrf\":{\"orderRefRule\":1,\"orderOpType\":3,\"ctrlMapping\":{\"1\":0}},\r\n        \r\n         \"SyncQuotationToTrf\": {\r\n             \"orderOpType\": 7,\r\n             \"ctrlMapping\": {\r\n                 \"2\": 0,\r\n								 \"3\":0,\r\n								 \"4\":0,\r\n								 \"5\":0,\r\n								 \"6\":0\r\n             }\r\n         }}}', 'system', '2024-01-29 15:12:36', 'system', '2024-01-29 08:20:58', '2024-01-29 16:40:29');
INSERT INTO `tb_cfg_system_api`(`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (33, 10019, 2, 'com.sgs.extsystem.sync.order.forILayer', 2, '{\r\n\"customerGroupCode\": \"CG0001420\",\r\n\"dffFormGroupId\": \"\",\r\n\"gridFormGroupId\": \"\",\r\n\"productLineCode\": \"SL\",\r\n\"refSystemId\": 10019,\r\n\"trfNo\": \"$.trfNo\"\r\n}\r\n', '{\r\n  \"header\": {\r\n    \"refSystemId\": 10019,\r\n    \"trfNo\": \"$.QuaNo\",\r\n    \"serviceType\": null,\r\n    \"sampleLevel\": null,\r\n    \"parcelNoList\": null,\r\n    \"trfSubmissionDate\": null,\r\n    \"selfTestFlag\": null,\r\n    \"others\": {\r\n      \"trfRemark\": null\r\n    },\r\n    \"extFields\": null\r\n  },\r\n  \"serviceRequirement\": {\r\n    \"report\": {\r\n      \"reportLanguage\": null,\r\n      \"reportForm\": null,\r\n      \"reportHeader\": null,\r\n      \"reportAddress\": null,\r\n      \"accreditation\": null,\r\n      \"needConclusion\": null,\r\n      \"needDraft\": \"\",\r\n      \"needPhoto\": \"\",\r\n      \"_languageList\": [\r\n        {\r\n          \"languageId\": null,\r\n          \"reportHeader\": null,\r\n          \"reportAddress\": null\r\n        }\r\n      ],\r\n      \"softcopy\": {\r\n        \"required\": null,\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      },\r\n      \"hardcopy\": {\r\n        \"required\": null,\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      }\r\n    },\r\n    \"sample\": {\r\n      \"returnResidueSample\": {\r\n        \"required\": null,\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      },\r\n      \"_returnTestSample\": {\r\n        \"required\": null,\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      }\r\n    },\r\n    \"invoice\": {\r\n      \"invoiceType\": null,\r\n      \"invoiceTitle\": null,\r\n      \"taxNo\": null,\r\n      \"registerAddr\": null,\r\n      \"registerPhone\": null,\r\n      \"bankName\": null,\r\n      \"bankNumber\": null,\r\n      \"actualAmountPaid\": null,\r\n      \"_invoiceDelivery\": {\r\n        \"required\": null,\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      }\r\n    }\r\n  },\r\n  \"customerList\": [\r\n    {\r\n      \"customerInstanceId\": null,\r\n      \"customerUsage\": 3,\r\n      \"customerId\": null,\r\n      \"bossNo\": null,\r\n      \"customerGroupCode\": \"CG0001420\",\r\n      \"customerName\": null,\r\n      \"customerAddress\": null,\r\n      \"languageList\": [\r\n        {\r\n          \"languageId\": null,\r\n          \"customerName\": null,\r\n          \"customerAddress\": null\r\n        }\r\n      ],\r\n      \"customerContactList\": [\r\n        {\r\n          \"customerContactId\": null,\r\n          \"bossContactId\": null,\r\n          \"bossSiteUseId\": null,\r\n          \"contactName\": null,\r\n          \"contactTelephone\": null,\r\n          \"contactMobile\": null,\r\n          \"contactFax\": null,\r\n          \"contactEmail\": null\r\n        }\r\n      ]\r\n    }\r\n  ],\r\n  \"product\": {\r\n    \"templateId\": \"8bdbf72b-aa78-4796-8338-0fa316cb2d30\",\r\n    \"productAttrList\": {\r\n        \"languageId\": 2,\r\n        \"StyleNos\":\"$.StyleNos\",\r\n        \"QuaNo\":\"$.QuaNo\",\r\n        \"Levels\":\"$.Levels\",\r\n        \"SafetyType\":\"$.SafetyType\"\r\n      }\r\n    \r\n  },\r\n  \"careLabelList\": [\r\n    {\r\n      \"careInstruction\": null,\r\n      \"radioType\": null,\r\n      \"selectCountry\": null,\r\n      \"careLabelSeq\": null,\r\n      \"sampleIds\": null\r\n    }\r\n  ],\r\n  \"testSampleList\": [\r\n    {\r\n      \"testSampleInstanceId\": \"\",\r\n      \"externalSampleNo\": \"$.SampleNos\",\r\n      \"testSampleType\": 101,\r\n      \"testSampleSeq\": null,\r\n      \"materialAttrList\": [\r\n        {\r\n          \"materialDescription\": null,\r\n          \"materialEndUse\": null,\r\n          \"materialColor\": null,\r\n          \"materialTexture\": null,\r\n          \"materialSampleRemark\": null,\r\n          \"extFields\": {\r\n            \"materialCategory\": null,\r\n            \"materialSku\": null,\r\n            \"materialItem\": null\r\n          }\r\n        }\r\n      ]\r\n    }\r\n  ],\r\n  \"testLineList\": [\r\n    {\r\n      \"testLineId\": null,\r\n      \"evaluationAlias\": null,\r\n      \"testLineSeq\": \".[index]\",\r\n      \"_citation\": {\r\n        \"citationId\": null,\r\n        \"citationType\": null,\r\n        \"citationFullName\": null\r\n      },\r\n      \"_ppTestLineRelList\": [\r\n        {\r\n          \"ppNo\": null,\r\n          \"ppName\": null\r\n        }\r\n      ],\r\n      \"externalInfo\": {\r\n        \"testItemId\": null,\r\n        \"testItemName\": \"$.TypeName\",\r\n        \"testCitationId\": null,\r\n        \"testCitationName\": \"$.JudgingBy\",\r\n        \"checkType\": null\r\n      }\r\n    }\r\n  ],\r\n  \"attachmentList\": [\r\n    {\r\n      \"fileName\": \"$.QuaNo\",\r\n      \"fileType\": null,\r\n      \"filePath\": \"$.PdfUrl\",\r\n      \"cloudId\": null\r\n    }\r\n  ]\r\n}\r\n', 'Septwolves Import', 'XQW', '2024-01-29 16:42:54', 'XQW', '2024-01-29 16:42:59', '2024-02-02 11:30:43');

-- 待修改
INSERT INTO `tb_cfg_system_api`(`id`, `system_id`, `protocol_type`, `request_url`, `request_method`,
                                                 `request_body_template`, `response_body_template`, `remark`,
                                                 `created_by`, `created_date`, `modified_by`, `modified_date`,
                                                 `last_modified_timestamp`)
VALUES (34, 10019, 2, ''com.sgs.extsystem.sync.order.forILayer'', 2, ''{\r\n  \"action\": \"SyncReport\",\r\n  \"dataStatus\": \"Completed\",\r\n  \"bu\": \"$.extra.productLineCode\",\r\n  \"objectNumber\": \"$.extra.trfNo\",\r\n  \"refSystemId\": \"$.extra.refSystemId\",\r\n  \"msgId\": \"$.extra.randomSequence\",\r\n  \"body\": {\r\n    \"ResultContent\": [\r\n      {\r\n        \"_reportNo\": \"$.reportList[*].reportNo\",\r\n        \"QuaNo\": \"$.trfList[0].trfNo\",\r\n        \"Stfbreportno\": \"\",\r\n        \"AcceptDate\": \"$.orderList[0].productList[0].productAttrList[labCode=\''sampleReceivedDate\''].languageList[languageId=2].labelValue[0]\",\r\n        \"TrustcustomerName\": \"$.orderList[0].productList[0].productAttrList[labCode=\''buyerOrgannizationCode1\''].languageList[languageId=2].labelValue[0]\",\r\n        \"MakeCustomerName\": null,\r\n        \"ProductName\": \"$.orderList[0].productList[0].productAttrList[labCode=\''productDescription\''].languageList[languageId=2].labelValue[0]\",\r\n        \"ProductCount\": null,\r\n        \"Trademark\": \"\",\r\n        \"Samplespec\": \"\",\r\n        \"SecurityCategories\": \"\",\r\n        \"QualityRegistration\": \"\",\r\n        \"DapproveDate\": \"$.reportList[*].approveDate\",\r\n        \"ProductStandard\": \"\",\r\n        \"ItemList\": \"\",\r\n        \"SuperviseNoticeCode\": null,\r\n        \"Fails\": \"#getFailsValueFn($.reportList[*],$.testLineList)\",\r\n        \"UrlImage\": null,\r\n        \"UrlPdf\": \"$.reportList[*].reportFileList[0].filePath\",\r\n        \"UrlPdf1\": null,\r\n        \"UrlPdf2\": null,\r\n        \"ProductSremark\": null,\r\n        \"ProductSremarkFull\": null,\r\n        \"Samples\": [\r\n          {\r\n            \"SampleSn\": \"$.testSampleList[*].testSampleNo\",\r\n            \"SampleMark\": null\r\n          }\r\n        ],\r\n        \"Detail\": [\r\n          {\r\n            \"_reportNo\": \"@_reportNo\",\r\n            \"_testMatrixId\": \"$.reportList[reportNo=@.reportNo][0].reportMatrixList[*].testMatrixId\",\r\n            \"_testSampleInstanceId\": \"$.reportList[reportNo=@.reportNo][0].reportMatrixList[*].testSampleInstanceId\",\r\n            \"_testLineInstanceId\": \"$.reportList[reportNo=@.reportNo][0].reportMatrixList[*].testLineInstanceId\",\r\n            \"SampleSn\": \"$.testSampleList[testSampleInstanceId=@._testSampleInstanceId].testSampleNo[0]\",\r\n            \"Item\": \"$.testLineList[testLineInstanceId=@._testLineInstanceId].languageList[languageId=2].evaluationAlias[0]\",\r\n            \"PrefixA\": \"$.testLineList[testLineInstanceId=@._testLineInstanceId].ppTestLineRelList[0].citation.citationFullName[0]\",\r\n            \"Prefix\": \"$.testLineList[testLineInstanceId=@._testLineInstanceId].citation.languageList[languageId=2].citationFullName[0]\",\r\n            \"ItemDetail\": [\r\n              {\r\n                \"ItemName\": \"$.testResultList[testMatrixId=@_testMatrixId].testResult.languageList[languageId=2].testResultFullName\",\r\n                \"Result\": \"$.reportList[reportNo=@_reportNo].reportMatrixList[testMatrixId=@_testMatrixId].conclusion.languageList[languageId=2][0].customerConclusion\",\r\n                \"StandValue\": \"$.testResultList[testMatrixId=@_testMatrixId].reportLimit.limitValueFullName\",\r\n                \"ActureValue\": \"$.testResultList[testMatrixId=@_testMatrixId].testResult.resultValue\"\r\n              }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n    ]\r\n  }\r\n}\r\n'', ''{}'', ''Septwolves SyncCompleted'', ''XQW'', ''2024-01-29 16:44:02'', ''XQW'', ''2024-01-29 16:44:05'', ''2024-02-02 17:13:48'');

INSERT INTO `tb_cfg_event_subscribe`(`id`, `subscriber`, `ref_system_id`, `event_code`, `api_id`, `priority`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (56, 10019, 10019, 1, 33, 0, 0, 511, 'messageNotifyHandler', '', 'XQW', '2024-01-29 16:45:15', 'XQW', '2024-01-29 16:45:20', '2024-01-29 16:45:20');
INSERT INTO `tb_cfg_event_subscribe`(`id`, `subscriber`, `ref_system_id`, `event_code`, `api_id`, `priority`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (57, 10019, 10019, 60, 34, 0, 0, 2023, 'messageNotifyHandler', '', 'XQW', '2024-01-29 16:45:47', 'XQW', '2024-01-29 16:45:50', '2024-01-29 16:45:50');
