-- uat 已执行 start

INSERT INTO  `tb_dfv_template_field`(`id`, `template_id`, `field_id`, `field_path`, `sort_num`, `parent_id`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (161, 5, 340, 'testResultList.testMatrixId', 1, 160, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO  `tb_dfv_template_field`(`id`, `template_id`, `field_id`, `field_path`, `sort_num`, `parent_id`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (160, 5, 9, 'testResultList', 0, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO  `tb_dfv_template_field_constraint`(`id`, `template_id`, `template_field_id`, `field_id`, `field_path`, `type`, `constraint_value1`, `constraint_value2`, `field_id_ref`, `active_indicator`, `sort_num`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (1160, 5, 160, 9, 'testResultList', 'Other', '', '', 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-07-03 15:40:12');
INSERT INTO  `tb_dfv_template_field_constraint`(`id`, `template_id`, `template_field_id`, `field_id`, `field_path`, `type`, `constraint_value1`, `constraint_value2`, `field_id_ref`, `active_indicator`, `sort_num`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (1161, 5, 161, 340, 'testResultList.testMatrixId', 'RefObject', 'reportList.reportMatrixList.testMatrixId', 'reportList.reportMatrixList', 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-07-03 15:46:04');


-- end
UPDATE `tb_dfv_template_field_constraint` SET `constraint_value1` = '^\\d?\\d*$' WHERE `id` = 888;


-- 初始化希音取消事件订阅配置
INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (24, 7, 82, 0, 8, 0, 'sheinCancellationHandler', '', 'Walley', '2023-07-06 16:30:24', 'Walley', '2023-07-06 16:30:24', '2023-07-06 19:21:33');

update tb_cfg_event_subscribe set error_handle='email' where system_id=7 and event_code in (30,40,60);

update tb_cfg_info set config_value = '{"trfOrderRelationshipRule":2,"statusRule":4,"statusFlow":[1,4,5,6,7],"statusMapping":{"1":1,"4":4,"5":5,"6":6,"7":7,"9":9},"actionStatusMapping":{"Import":{"orderRefRule":0,"orderOpType":1,"ctrlMapping":{}},"SyncConfirmed":{"orderRefRule":1,"orderOpType":1,"ctrlMapping":{"4":0}},"SyncTesting":{"orderRefRule":1,"orderOpType":3,"ctrlMapping":{"5":0}},"SyncCompleted":{"orderRefRule":1,"orderOpType":3,"ctrlMapping":{"6":0}},"SyncClosed":{"orderRefRule":1,"orderOpType":3,"ctrlMapping":{"7":0}},"SyncUnBind":{"orderRefRule":1,"orderOpType":3,"ctrlMapping":{"1":0,"2":0,"3":0,"4":0,"5":0}},"CustomerCancelTrf":{"ctrlMapping":{"1":0}},"PreOrderCancelTrf":{"ctrlMapping":{"1":0}}}}' WHERE id = 1;