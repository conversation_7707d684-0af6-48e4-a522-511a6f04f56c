CREATE TABLE `tb_trf_park_event` (
                                     `id` bigint(20) NOT NULL COMMENT '主键',
                                     `event_code` int(11) NOT NULL COMMENT '事件code',
                                     `ref_system_id` int(11) NOT NULL COMMENT '客户系统ID',
                                     `trf_no` varchar(50) NOT NULL COMMENT 'Trf 编号',
                                     `trigger_by` varchar(50) NOT NULL COMMENT '触发方',
                                     `request_id` varchar(100) DEFAULT NULL COMMENT '请求id',
                                     `active_indicator` tinyint(4) NOT NULL DEFAULT '1' COMMENT '有效无效标记：0: inactive, 1: active',
                                     `created_by` varchar(100) DEFAULT NULL COMMENT '创建人',
                                     `created_date` datetime DEFAULT NULL COMMENT '创建时间',
                                     `modified_by` varchar(50) DEFAULT NULL COMMENT '修改人',
                                     `modified_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                     `last_modified_timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
                                     PRIMARY KEY (`id`),
                                     KEY `idx_created_time` (`created_date`) USING BTREE,
                                     KEY `idx_trfNo` (`trf_no`,`ref_system_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


ALTER TABLE `tb_cfg_event_subscribe`
    ADD COLUMN `handler_name`  varchar(50) NOT NULL DEFAULT 'messageNotifyHandler' COMMENT '事件处理器实例名称' AFTER `notify_data`;

ALTER TABLE `tb_cfg_event_subscribe`
    ADD COLUMN `error_handle`  varchar(500) NOT NULL DEFAULT '' COMMENT 'sync失败时的处理动作，多个动作名称之间用,分隔' AFTER `handler_name`;

