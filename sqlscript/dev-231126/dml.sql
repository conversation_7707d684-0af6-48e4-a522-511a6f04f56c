UPDATE `tb_cfg_system_api` SET `request_body_template` = '{\r\n  \"_reports\":[\r\n    {\r\n		\"_reportNo\": \"$.reportList[*].reportNo\",\r\n      \"data\": [\r\n        {\r\n				\"_reportNo\": \"@_reportNo\",\r\n					\"_testSampleInstanceId\":\"$.reportList[reportNo=@._reportNo].reportMatrixList[*].testSampleInstanceId\",\r\n					\r\n          \"G_ID\": \"$.testSampleList[testSampleInstanceId=@._testSampleInstanceId][0].external.testSampleId\",\r\n          \"WTDH\": \"$.trfList[0].trfNo\",\r\n          \"SFSC\": \"$.testSampleList[testSampleInstanceId=@._testSampleInstanceId][0].testSampleNo\",\r\n          \"SC\": \"$.testSampleList[testSampleInstanceId=@._testSampleInstanceId][0].external.testSampleNo\",\r\n          \"PDFTIME\": \"#toDate($.reportList[reportNo=@._reportNo].softCopyDeliveryDate[0],\'yyyy-MM-dd\')\",\r\n          \"VER\": \"#yiliReportDeliveryCounter($.extra.refSystemId,$.extra.trfNo)\",\r\n          \"YPBMWD\": null,\r\n          \"PDFFILES\": [\r\n            {\r\n              \"PDFNAME\": \"$.reportList[reportNo = @_reportNo].reportFileList[*].fileName\",\r\n              \"PDFSTRING\": null,\r\n              \"PDFURI\": \"$.reportList[reportNo = @_reportNo].reportFileList[*].cloudId\"\r\n            }\r\n          ],\r\n          \"PADATA\": [\r\n            {\r\n              \"G_ID\": \"$.reportList[reportNo = @_reportNo].reportMatrixList[*].testMatrixId\",\r\n              \"SC\": null,\r\n							\"_testLineInstanceId\":\"$.reportList[reportNo=@_reportNo].reportMatrixList[*].testLineInstanceId\",\r\n              \"PA\": \"$.orderList[0].testItemMappingList[testLineInstanceId =@._testLineInstanceId].external.testItemId[0]\",\r\n              \"PANAME\": \"$.orderList[0].testItemMappingList[testLineInstanceId =@._testLineInstanceId].external.testItemName[0]\",\r\n              \"UNIT\": \"$.testResultList[testMatrixId = @.G_ID].testResult.resultUnit[0]\",\r\n              \"VALUE_S\": \"$.testResultList[testMatrixId = @.G_ID].testResult.resultValue[0]\",\r\n              \"CONCLUSION\": \"$.reportList[reportNo =@_reportNo][0].reportMatrixList[testMatrixId = @.G_ID][0].conclusion.customerConclusion\",\r\n              \"METHOD\": \"$.testLineList[testLineInstanceId = @._testLineInstanceId][0].citation.languageList[0].citationFullName\",\r\n              \"METHODNAME\": \"$.testLineList[testLineInstanceId = @._testLineInstanceId][0].citation.languageList[0].citationFullName\",\r\n              \"LODORLOQ\": \"$.testResultList[testMatrixId = @.G_ID].methodLimit.limitType[0]\",\r\n              \"LIMITVALUE\": \"$.testResultList[testMatrixId = @.G_ID].methodLimit.limitValueFullName[0]\",\r\n              \"TESTTIME\": \"#toDate($.orderList[0].testingStartDate)\",\r\n              \"TESTENDTIME\": \"#toDate($.orderList[0].testingEndDate)\",\r\n              \"EQNAME\": null,\r\n              \"SYTJ\": null,\r\n              \"STATUS\": null,\r\n              \"BZYQ\": \"$.testResultList[testMatrixId = @.G_ID].reportLimit.limitValueFullName[0]\",\r\n              \"HighLimit\": null,\r\n              \"LowLimit\": null\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  ] ,\r\n  \"_buildReportsNode\": \"#buildVirtualNode(\'_reports\',@._reports)\",\r\n  \"action\": \"SyncReport\",\r\n  \"dataStatus\": \"Completed\",\r\n  \"bu\": \"$.extra.productLineCode\",\r\n  \"objectNumber\": \"$.extra.trfNo\",\r\n  \"refSystemId\": \"$.extra.refSystemId\",\r\n  \"msgId\": \"$.extra.randomSequence\",\r\n  \"body\": {\r\n    \"reports\": \"$._reports[0].data\"\r\n  }\r\n}' WHERE `id` = 20;



INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (20, NULL, NULL, '7', 'SL', 2, 'InvoiceCurrency', 'CNY', 'system', '2023-11-23 13:42:30', 'system', '2023-11-23 13:42:39', '2023-11-23 13:42:39');
INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (21, NULL, NULL, '7', 'SL', 2, 'InvoiceRateType', 'fixedRate', 'system', '2023-11-23 13:43:13', 'system', '2023-11-23 13:43:21', '2023-11-23 13:43:21');
INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (22, NULL, NULL, '0', 'SL', 2, 'InvoiceRateType', 'RealTimeRate', 'system', '2023-11-23 13:44:09', 'system', '2023-11-23 13:44:14', '2023-11-23 15:47:39');
INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (23, NULL, NULL, '0', 'SL', 2, 'InvoiceCurrency', 'CNY', 'system', '2023-11-23 13:44:46', 'system', '2023-11-23 13:44:52', '2023-11-23 13:44:52');
UPDATE `tb_cfg_event_subscribe` SET `notify_rule` = 0 WHERE `id` = 15;
UPDATE `tb_cfg_info` SET `config_value` = '{\r\n    \"trfOrderRelationshipRule\":2,\r\n    \"statusRule\":4,\r\n    \"statusFlow\":[\r\n        1,\r\n        4,\r\n        5,\r\n        6,\r\n        7\r\n    ],\r\n    \"statusMapping\":{\r\n        \"1\":1,\r\n        \"4\":4,\r\n        \"5\":5,\r\n        \"6\":6,\r\n        \"7\":7,\r\n        \"9\":9\r\n    },\r\n    \"actionStatusMapping\":{\r\n        \"Import\":{\r\n            \"orderRefRule\":0,\r\n            \"orderOpType\":1,\r\n            \"ctrlMapping\":{\r\n\r\n            }\r\n        },\r\n        \"SyncConfirmed\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":1,\r\n            \"ctrlMapping\":{\r\n                \"4\":0\r\n            }\r\n        },\"SyncClosed\":{\"orderRefRule\":1,\"orderOpType\":7,\"ctrlMapping\":{\"7\":0}},\r\n        \"SyncTesting\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"5\":0\r\n            }\r\n        },\r\n        \"SyncCompleted\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"6\":0\r\n            }\r\n        },\r\n        \"SyncUnBind\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":3,\r\n            \"ctrlMapping\":{\r\n                \"1\":0,\r\n                \"2\":0,\r\n                \"3\":0,\r\n                \"4\":0,\r\n                \"5\":0\r\n            }\r\n        }\r\n    }\r\n}'  WHERE `id` = 1;
