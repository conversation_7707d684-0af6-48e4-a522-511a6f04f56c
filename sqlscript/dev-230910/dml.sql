--- TIC unbind；；todo 其他所有都需要允许100 unbind
UPDATE `tb_cfg_info` SET `config_value` = '{\r\n    \"trfOrderRelationshipRule\":4,\r\n    \"statusRule\":0,\r\n    \"statusFlow\":[\r\n        1,\r\n        3,\r\n        4,\r\n        5,\r\n        6,\r\n        7,\r\n				100\r\n    ],\r\n    \"statusMapping\":{\r\n        \"1\":1,\r\n        \"3\":3,\r\n        \"4\":4,\r\n        \"5\":5,\r\n        \"6\":6,\r\n        \"7\":7,\r\n        \"9\":9,\r\n				\"100\":100\r\n    },\r\n    \"actionStatusMapping\":{\r\n        \"Import\":{\r\n            \"orderRefRule\":0,\r\n            \"orderOpType\":1,\r\n            \"ctrlMapping\":{\r\n\r\n            }\r\n        },\r\n        \"SyncToOrder\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":1,\r\n            \"ctrlMapping\":{\r\n                \"3\":0\r\n            }\r\n        },\r\n        \"SyncConfirmed\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"4\":0\r\n            }\r\n        },\r\n        \"SyncTesting\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"5\":0\r\n            }\r\n        },\r\n        \"SyncCompleted\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"6\":0\r\n            }\r\n        },\r\n        \"SyncClosed\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"7\":8\r\n            }\r\n        },\r\n        \"SyncUnBind\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"1\":0,\r\n                \"2\":0,\r\n                \"3\":0,\r\n                \"4\":0,\r\n                \"5\":0,\r\n								\"100\":0\r\n            }\r\n        },\r\n        \"PreOrderReturnTrf\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":3,\r\n            \"ctrlMapping\":{\r\n                \"1\":0\r\n            }\r\n        },\r\n        \"CustomerReturnTrf\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":3,\r\n            \"ctrlMapping\":{\r\n                \"1\":0\r\n            }\r\n        },\r\n        \"SyncReviseReport\": {\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"100\": 0,\r\n								\"6\":0\r\n            }\r\n        }\r\n    }\r\n}' WHERE `id` = 6

-- TIC 报告回传
UPDATE `tb_cfg_system_api` SET `request_body_template` = '{\r\n	\"_init\":{\r\n		\"_filter_reportList\":\"#filterReportForCompleted()\",\r\n		\"_ignoreNode\":{\r\n			\"_ignore1\":\"#buildVirtualNode(\'filterReportList\',@_filter_reportList)\"\r\n		}\r\n	},\r\n    \"bu\": \"$.extra.productLineCode\",\r\n    \"refSystemId\": \"$.extra.refSystemId\",\r\n    \"objectNumber\": \"$.extra.trfNo\",\r\n    \"action\": \"SyncReport\",\r\n    \"dataStatus\": \"#eventToIlayerDataStatus($.extra.eventName)\",\r\n    \"msgId\": \"$.extra.randomSequence\",\r\n    \"body\": {\r\n	    \"orderNo\":\"$.trfList[0].trfNo\",\r\n	    \"platform\":\"SODA\",\r\n	    \"platformOrder\":\"$.trfList[0].trfId\",\r\n	    \"reports\":[\r\n	        {\r\n	            \"status\":\"#reviseReportType($.trfList[0].refSystemId,$.trfList[0].trfNo,$.filterReportList[*].originalReportNo)\",\r\n							\"cloudId\":\"$.filterReportList[*].reportFileList[*].cloudId\",\r\n	            \"fileId\":\"$.filterReportList[*].reportFileList[*].cloudId\",\r\n	            \"fileName\":\"$.filterReportList[*].reportFileList[*].fileName\",\r\n	            \"reportNo\":\"$.filterReportList[*].reportNo\",\r\n	            \"oldNo\":\"$.filterReportList[*].originalReportNo\",\r\n	            \"reportDate\":\"#toDate($.filterReportList[*].softCopyDeliveryDate)\"\r\n	        }\r\n	    ]\r\n	}\r\n}' WHERE `id` = 1;

