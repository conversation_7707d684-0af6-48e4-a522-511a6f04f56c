UPDATE `tb_cfg_info` SET `csutomer_group` = 'CG0001085', `customer_no` = '', `identity_id` = '7', `product_line` = 'SL', `config_type` = 2, `config_key` = 'TrfStatusControl', `config_value` = '{\r\n    \"trfOrderRelationshipRule\":2,\r\n    \"statusRule\":4,\r\n    \"statusFlow\":[\r\n        1,\r\n        4,\r\n        5,\r\n        6,\r\n        7\r\n    ],\r\n    \"statusMapping\":{\r\n        \"1\":1,\r\n        \"4\":4,\r\n        \"5\":5,\r\n        \"6\":6,\r\n        \"7\":7,\r\n        \"9\":9\r\n    },\r\n    \"actionStatusMapping\":{\r\n        \"Import\":{\r\n            \"orderRefRule\":0,\r\n            \"orderOpType\":1,\r\n            \"ctrlMapping\":{\r\n\r\n            }\r\n        },\r\n        \"SyncConfirmed\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":1,\r\n            \"ctrlMapping\":{\r\n                \"4\":0\r\n            }\r\n        },\r\n        \"SyncTesting\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":3,\r\n            \"ctrlMapping\":{\r\n                \"5\":0\r\n            }\r\n        },\r\n        \"SyncCompleted\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":3,\r\n            \"ctrlMapping\":{\r\n                \"6\":0\r\n            }\r\n        },\r\n        \"SyncClosed\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":3,\r\n            \"ctrlMapping\":{\r\n                \"7\":0\r\n            }\r\n        },\r\n        \"SyncUnBind\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":3,\r\n            \"ctrlMapping\":{\r\n                \"1\":0,\r\n                \"2\":0,\r\n                \"3\":0,\r\n                \"4\":0,\r\n                \"5\":0\r\n            }\r\n        }\r\n    }\r\n}', `created_by` = 'system', `created_date` = '2023-05-11 15:12:36', `modified_by` = 'system', `modified_date` = '2023-05-15 08:20:58', `last_modified_timestamp` = '2023-07-26 10:59:11' WHERE `id` = 1;
UPDATE `tb_cfg_info` SET `csutomer_group` = NULL, `customer_no` = '', `identity_id` = '0', `product_line` = '', `config_type` = 2, `config_key` = 'TrfStatusControl', `config_value` = '{\"actionStatusMapping\":{\"CustomerCancelTrf\":{\"ctrlMapping\":{\"1\":0}},\"PreOrderCancelTrf\":{\"ctrlMapping\":{\"1\":0,\"2\":0,\"3\":0,\"4\":0,\"5\":0}}}}', `created_by` = 'system', `created_date` = '2023-05-11 15:12:36', `modified_by` = 'system', `modified_date` = '2023-05-15 08:20:58', `last_modified_timestamp` = '2023-07-27 11:53:20' WHERE `id` = 5;
UPDATE `tb_cfg_info` SET `csutomer_group` = '', `customer_no` = '', `identity_id` = '12', `product_line` = 'SL', `config_type` = 2, `config_key` = 'TrfStatusControl', `config_value` = '{\r\n    \"trfOrderRelationshipRule\":2,\r\n    \"statusRule\":0,\r\n    \"statusFlow\":[\r\n        1,\r\n        3,\r\n        4,\r\n        5,\r\n        6,\r\n        7\r\n    ],\r\n    \"statusMapping\":{\r\n        \"1\":1,\r\n        \"3\":3,\r\n        \"4\":4,\r\n        \"5\":5,\r\n        \"6\":6,\r\n        \"7\":7,\r\n        \"9\":9\r\n    },\r\n    \"actionStatusMapping\":{\r\n        \"Import\":{\r\n            \"orderRefRule\":0,\r\n            \"orderOpType\":1,\r\n            \"ctrlMapping\":{\r\n\r\n            }\r\n        },\r\n        \"SyncToOrder\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":1,\r\n            \"ctrlMapping\":{\r\n                \"3\":0\r\n            }\r\n        },\r\n        \"SyncConfirmed\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"4\":0\r\n            }\r\n        },\r\n        \"SyncTesting\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"5\":0\r\n            }\r\n        },\r\n        \"SyncCompleted\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"6\":0\r\n            }\r\n        },\r\n        \"SyncClosed\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"7\":8\r\n            }\r\n        },\r\n        \"SyncUnBind\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"1\":0,\r\n                \"2\":0,\r\n                \"3\":0,\r\n                \"4\":0,\r\n                \"5\":0\r\n            }\r\n        },\r\n        \"PreOrderReturnTrf\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":3,\r\n            \"ctrlMapping\":{\r\n                \"1\":0\r\n            }\r\n        },\r\n        \"CustomerReturnTrf\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":3,\r\n            \"ctrlMapping\":{\r\n                \"1\":0\r\n            }\r\n        }\r\n    }\r\n}', `created_by` = 'system', `created_date` = '2023-05-11 15:12:36', `modified_by` = 'system', `modified_date` = '2023-05-15 08:20:58', `last_modified_timestamp` = '2023-07-26 11:02:10' WHERE `id` = 6;
