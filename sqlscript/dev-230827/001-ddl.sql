
CREATE TABLE `tb_local_event` (
    `id` bigint(20) NOT NULL COMMENT 'ID',
    `event_id` bigint(20) NOT NULL COMMENT '事件ID',
    `event_type` varchar(100) NOT NULL COMMENT '事件类型名',
    `event_content` text NOT NULL COMMENT '事件内容',
    `created_at` datetime NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_event_id` (`event_id`) USING BTREE,
    KEY `idx_created_at` (`created_at`) USING BTREE,
    FULLTEXT KEY `ft_event_content` (`event_content`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='本地事件';

CREATE TABLE `tb_local_event_failover` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `event_id` bigint(20) NOT NULL COMMENT '事件ID',
    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态，0-初始化，1-成功，2-失败',
    `created_at` datetime NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


alter table tb_trf_park_event
    add bu_code varchar(50) not null comment '业务线编码';
alter table tb_trf_park_event
    add event_id bigint not null comment '事件ID';
alter table tb_trf_park_event
    add replay_result tinyint default 0 not null comment '事件重放结果，0-initial 1-successful 2-failed';
alter table tb_trf_park_event
    add system_id int not null comment '执行系统ID';
alter table tb_trf_park_event
    add trigger_timestamp bigint not null comment '事件触发时时间戳';


ALTER TABLE tb_trf_order ADD COLUMN trf_no varchar(50) DEFAULT NULL COMMENT 'Trf 编号' AFTER trf_id;
ALTER TABLE tb_trf_order ADD COLUMN ref_system_id int(11) DEFAULT NULL COMMENT '客户系统编号' AFTER trf_no;
