-- 7.修正订阅关系表systemApiId和systemId不匹配问题（需要提前到更新apiId=1之前执行）
--  7.1 新增api
INSERT INTO `tb_cfg_system_api` (`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`) VALUES (10, 7, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{}', '{}', NULL, 'YANG', '2023-05-11 19:22:59', 'YANG', '2023-05-11 19:23:07');
--  7.2 修正关系
UPDATE `tb_cfg_event_subscribe` SET `api_id` = 10 ,`modified_date` = now() WHERE `id` in(1,2,3,4);

-- tic相关
-- 更新TIC systemApi信息
UPDATE `tb_cfg_system_api` SET `system_id` = 12, `protocol_type` = 2, `request_url` = 'com.sgs.extsystem.sync.order.forILayer', `request_method` = NULL, `request_body_template` = '{\r\n	\"_init\":{\r\n		\"_filter_reportList\":\"#filterReportForCompleted()\",\r\n		\"_ignoreNode\":{\r\n			\"_ignore1\":\"#buildVirtualNode(\'filterReportList\',@_filter_reportList)\"\r\n		}\r\n	},\r\n    \"bu\": \"$.extra.productLineCode\",\r\n    \"refSystemId\": \"$.extra.refSystemId\",\r\n    \"objectNumber\": \"$.extra.trfNo\",\r\n    \"action\": \"SyncReport\",\r\n    \"dataStatus\": \"#eventToIlayerDataStatus($.extra.eventName)\",\r\n    \"msgId\": \"$.extra.randomSequence\",\r\n    \"body\": {\r\n	    \"orderNo\":\"$.trfList[0].trfNo\",\r\n	    \"platform\":\"SODA\",\r\n	    \"platformOrder\":\"$.trfList[0].trfId\",\r\n	    \"reports\":[\r\n	        {\r\n	            \"status\":\"#reviseReportType($.trfList[0].refSystemId,$.trfList[0].trfNo,$.reportList[*].originalReportNo)\",\r\n							\"cloudId\":\"$.reportList[*].reportFileList[*].cloudId\",\r\n	            \"fileId\":\"$.reportList[*].reportFileList[*].cloudId\",\r\n	            \"fileName\":\"$.reportList[*].reportFileList[*].fileName\",\r\n	            \"reportNo\":\"$.reportList[*].reportNo\",\r\n	            \"oldNo\":\"$.reportList[*].originalReportNo\",\r\n	            \"reportDate\":\"#toDate($.reportList[*].softCopyDeliveryDate)\"\r\n	        }\r\n	    ]\r\n	}\r\n}', `response_body_template` = '{}', `remark` = 'TIC 报告更改通知接口', `modified_by` = 'YANG', `modified_date` = now() WHERE `id` = 1;
UPDATE `tb_cfg_system_api` SET `system_id` = 12, `protocol_type` = 2, `request_url` = 'com.sgs.extsystem.sync.order.forILayer', `request_method` = NULL, `request_body_template` = '{\r\n    \"bu\": \"$.extra.productLineCode\",\r\n    \"refSystemId\": \"$.extra.refSystemId\",\r\n    \"objectNumber\": \"$.extra.trfNo\",\r\n    \"action\": \"SyncOrderStatus\",\r\n    \"dataStatus\": \"#eventToIlayerDataStatus($.extra.eventName)\",\r\n    \"msgId\": \"$.extra.randomSequence\",\r\n    \"body\": {\r\n	    \"orderNo\":\"$.trfList[0].trfNo\",\r\n	    \"platform\":\"SODA\",\r\n	    \"platformOrder\":\"$.trfList[0].trfId\",\r\n	    \"orderState\":\"#actionMapping($extra.eventName,\'SyncToOrder\':64&\'SyncConfirmed\':14&\'SyncCompleted\':80&\'PreOrderCancelTrf\':91)\",\r\n	    \"orderAmount\":null,\r\n	    \"memo\":\"#getCancelTypeDesc($.trfList[0].others.cancel.cancelType)\",\r\n	    \"operatorDate\":\"#toDate($.extra.syncInfo.reportList[0].softCopyDeliveryDate)\",\r\n	    \"csCode\":\"$.trfList[0].lab.labContact.contactName\"\r\n	}\r\n}', `response_body_template` = '{}', `remark` = 'TIC 状态更改通知接口(completed)' WHERE `id` = 6;
INSERT INTO `tb_cfg_system_api` (`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (13, 12, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{\r\n    \"bu\": \"$.extra.productLineCode\",\r\n    \"refSystemId\": \"$.extra.refSystemId\",\r\n    \"objectNumber\": \"$.extra.trfNo\",\r\n    \"action\": \"SyncOrderStatus\",\r\n    \"dataStatus\": \"#eventToIlayerDataStatus($.extra.eventName)\",\r\n    \"msgId\": \"$.extra.randomSequence\",\r\n    \"body\": {\r\n	    \"orderNo\":\"$.trfList[0].trfNo\",\r\n	    \"platform\":\"SODA\",\r\n	    \"platformOrder\":\"$.trfList[0].trfId\",\r\n	    \"orderState\":\"#actionMapping($extra.eventName,\'SyncToOrder\':64&\'SyncConfirmed\':14&\'SyncCompleted\':80&\'PreOrderCancelTrf\':91)\",\r\n	    \"orderAmount\":null,\r\n	    \"memo\":\"#getCancelTypeDesc($.trfList[0].others.cancel.cancelType)\",\r\n	    \"operatorDate\":\"#toDate($.extra.syncInfo.reportList[0].reportDueDate)\",\r\n	    \"csCode\":\"$.trfList[0].lab.labContact.contactName\"\r\n	}\r\n}', '{}', 'TIC 状态更改通知接口(confirm)', 'YANG', '2023-05-11 19:22:59', 'YANG', '2023-05-11 19:23:07', '2023-08-26 22:30:04');
-- confirm  syncStatus =13, syncReport =1 ,  completed syncStatus =6


-- 更新TIC 事件订阅API关系表
UPDATE `tb_cfg_event_subscribe` SET `event_code` = 10, `api_id` = 13, `notify_rule` = 24, `notify_data` = 2023, `handler_name` = 'messageNotifyHandler', `error_handle` = '', `created_by` = 'YANG', `created_date` = '2023-05-11 19:20:49', `modified_by` = 'YANG', `modified_date` = '2023-05-11 19:21:00', `last_modified_timestamp` = '2023-08-24 15:52:24' WHERE `id` = 12;
UPDATE `tb_cfg_event_subscribe` SET `event_code` = 30, `api_id` = 13, `notify_rule` = 24, `notify_data` = 2023, `handler_name` = 'messageNotifyHandler', `error_handle` = '', `created_by` = 'YANG', `created_date` = '2023-05-11 19:20:49', `modified_by` = 'YANG', `modified_date` = '2023-05-11 19:21:00', `last_modified_timestamp` = '2023-08-24 15:52:09' WHERE `id` = 13;
UPDATE `tb_cfg_event_subscribe` SET `event_code` = 60, `api_id` = 6, `notify_rule` = 8, `notify_data` = 2023, `handler_name` = 'messageNotifyHandler', `error_handle` = '', `created_by` = 'YANG', `created_date` = '2023-05-11 19:20:49', `modified_by` = 'YANG', `modified_date` = '2023-05-11 19:21:00', `last_modified_timestamp` = '2023-08-24 11:09:59' WHERE `id` = 14;
UPDATE `tb_cfg_event_subscribe` SET `event_code` = 82, `api_id` = 13, `notify_rule` = 24, `notify_data` = 2023, `handler_name` = 'messageNotifyHandler', `error_handle` = '', `created_by` = 'YANG', `created_date` = '2023-05-11 19:20:49', `modified_by` = 'YANG', `modified_date` = '2023-05-11 19:21:00', `last_modified_timestamp` = '2023-08-23 15:41:53' WHERE `id` = 15;
-- 新增TIC Completed sync report订阅
INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`) VALUES (35, 12, 60, 1, 8, 2023, 'messageNotifyHandler', '', 'YANG', '2023-05-11 19:20:49', 'YANG', '2023-05-11 19:21:00');

-- 更新TIC sync配置
UPDATE `tb_cfg_info` SET  `config_value` = '{\r\n    \"trfOrderRelationshipRule\":4,\r\n    \"statusRule\":0,\r\n    \"statusFlow\":[\r\n        1,\r\n        3,\r\n        4,\r\n        5,\r\n        6,\r\n        7,\r\n				100\r\n    ],\r\n    \"statusMapping\":{\r\n        \"1\":1,\r\n        \"3\":3,\r\n        \"4\":4,\r\n        \"5\":5,\r\n        \"6\":6,\r\n        \"7\":7,\r\n        \"9\":9,\r\n				\"100\":100\r\n    },\r\n    \"actionStatusMapping\":{\r\n        \"Import\":{\r\n            \"orderRefRule\":0,\r\n            \"orderOpType\":1,\r\n            \"ctrlMapping\":{\r\n\r\n            }\r\n        },\r\n        \"SyncToOrder\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":1,\r\n            \"ctrlMapping\":{\r\n                \"3\":0\r\n            }\r\n        },\r\n        \"SyncConfirmed\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"4\":0\r\n            }\r\n        },\r\n        \"SyncTesting\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"5\":0\r\n            }\r\n        },\r\n        \"SyncCompleted\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"6\":0\r\n            }\r\n        },\r\n        \"SyncClosed\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"7\":8\r\n            }\r\n        },\r\n        \"SyncUnBind\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"1\":0,\r\n                \"2\":0,\r\n                \"3\":0,\r\n                \"4\":0,\r\n                \"5\":0\r\n            }\r\n        },\r\n        \"PreOrderReturnTrf\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":3,\r\n            \"ctrlMapping\":{\r\n                \"1\":0\r\n            }\r\n        },\r\n        \"CustomerReturnTrf\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":3,\r\n            \"ctrlMapping\":{\r\n                \"1\":0\r\n            }\r\n        },\r\n        \"SyncReviseReport\": {\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"100\": 0,\r\n								\"6\":0\r\n            }\r\n        }\r\n    }\r\n}', `modified_date` = now()  WHERE `identity_id` = '12' and `product_line` = 'SL' and `config_type` = 2 and `config_key` = 'TrfStatusControl';

-- SGSMart相关


-- 其他数据清洗
-- shein config数据


-- 表结构变动
-- 1.新增report_status字段
ALTER TABLE `tb_trf_report`
    ADD COLUMN `report_status` int UNSIGNED NULL COMMENT '报告状态' AFTER `report_no`;
-- 2.全局statusControl配置更新
-- UPDATE `tb_cfg_info` SET `config_value` = '{\r\n    \"pendingDecision\": \"hold\",\r\n    \"actionStatusMapping\": {\r\n        \"SyncToOrder\": {\r\n            \"statusRule\": 1\r\n        },\r\n        \"SyncConfirmed\": {\r\n            \"statusRule\": 1\r\n        },\r\n        \"SyncTesting\": {\r\n            \"statusRule\": 1\r\n        },\r\n        \"SyncCompleted\": {\r\n            \"statusRule\": 2\r\n        },\r\n        \"SyncClosed\": {\r\n            \"statusRule\": 2\r\n        },\r\n        \"CustomerCancelTrf\": {\r\n            \"ctrlMapping\": {\r\n                \"1\": 0\r\n            }\r\n        },\r\n        \"SyncReviseReport\":{\r\n						\"statusRule\": 1\r\n        },\r\n        \"PreOrderCancelTrf\": {\r\n            \"ctrlMapping\": {\r\n                \"1\": 0,\r\n                \"2\": 0,\r\n                \"3\": 0,\r\n                \"4\": 0,\r\n                \"5\": 0\r\n            }\r\n        }\r\n    }\r\n}' WHERE `identity_id` = '0' and `config_type` = 2 and `config_key` = 'TrfStatusControl';
-- 3.新增使用RD系统配置
INSERT INTO `tb_cfg_info` (`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`) VALUES (9, NULL, NULL, '0', NULL, 2, 'UsedRD', '2', 'system',  now(), 'system', now());
-- 4.dfv验证dict新增（revise）
INSERT INTO `tb_dict_value` (`id`, `system_id`, `dict_type`, `dict_value`, `dict_label`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`) VALUES (20, 40, 'SCI-SyncAction', 'SyncReviseReport', 'SyncReviseReport', 1, 'system', now(), 'system', now());
-- 5.attachment表新增cloudId字段
ALTER TABLE `tb_trf_attachment`
    ADD COLUMN `cloud_id` varchar(500) NULL COMMENT 'cloud id' AFTER `file_size`;
