
INSERT INTO tb_cfg_system_api ( id, system_id, protocol_type, request_url, request_method, request_body_template, response_body_template, remark, created_by, created_date, modified_by, modified_date, last_modified_timestamp ) VALUES (9, 10002, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{}', '{}', 'SGSMart回传能力配置', '<PERSON>ey', now(), '<PERSON>ey', now(), now());

INSERT INTO `tb_cfg_event_subscribe` ( `id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp` ) VALUES ( 29, 10002, 10, 9, 24, 2023, 'messageNotifyHandler', '', '<PERSON>ey', NOW(), '<PERSON><PERSON>', NOW(), NOW());
INSERT INTO `tb_cfg_event_subscribe` ( `id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp` ) VALUES ( 30, 10002, 40, 9, 24, 2023, 'messageNotifyHandler', '', 'Walley', NOW(), 'Walley', NOW(), NOW());
INSERT INTO `tb_cfg_event_subscribe` ( `id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp` ) VALUES ( 31, 10002, 60, 9, 24, 2023, 'messageNotifyHandler', '', 'Walley', NOW(), 'Walley', NOW(), NOW());
INSERT INTO `tb_cfg_event_subscribe` ( `id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp` ) VALUES ( 32, 10002, 100, 9, 24, 2023, 'messageNotifyHandler', '', 'Walley', NOW(), 'Walley', NOW(), NOW());
INSERT INTO `tb_cfg_event_subscribe` ( `id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp` ) VALUES ( 33, 10002, 110, 9, 24, 2023, 'unPendingHandler', '', 'Walley', NOW(), 'Walley', NOW(), NOW());

