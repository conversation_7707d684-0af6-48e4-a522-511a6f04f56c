UPDATE `tb_cfg_system_api` SET `request_body_template` = '{\r\n	\"_init\":{\r\n		\"_filter_reportList\":\"#filterReportForCompleted()\",\r\n		\"_ignoreNode\":{\r\n			\"_ignore1\":\"#buildVirtualNode(\'filterReportList\',@_filter_reportList)\"\r\n		}\r\n	},\r\n    \"bu\": \"$.extra.productLineCode\",\r\n    \"refSystemId\": \"$.extra.refSystemId\",\r\n    \"objectNumber\": \"$.extra.trfNo\",\r\n    \"action\": \"SyncReport\",\r\n    \"dataStatus\": \"#eventToIlayerDataStatus($.extra.eventName)\",\r\n    \"msgId\": \"$.extra.randomSequence\",\r\n    \"body\": {\r\n	    \"orderNo\":\"$.trfList[0].trfNo\",\r\n	    \"platform\":\"SODA\",\r\n	    \"platformOrder\":\"$.trfList[0].trfId\",\r\n	    \"reports\":[\r\n	        {\r\n	            \"status\":\"#reviseReportType($.trfList[0].refSystemId,$.trfList[0].trfNo,$.filterReportList[*].originalReportNo)\",\r\n							\"cloudId\":\"$.filterReportList[*].reportFileList[*].cloudId\",\r\n	            \"fileId\":\"$.filterReportList[*].reportFileList[*].cloudId\",\r\n	            \"fileName\":\"$.filterReportList[*].reportFileList[*].fileName\",\r\n	            \"reportNo\":\"$.filterReportList[*].reportNo\",\r\n	            \"oldNo\":\"$.filterReportList[*].originalReportNo\",\r\n	            \"reportDate\":\"#toDate($.filterReportList[*].softCopyDeliveryDate)\"\r\n	        }\r\n	    ]\r\n	}\r\n}' WHERE `id` = 1;