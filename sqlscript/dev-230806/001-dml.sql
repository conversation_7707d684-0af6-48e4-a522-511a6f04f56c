INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`)
VALUES (25, 10002, 10, 0, 0, 0, 'messageNotifyHandler', '', '<PERSON>ey', NOW(), '<PERSON>ey', NOW(), NOW());
INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`)
VALUES (26, 10002, 40, 0, 0, 0, 'messageNotifyHandler', '', '<PERSON><PERSON>', NOW(), '<PERSON><PERSON>', NOW(), NOW());
INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`)
VALUES (27, 10002, 60, 0, 0, 0, 'messageNotifyHandler', '', 'Walley', NOW(), 'Walley', NOW(), NOW());
INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`)
VALUES (28, 10002, 90, 0, 0, 0, 'messageNotifyHandler', '', 'Walley', NOW(), 'Walley', NOW(), NOW());
INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`)
VALUES (29, 10002, 100, 0, 0, 0, 'messageNotifyHandler', '', 'Walley', NOW(), 'Walley', NOW(), NOW());
INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`)
VALUES (30, 10002, 110, 0, 0, 0, 'unPendingHandler', '', 'Walley', NOW(), 'Walley', NOW(), NOW());

update tb_cfg_info set config_value='{"pendingDecision":"hold","actionStatusMapping":{"CustomerCancelTrf":{"ctrlMapping":{"1":0}},"PreOrderCancelTrf":{"ctrlMapping":{"1":0,"2":0,"3":0,"4":0,"5":0}}}}' where id=5;