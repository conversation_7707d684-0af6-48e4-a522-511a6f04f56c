--- tic报告回传模板更新
UPDATE `tb_cfg_system_api` SET `request_body_template` = '{\r\n    \"bu\":\"$.extra.productLineCode\",\r\n    \"refSystemId\":\"$.extra.refSystemId\",\r\n    \"objectNumber\":\"$.extra.trfNo\",\r\n    \"action\":\"SyncReport\",\r\n    \"dataStatus\":\"#eventToIlayerDataStatus($.extra.eventName)\",\r\n    \"msgId\":\"$.extra.randomSequence\",\r\n    \"body\":{\r\n        \"orderNo\":\"$.trfList[0].trfNo\",\r\n        \"platform\":\"SODA\",\r\n        \"platformOrder\":\"$.trfList[0].trfId\",\r\n        \"reports\":[\r\n            {\r\n                \"status\":\"#reviseReportType($.extra.systemId,$.extra.syncInfo.order.orderNo,$.reportList[*].originalReportNo)\",\r\n                \"cloudId\":\"$.reportList[*].reportFileList[*].cloudId\",\r\n                \"fileId\":\"$.reportList[*].reportFileList[*].cloudId\",\r\n                \"fileName\":\"$.reportList[*].reportFileList[*].fileName\",\r\n                \"reportNo\":\"$.reportList[*].reportNo\",\r\n                \"oldNo\":\"$.reportList[*].originalReportNo\",\r\n                \"reportDate\":\"#toDate($.reportList[*].softCopyDeliveryDate)\"\r\n            }\r\n        ]\r\n    }\r\n}' WHERE `id` = 1;

--- trf_report表新增索引
ALTER TABLE `tb_trf_report`
    ADD INDEX `idx_systemId_orderNo`(`system_id`, `order_no`) USING BTREE;

--- sgsMart数据回传设置为全量
INSERT INTO `tb_cfg_info` (`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (11, NULL, NULL, '2', 'SL', 2, 'DeliveryReportMode', '2', 'system', '2023-09-16 10:11:11', 'system', '2023-09-16 10:11:16', '2023-09-16 10:41:23');
