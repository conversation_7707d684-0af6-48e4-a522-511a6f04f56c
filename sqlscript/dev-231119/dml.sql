-- 修改AF模板
UPDATE `tb_cfg_system_api` SET  `request_body_template` = '{
  "_reports":[
    {
		"_reportNo": "$.reportList[*].reportNo",
      "data": [
        {
				"_reportNo": "$.reportList[*].reportNo",
          "_orderNo": "$.reportList[reportNo=@._reportNo].orderNo",
					"_testSampleInstanceId":"$.reportList[*].reportMatrixList[*].testSampleInstanceId",
					"_testLineInstanceId":"$.reportList[*].reportMatrixList[*].testLineInstanceId",
          "G_ID": "$.testSampleList[testSampleInstanceId=@._testSampleInstanceId][0].external.testSampleId",
          "WTDH": "$.trfList[0].trfNo",
          "SFSC": "$.testSampleList[testSampleInstanceId=@._testSampleInstanceId][0].testSampleNo",
          "SC": "$.testSampleList[testSampleInstanceId=@._testSampleInstanceId][0].external.testSampleNo",
          "PDFTIME": "#toDate($.reportList[*].softCopyDeliveryDate,''yyyy-MM-dd'')",
          "VER": "#yiliReportDeliveryCounter($.extra.refSystemId,$.extra.trfNo)",
          "YPBMWD": null,
          "PDFFILES": [
            {
              "PDFNAME": "$.reportList[reportNo = @_reportNo].reportFileList[*].fileName",
              "PDFSTRING": null,
              "PDFURI": "$.reportList[reportNo = @_reportNo].reportFileList[*].cloudId"
            }
          ],
          "PADATA": [
            {
              "G_ID": "$.reportList[reportNo = @_reportNo].reportMatrixList[*].testMatrixId",
              "SC": null,
              "PA": "$.orderList[0].testItemMappingList[testLineInstanceId =@_testLineInstanceId].external.testItemId",
              "PANAME": "$.orderList[0].testItemMappingList[testLineInstanceId =@_testLineInstanceId].external.testItemName",
              "UNIT": "$.testResultList[testMatrixId = @.G_ID].testResult.resultUnit[0]",
              "VALUE_S": "#listToStr($.testResultList[testMatrixId = @.G_ID].testResult.resultValue)",
              "CONCLUSION": "$.reportList[reportNo =@_reportNo][0].reportMatrixList[testMatrixId = @.G_ID][0].conclusion.customerConclusion",
              "METHOD": "$.testLineList[testLineInstanceId = @_testLineInstanceId][0].citation.languageList[0].citationFullName",
              "METHODNAME": "$.testLineList[testLineInstanceId = @_testLineInstanceId][0].citation.languageList[0].citationFullName",
              "LODORLOQ": "$.testResultList[testMatrixId = @.G_ID].methodLimit.limitType[0]",
              "LIMITVALUE": "$.testResultList[testMatrixId = @.G_ID].methodLimit.limitValueFullName[0]",
              "TESTTIME": "#toDate($.orderList[0].testingStartDate)",
              "TESTENDTIME": "#toDate($.orderList[0].testingEndDate)",
              "EQNAME": null,
              "SYTJ": null,
              "STATUS": null,
              "BZYQ": "$.testResultList[testMatrixId = @.G_ID].reportLimit.limitValueFullName[0]",
              "HighLimit": null,
              "LowLimit": null
            }
          ]
        }
      ]
    }
  ] ,
  "_buildReportsNode": "#buildVirtualNode(''_reports'',@._reports)",
  "action": "SyncReport",
  "dataStatus": "Completed",
  "bu": "$.extra.productLineCode",
  "objectNumber": "$.extra.trfNo",
  "refSystemId": "$.extra.refSystemId",
  "msgId": "$.extra.randomSequence",
  "body": {
    "reports": "$._reports[0].data"
  }
}' WHERE `id` = 20;

-- 去除shein 土耳其 close节点配置
UPDATE `tb_cfg_info` SET  `config_value` = '{\r\n    \"trfOrderRelationshipRule\":2,\r\n    \"statusRule\":4,\r\n    \"statusFlow\":[\r\n        1,\r\n        4,\r\n        5,\r\n        6,\r\n        7\r\n    ],\r\n    \"statusMapping\":{\r\n        \"1\":1,\r\n        \"4\":4,\r\n        \"5\":5,\r\n        \"6\":6,\r\n        \"7\":7,\r\n        \"9\":9\r\n    },\r\n    \"actionStatusMapping\":{\r\n        \"Import\":{\r\n            \"orderRefRule\":0,\r\n            \"orderOpType\":1,\r\n            \"ctrlMapping\":{\r\n\r\n            }\r\n        },\r\n        \"SyncConfirmed\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":1,\r\n            \"ctrlMapping\":{\r\n                \"4\":0\r\n            }\r\n        },\r\n        \"SyncTesting\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"5\":0\r\n            }\r\n        },\r\n        \"SyncCompleted\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"6\":0\r\n            }\r\n        },\r\n        \"SyncUnBind\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":3,\r\n            \"ctrlMapping\":{\r\n                \"1\":0,\r\n                \"2\":0,\r\n                \"3\":0,\r\n                \"4\":0,\r\n                \"5\":0\r\n            }\r\n        }\r\n    }\r\n}' WHERE `id` = 1;

-- 新增tb_trf_report.report_due_date
ALTER TABLE `tb_trf_report`
    ADD COLUMN `report_due_date` datetime NULL AFTER `report_status`;

-- 修改AF回传Time字段取值
UPDATE `tb_cfg_system_api` SET  `request_body_template` = '{\r\n    \"action\":\"SyncOrderStatus\",\r\n    \"dataStatus\":\"ToOrder\",\r\n    \"bu\":\"$.extra.productLineCode\",\r\n    \"objectNumber\":\"$.trfList[0].trfNo\",\r\n    \"refSystemId\":10016,\r\n    \"msgId\":\"$.extra.randomSequence\",\r\n    \"body\":{\r\n        \"WTDH\":\"$.trfList[0].trfNo\",\r\n        \"STATUS\":\"接样\",\r\n        \"Time\":\"#toDate($.extra.operationTime)\",\r\n        \"REMARK\":null\r\n    }\r\n}' WHERE `id` = 16;
UPDATE `tb_cfg_system_api` SET  `request_body_template` = '{\r\n    \"action\":\"SyncOrderStatus\",\r\n    \"dataStatus\":\"Confirmed\",\r\n    \"bu\":\"$.extra.productLineCode\",\r\n    \"objectNumber\":\"$.trfList[0].trfNo\",\r\n    \"refSystemId\":10016,\r\n    \"msgId\":\"$.extra.randomSequence\",\r\n    \"body\":{\r\n        \"WTDH\":\"$.trfList[0].trfNo\",\r\n        \"STATUS\":\"受理\",\r\n        \"Time\":\"#toDate($.extra.operationTime)\",\r\n        \"REMARK\":null\r\n    }\r\n}' WHERE `id` = 17;
UPDATE `tb_cfg_system_api` SET  `request_body_template` = '{\r\n    \"action\":\"SyncOrderStatus\",\r\n    \"dataStatus\":\"Testing\",\r\n    \"bu\":\"$.extra.productLineCode\",\r\n    \"objectNumber\":\"$.trfList[0].trfNo\",\r\n    \"refSystemId\":10016,\r\n    \"msgId\":\"$.extra.randomSequence\",\r\n    \"body\":{\r\n        \"WTDH\":\"$.trfList[0].trfNo\",\r\n        \"STATUS\":\"检测中\",\r\n        \"Time\":\"#toDate($.extra.operationTime)\",\r\n        \"REMARK\":null\r\n    }\r\n}' WHERE `id` = 18;
UPDATE `tb_cfg_system_api` SET  `request_body_template` = '{\r\n    \"action\":\"SyncOrderStatus\",\r\n    \"dataStatus\":\"Completed\",\r\n    \"bu\":\"$.extra.productLineCode\",\r\n    \"objectNumber\":\"$.trfList[0].trfNo\",\r\n    \"refSystemId\":10016,\r\n    \"msgId\":\"$.extra.randomSequence\",\r\n    \"body\":{\r\n        \"WTDH\":\"$.trfList[0].trfNo\",\r\n        \"STATUS\":\"报告出具\",\r\n        \"Time\":\"#toDate($.extra.operationTime)\",\r\n        \"REMARK\":null\r\n    }\r\n}' WHERE `id` = 19;

-- 优衣库相关配置
INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (16, '', '', '10017', 'SL', 2, 'TrfStatusControl', '{\n     \"trfOrderRelationshipRule\": 2,\n     \"statusRule\": 0,\n     \"statusFlow\": [\n         1,\n         3,\n         4,\n         5,\n         6,\n         7,\n         100\n     ],\n     \"statusMapping\": {\n         \"1\": 1,\n         \"3\": 3,\n         \"4\": 4,\n         \"5\": 5,\n         \"6\": 6,\n         \"7\": 7,\n         \"100\": 100\n     },\n     \"actionStatusMapping\": {\n         \"Import\": {\n             \"orderRefRule\": 0,\n             \"orderOpType\": 1,\n             \"ctrlMapping\": {}\n         },\n         \"SyncToOrder\": {\n             \"orderRefRule\": 1,\n             \"orderOpType\": 1,\n             \"ctrlMapping\": {\n                 \"3\": 0\n             }\n         },\n         \"SyncConfirmed\": {\n             \"orderRefRule\": 1,\n             \"orderOpType\": 1,\n             \"ctrlMapping\": {\n                 \"4\": 0\n             }\n         },\n         \"SyncTesting\": {\n             \"orderRefRule\": 1,\n             \"orderOpType\": 1,\n             \"ctrlMapping\": {\n                 \"5\": 0\n             }\n         },\n         \"SyncCompleted\": {\n             \"orderRefRule\": 1,\n             \"orderOpType\": 7,\n             \"ctrlMapping\": {\n                 \"6\": 0\n             }\n         },\n         \"SyncClosed\": {\n             \"orderRefRule\": 1,\n             \"orderOpType\": 7,\n             \"ctrlMapping\": {\n                 \"7\": 0\n             }\n         },\n         \"SyncPending\": {\n             \"orderRefRule\": 1,\n             \"orderOpType\": 7,\n             \"ctrlMapping\": {\n                 \"1\": 1,\n                 \"3\": 3,\n                 \"4\": 4,\n                 \"5\": 5\n             }\n         },\n         \"SyncUnPending\": {\n             \"orderRefRule\": 1,\n             \"orderOpType\": 7,\n             \"ctrlMapping\": {\n                 \"1\": 1,\n                 \"3\": 3,\n                 \"4\": 4,\n                 \"5\": 5\n             }\n         },\n         \"SyncUnBind\": {\n             \"orderRefRule\": 1,\n             \"orderOpType\": 2,\n             \"ctrlMapping\": {\n                 \"3\": 3,\n                 \"5\": 5,\n                 \"4\": 4\n             }\n         },\n         \"SyncReviseReport\": {\n             \"orderOpType\": 7,\n             \"ctrlMapping\": {\n                 \"100\": 0,\n 		\"6\":0\n             }\n         },\n         \"PreOrderReturnTrf\":{\n             \"orderRefRule\":1,\n             \"orderOpType\":3,\n             \"ctrlMapping\":{\n                 \"1\":0\n             }\n         },\n         \"CustomerReturnTrf\":{\n             \"orderRefRule\":1,\n             \"orderOpType\":3,\n             \"ctrlMapping\":{\n                 \"1\":0\n             }\n         }\n     }\n }', 'system', '2023-05-11 15:12:36', 'system', '2023-05-15 08:20:58', '2023-11-17 18:48:02');
INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (17, NULL, NULL, '10017', 'SL', 2, 'DeliveryReportMode', '1', 'system', '2023-09-27 14:35:20', 'system', '2023-09-27 14:35:23', '2023-10-27 20:11:29');
INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (18, 'CG0001085', '', '10017', 'SL', 2, 'DataSend', '{\"mode\":1,\"cronExpression\":\"0 */1 * * * ?\",\"identityId\":10017}', 'system', '2023-10-27 15:12:36', 'system', '2023-10-27 15:12:36', '2023-11-09 14:47:19');
INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (19, NULL, NULL, '10017', 'SL', 2, 'OriginReportDataMode', '1', 'system', '2023-11-08 19:25:19', 'system', '2023-11-08 19:25:25', '2023-11-08 19:25:25');
INSERT INTO `tb_cfg_system_api`(`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (21, 10017, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{\r\n  \"body\":{\r\n		\"_timeStr\":\"#dateTimeNameForMart(yyyyMMddHHmmssSSS)\",\r\n    \"zipFileName\":\"#dateTimeNameForMart(AIF1161X01_20100091_,@._timeStr,.zip)\",\r\n    \"report\":{\r\n		\"_timeStrTwo\":\"#dateTimeNameForMart()\",\r\n      \"header\": {\r\n        \"fileName\": \"#dateTimeNameForMart(Quality_Result_Header_,@_timeStrTwo,.csv)\",\r\n        \"data\":[\r\n          {\r\n            \"reportNo\": \"$.extra.syncInfo.allReportList[*].reportNo\",\r\n            \"testInstitutionCode\": \"#mapping($.extra.syncInfo.allReportList[*].lab.labCode,\'HK SL\':\'sgs001\'&\'SH SL\':\'sgs002\'&\'QD SL\':\'sgs003\'&\'GZ SL\':\'sgs004\'&\'CZ SL\':\'sgs005\')\",\r\n            \"testRequestFlag\": \"1\",\r\n            \"brandCode\": \"010\",\r\n            \"previousReportNo\":\"$.orderList[0].productList[0].productAttrList[labelCode=\'PreviousReportNo\'][0].value\",\r\n            \"reportType\": \"$.orderList[0].productList[0].productAttrList[labelCode=\'SpecialCustomerAttribute4\'][0].value\",\r\n            \"season\": \"$.orderList[0].productList[0].productAttrList[labelCode=\'Season\'][0].value\",\r\n            \"phase\": \"$.orderList[0].productList[0].productAttrList[labelCode=\'SpecialProductAttribute4\'][0].value\",\r\n            \"managementFactoryCode\": \"$.orderList[0].productList[0].productAttrList[labelCode=\'SpecialProductAttribute5\'][0].value\",\r\n            \"testRequestNumber\": \"$.extra.trfNo\",\r\n            \"receiptDate\": \"#toDate($.orderList[0].productList[0].productAttrList[labelCode=\'SampleReceivedDate\'][0].value,\'yyyy-MM-dd\')\",\r\n            \"issuranceDate\": \"#toDate($.extra.syncInfo.allReportList[*].softCopyDeliveryDate,\'yyyy-MM-dd\')\",\r\n            \"comment\": null,\r\n            \"URL\": \"$.extra.syncInfo.allReportList[*].reportFileList[0].cloudId\",\r\n            \"reportFileName\": \"$.extra.syncInfo.allReportList[*].reportFileList[0].fileName\",\r\n            \"updateType\": \"#uNIQLOUpdateTypeFn($.extra.syncInfo.allReportList[*].reportNo,$.extra.syncInfo.allReportList[*].lab.labCode,$.orderList[0].systemId,$.extra.syncInfo.allReportList[*].originalReportNo)\"\r\n          }\r\n        ]\r\n      },\r\n      \"detail\": {\r\n        \"fileName\": \"#dateTimeNameForMart(Quality_Result_Detail_,@_timeStrTwo,.csv)\",\r\n        \"data\": [{\r\n					\"_sampleInstanceId\":\"$.reportConclusionList[conclusionLevelId=604].objectId\",\r\n					\"_testLineInstanceId\":\"$.reportConclusionList[conclusionLevelId=604].testLineInstanceId\",\r\n					\"_reportId\":\"$.reportConclusionList[conclusionLevelId=604].reportId\",\r\n					\"_report\":\"$.reportList[reportId=@._reportId][0]\",\r\n					\"_reportNo\": \"#getReportByTestMatrixIdFn(@._report)\",\r\n          \"reportNo\": \"#getReportByTestMatrixIdFn(@._report)\",\r\n          \"testInstitutionCode\": \"#mapping($.reportList[reportNo=@.reportNo][0].lab.labCode,\'HK SL\':\'sgs001\'&\'SH SL\':\'sgs002\'&\'QD SL\':\'sgs003\'&\'GZ SL\':\'sgs004\'&\'CZ SL\':\'sgs005\')\",\r\n          \"testType\": \"#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._sampleInstanceId,SpecialCustomerAttribute2,2)\",\r\n          \"testTargetCode\": \"#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._sampleInstanceId,SpecialProductAttribute2,2)\",\r\n          \"colorCode\":\"#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._sampleInstanceId,ProductColor,2)\",\r\n          \"colorName\": \"#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._sampleInstanceId,SpecialCustomerAttribute1,2)\",\r\n          \"testItemKeyCode\": \"#uNIQLOTestLineMappingFn($.testLineList[testLineInstanceId=@._testLineInstanceId][0],1)\",\r\n          \"testShareSampleCode\": \"#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._sampleInstanceId,SpecialProductAttribute3,2)\",\r\n          \"supplierRawMaterialCode\": \"#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._sampleInstanceId,SpecialCustomerAttribute3,2)\",\r\n          \"lotNo\": \"#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._sampleInstanceId,RefCode4,2)\",\r\n          \"remark1\": null,\r\n          \"remark2\": null,\r\n          \"result\": \"#uNIQLOResultMappingFn($.reportConclusionList[conclusionLevelId=604].conclusionCode)\",\r\n          \"comment\": \"$.reportConclusionList[conclusionLevelId=604].conclusionRemark\",\r\n          \"updateType\": \"U\"\r\n        }\r\n      ]},\r\n      \"subDetail\": {\r\n        \"fileName\":	\"#dateTimeNameForMart(Quality_Result_SubDetail_,@_timeStrTwo,.csv)\",\r\n        \"data\":[\r\n          {\r\n					\"_testMatrixId\":\"$.testResultList[*].testMatrixId\",\r\n					\r\n					\"_testLineInstanceId\":\"#getReportByTestMatrixIdFn($.reportList,@._testMatrixId,3)\",\r\n					\"_testSampleInstanceId\":\"#getReportByTestMatrixIdFn($.reportList,@._testMatrixId,2)\",\r\n					\"_positionInstanceId\":\"$.testResultList[*].testResult.testResultFullNameRel.positionInstanceId\",\r\n					\"_analyteInstanceId\":\"$.testResultList[*].testResult.testResultFullNameRel.analyteInstanceId\",\r\n					\"_testMatrix\":\"#getReportByTestMatrixIdFn($.reportList,@._testMatrixId,4)\",\r\n					\"_testLine\":\"$.testLineList[testLineInstanceId=@._testLineInstanceId][0]\",\r\n            \"reportNo\": \"#getReportByTestMatrixIdFn($.reportList,@._testMatrixId,1)\",\r\n            \"testInstitutionCode\": \"#mapping($.reportList[reportNo=@.reportNo][0].lab.labCode,\'HK SL\':\'sgs001\'&\'SH SL\':\'sgs002\'&\'QD SL\':\'sgs003\'&\'GZ SL\':\'sgs004\'&\'CZ SL\':\'sgs005\')\",\r\n            \"testType\": \"#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._testSampleInstanceId,SpecialCustomerAttribute2,1)\",\r\n            \"testTargetCode\": \"#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._testSampleInstanceId,SpecialProductAttribute2,1)\",\r\n            \"colorCode\":\"#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._testSampleInstanceId,ProductColor,1)\",\r\n            \"colorName\": \"#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._testSampleInstanceId,SpecialCustomerAttribute1,1)\",\r\n            \"testItemKeyCode\": \"#uNIQLOTestLineMappingFn($.testLineList[testLineInstanceId=@._testLineInstanceId][0],1)\",\r\n            \"testMethodCode\": \"#uNIQLOTestLineMappingFn($.testLineList[testLineInstanceId=@._testLineInstanceId][0],2)\",\r\n            \"jugdementDevisionCode\": \"#uNIQLOTestLineMappingFn($.testLineList[testLineInstanceId=@._testLineInstanceId][0],3,SL,@._analyteInstanceId)\",\r\n            \"testObjectNo\": \"$.testSampleList[testSampleInstanceId=@._testSampleInstanceId][0].testSampleNo\",\r\n            \"testActualColorName\": \"$.testSampleList[testSampleInstanceId=@._testSampleInstanceId][0].materialAttr.materialColor\",\r\n            \"testCondition1\": \"#uNIQLOGetValueFormListFn(@._testMatrix,@._positionInstanceId,positionId)\",\r\n            \"testCondition2\": null,\r\n            \"testCondition3\": null,\r\n            \"testCondition4\": \"#uNIQLOGetValueFormListFn(@._testLine,@._analyteInstanceId,analyteId)\",\r\n            \"testCondition5\": null,\r\n            \"testCondition6\": null,\r\n            \"unit\": \"#uNIQLOUnitMappingFn($.testResultList[*],SL,$.testLineList[testLineInstanceId=@._testLineInstanceId][0])\",\r\n            \"testValue\": \"$.testResultList[*].testResult.resultValue\",\r\n            \"result\": null,\r\n            \"comment\": null\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n	\"action\": \"SyncReport\",\r\n  \"dataStatus\": \"Completed\",\r\n  \"bu\": \"$.extra.productLineCode\",\r\n  \"objectNumber\": \"$.extra.trfNo\",\r\n  \"refSystemId\": \"$.extra.refSystemId\",\r\n  \"msgId\": \"$.extra.randomSequence\"\r\n}', '{}', '优衣库报告数据同步接口（Completed）', 'YANG', '2023-09-08 13:42:12', 'YANG', '2023-09-08 13:44:20', '2023-11-17 11:20:16');
INSERT INTO `tb_dict_value`(`id`, `system_id`, `dict_type`, `dict_value`, `dict_label`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (23, 40, 'RefSystemId', '10017', '优衣库系统', 1, 'system', '2023-10-26 11:22:10', 'system', '2023-10-26 11:22:16', '2023-10-26 11:22:20');
INSERT INTO `tb_cfg_event_subscribe`(`id`, `system_id`, `event_code`, `api_id`, `priority`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`, `handler_name`, `error_handle`) VALUES (47, 10017, 60, 21, 0, 0, 8167, 'Lester', '2023-10-26 11:20:04', 'Lester', '2023-10-26 11:20:13', '2023-10-31 15:19:45', 'messageNotifyHandler', '');


-- If there is a system exception after executing the insert statement, please execute the corresponding rollback statement

INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`)
VALUES (48, 12, 40, 13, 24, 2023, 'messageNotifyHandler', '', 'system', NOW(), 'system', NOW(), NOW());

-- roll back sql delete  from tb_cfg_event_subscribe where system_id = 12 and event_code = '40';
INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`)
VALUES (49, 12, 100, 13, 24, 2023, 'messageNotifyHandler', '', 'system', NOW(), 'system', NOW(), NOW());

-- roll back sql delete  from tb_cfg_event_subscribe where system_id = 12 and event_code = '100';

INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`)
VALUES (50, 12, 110, 13, 24, 2023, 'unPendingHandler', '', 'system', NOW(), 'system', NOW(), NOW());

-- roll back sql delete  from tb_cfg_event_subscribe where system_id = 12 and event_code = '110';

-- system_id = 12 and id = 1 的 rollback sql
--update tb_cfg_system_api  set request_body_template = '{
--     "bu":"$.extra.productLineCode",
--     "refSystemId":"$.extra.refSystemId",
--     "objectNumber":"$.extra.trfNo",
--     "action":"SyncReport",
--     "dataStatus":"#eventToIlayerDataStatus($.extra.eventName)",
--     "msgId":"$.extra.randomSequence",
--     "body":{
--         "orderNo":"$.trfList[0].trfNo",
--         "platform":"SODA",
--        "platformOrder":"$.trfList[0].trfId",
--        "reports":[
--             {
--                "status":"#reviseReportType($.extra.systemId,$.extra.syncInfo.order.orderNo,$.reportList[*].originalReportNo)",
--                 "cloudId":"$.reportList[*].reportFileList[*].cloudId",
--                "fileId":"$.reportList[*].reportFileList[*].cloudId",
--                 "fileName":"$.reportList[*].reportFileList[*].fileName",
--                 "reportNo":"$.reportList[*].reportNo",
--                 "oldNo":"$.reportList[*].originalReportNo",
--                "reportDate":"#toDate($.reportList[*].softCopyDeliveryDate)"
--             }
--         ]
--     }
-- }' where system_id = 12 and id = 1;




update tb_cfg_system_api  set request_body_template = '{
     "bu":"$.extra.productLineCode",
     "refSystemId":"$.extra.refSystemId",
     "objectNumber":"$.extra.trfNo",
     "action":"SyncReport",
     "dataStatus":"#eventToIlayerDataStatus($.extra.eventName)",
     "msgId":"$.extra.randomSequence",
     "body":{
         "orderNo":"$.trfList[0].trfNo",
         "platform":"SODA",
         "platformOrder":"$.trfList[0].trfId",
         "reports":[
             {
                 "status":"#reviseReportType($.extra.systemId,$.extra.syncInfo.order.orderNo,$.reportList[*].originalReportNo)",
                 "cloudId":"$.reportList[*].reportFileList[*].cloudId",
                 "fileId":"$.reportList[*].reportFileList[*].cloudId",
                 "fileName":"$.reportList[*].reportFileList[*].fileName",
                 "reportNo":"$.reportList[*].reportNo",
                 "oldNo":"$.reportList[*].originalReportNo",
                 "reportDate":"#toDate($.reportList[*].softCopyDeliveryDate)"
             }
         ]
     }
 }' where system_id = 12 and id = 1;

-- system_id = 12 and id = 13 的 rollback sql

--update tb_cfg_system_api  set request_body_template = '{
--  "bu": "$.extra.productLineCode",
--  "refSystemId": "$.extra.refSystemId",
--  "objectNumber": "$.extra.trfNo",
--  "action": "SyncOrderStatus",
--  "dataStatus": "#eventToIlayerDataStatus($.extra.eventName)",
--  "msgId": "$.extra.randomSequence",
--  "body": {
--  "orderNo":"$.trfList[0].trfNo",
--  "platform":"SODA",
--  "platformOrder":"$.trfList[0].trfId",
--  "orderState":"#actionMapping($extra.eventName,\'SyncToOrder\':64&\'SyncConfirmed\':14&\'SyncCompleted\':80&\'PreOrderCancelTrf\':91)",
--  "orderAmount":null,
--  "memo":"#getCancelTypeDesc($.trfList[0].others.cancel.cancelType)",
--  "operatorDate":"#toDate($.extra.syncInfo.reportList[0].reportDueDate)",
--  "csCode":"$.trfList[0].lab.labContact.contactName"
--  }
-- }' where system_id = 12 and id = 13;


update tb_cfg_system_api  set request_body_template = '{
   "bu": "$.extra.productLineCode",
    "refSystemId": "$.extra.refSystemId",
    "objectNumber": "$.extra.trfNo",
    "action": "SyncOrderStatus",
    "dataStatus": "#eventToIlayerDataStatus($.extra.eventName)",
    "msgId": "$.extra.randomSequence",
    "body": {
		"orderNo":"$.trfList[0].trfNo",
		"platform":"SODA",
		"platformOrder":"$.trfList[0].trfId",
		"orderState":"#actionMapping($extra.eventName,\'SyncToOrder\':64&\'SyncConfirmed\':14&\'SyncCompleted\':80&\'PreOrderCancelTrf\':91&\'SyncTesting\':50&\'SyncReporting\':51&\'SyncPending\':52&\'SyncUnPending\':52)",
		"orderAmount":null,
		"memo":"#getTypeDescFn($extra.eventName,$.trfList[0].others.cancel.cancelType,$.trfList[0].others.pending.pendingType)",
		"operatorDate":"#getDateFn($.extra.eventName,,$.extra.operationTime)",
		"csCode":"$.trfList[0].lab.labContact.contactName",
		"pendingState":"#pendingFn($extra.eventName)",
		"preReportDate":"#toDate($extra.reportDueDate)"
    }
 }' where system_id = 12 and id = 13;

update tb_cfg_info set config_value = '{"trfOrderRelationshipRule":4,"statusRule":4,"statusFlow":[1,3,4,5,6,7,100],"statusMapping":{"1":1,"3":3,"4":4,"5":5,"6":6,"7":7,"100":100},"actionStatusMapping":{"Import":{"orderRefRule":0,"orderOpType":1,"ctrlMapping":{}},"SyncToOrder":{"orderRefRule":1,"orderOpType":1,"ctrlMapping":{"3":0}},"SyncConfirmed":{"orderRefRule":1,"orderOpType":1,"ctrlMapping":{"4":0}},"SyncTesting":{"orderRefRule":1,"orderOpType":1,"ctrlMapping":{"5":0}},"SyncCompleted":{"orderRefRule":1,"orderOpType":7,"ctrlMapping":{"6":0}},"SyncClosed":{"orderRefRule":1,"orderOpType":7,"ctrlMapping":{"7":0}},"SyncPending":{"orderRefRule":1,"orderOpType":7,"ctrlMapping":{"1":1,"3":3,"4":4,"5":5}},"SyncUnPending":{"orderRefRule":1,"orderOpType":7,"ctrlMapping":{"1":1,"3":3,"4":4,"5":5}},"SyncUnBind":{"orderRefRule":1,"orderOpType":2,"ctrlMapping":{"3":3,"4":4,"5":5}},"SyncReviseReport":{"orderOpType":7,"ctrlMapping":{"6":0,"100":0}},"PreOrderReturnTrf":{"orderRefRule":1,"orderOpType":3,"ctrlMapping":{"1":0}},"CustomerReturnTrf":{"orderRefRule":1,"orderOpType":3,"ctrlMapping":{"1":0}}}}'
where config_key = 'TrfStatusControl' and identity_id = 2 and product_line = 'SL' and id = 3;

update tb_cfg_info set config_value = '{"trfOrderRelationshipRule":4,"statusRule":0,"statusFlow":[1,3,4,5,6,7,100],"statusMapping":{"1":1,"3":3,"4":4,"5":5,"6":6,"7":7,"9":9,"100":100},"actionStatusMapping":{"Import":{"orderRefRule":0,"orderOpType":1,"ctrlMapping":{}},"SyncToOrder":{"orderRefRule":1,"orderOpType":1,"ctrlMapping":{"3":0}},"SyncConfirmed":{"orderRefRule":1,"orderOpType":1,"ctrlMapping":{"4":0}},"SyncTesting":{"orderRefRule":1,"orderOpType":1,"ctrlMapping":{"5":0}},"SyncCompleted":{"orderRefRule":1,"orderOpType":7,"ctrlMapping":{"6":0}},"SyncClosed":{"orderRefRule":1,"orderOpType":7,"ctrlMapping":{"7":0}},"SyncUnBind":{"orderRefRule":1,"orderOpType":7,"ctrlMapping":{"3":0,"4":0,"5":0}},"PreOrderReturnTrf":{"orderRefRule":1,"orderOpType":3,"ctrlMapping":{"1":0}},"CustomerReturnTrf":{"orderRefRule":1,"orderOpType":3,"ctrlMapping":{"1":0}},"SyncReviseReport":{"orderOpType":7,"ctrlMapping":{"100":0}},"SyncPending":{"orderRefRule":1,"orderOpType":7,"ctrlMapping":{"1":1,"3":3,"4":4,"5":5}},"SyncUnPending":{"orderRefRule":1,"orderOpType":7,"ctrlMapping":{"1":1,"3":3,"4":4,"5":5}}}}'
where config_key = 'TrfStatusControl' and identity_id = 12 and product_line = 'SL' and id = 6;

update tb_cfg_info set config_value = '{"trfOrderRelationshipRule":4,"statusRule":4,"statusFlow":[1,3,4,5,6,7,100],"statusMapping":{"1":1,"3":3,"4":4,"5":5,"6":6,"7":7,"100":100},"actionStatusMapping":{"Import":{"orderRefRule":0,"orderOpType":1,"ctrlMapping":{}},"SyncToOrder":{"orderRefRule":1,"orderOpType":1,"ctrlMapping":{"3":0}},"SyncConfirmed":{"orderRefRule":1,"orderOpType":1,"ctrlMapping":{"4":0}},"SyncTesting":{"orderRefRule":1,"orderOpType":1,"ctrlMapping":{"5":0}},"SyncCompleted":{"orderRefRule":1,"orderOpType":7,"ctrlMapping":{"6":0}},"SyncClosed":{"orderRefRule":1,"orderOpType":7,"ctrlMapping":{"7":0}},"SyncPending":{"orderRefRule":1,"orderOpType":7,"ctrlMapping":{"1":1,"3":3,"4":4,"5":5}},"SyncUnPending":{"orderRefRule":1,"orderOpType":7,"ctrlMapping":{"1":1,"3":3,"4":4,"5":5}},"SyncUnBind":{"orderRefRule":1,"orderOpType":2,"ctrlMapping":{"3":3,"4":4,"5":5}},"SyncReviseReport":{"orderOpType":7,"ctrlMapping":{"6":0,"100":0}},"PreOrderReturnTrf":{"orderRefRule":1,"orderOpType":3,"ctrlMapping":{"1":0}},"CustomerReturnTrf":{"orderRefRule":1,"orderOpType":3,"ctrlMapping":{"1":0}}}}'
where config_key = 'TrfStatusControl' and identity_id = 10017 and product_line = 'SL' and id = 16;
