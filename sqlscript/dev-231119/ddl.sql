

CREATE TABLE `tb_trf_child_order` (
                                      `id` bigint(20) NOT NULL COMMENT 'ID唯一标识',
                                      `system_id` int(11) DEFAULT NULL COMMENT '执行系统编号',
                                      `parent_order_id` varchar(80) DEFAULT NULL COMMENT '执行系统Order ID',
                                      `parent_order_no` varchar(80) DEFAULT NULL COMMENT '执行系统Order No',
                                      `order_no` varchar(80) DEFAULT NULL COMMENT '执行系统子订单 Order ID',
                                      `last_modified_timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
                                      PRIMARY KEY (`id`),
                                      KEY `temp_idx1` (`order_no`,`system_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='执行系统子订单信息表';


alter table tb_customer_trf_info modify id bigint(20);
alter table tre_customer_trf_reason_relationship modify TrfInfoId bigint(20);