update tb_cfg_event_subscribe set subscriber = ref_system_id;

update tb_task_info set ref_system_id = identity_id;

-- update systemAPI.28 记录
UPDATE `tb_cfg_system_api` SET `system_id` = 10002, `protocol_type` = 2, `request_url` = 'com.sgs.extsystem.sync.order.forILayer', `request_method` = 2, `request_body_template` = '{\r\n    \"action\":\"OrderToTrf\",\r\n    \"dataStatus\":\"New\",\r\n    \"bu\":\"SL\",\r\n    \"objectNumber\":\"$.trfList[0].trfNo\",\r\n    \"refSystemId\":2,\r\n    \"msgId\":\"$.extra.randomSequence\",\r\n    \"body\":{\r\n        \"trfTemplateId\":null,\r\n        \"templateName\":null,\r\n        \"productLineId\":null,\r\n        \"productLineCode\":\"$.trfList[0].lab.buCode\",\r\n        \"remark\":\"$.trfList[0].others.trfRemark\",\r\n        \"orderNo\":\"\",\r\n        \"serviceType\":\"$.trfList[0].serviceType\",\r\n        \"serviceTypeName\":\"$.trfList[0].serviceType\",\r\n        \"reportDeliveredTo\":\"$.trfList[0].serviceRequirement.report.softcopy.deliveryTo\",\r\n        \"failedReportDeliveredTo\":\"$.trfList[0].serviceRequirement.report.softcopy.deliveryTo\",\r\n        \"applicantUser\":\"$.trfList[0].lab.labContact.contactName\",\r\n        \"testPackageAllIds\":\"\",\r\n        \"trfType\":10,\r\n        \"customerReferenceNo\":\"$.trfList[0].trfNo\",\r\n        \"isSelfRefrence\":0,\r\n        \"trfSourceId\":null,\r\n        \"trfSourceType\":4,\r\n        \"isGeneralRequest\":0,\r\n        \"dffFormId\":null,\r\n        \"dffGridId\":null,\r\n        \"materialIds\":null,\r\n        \"actionType\":\"add\",\r\n        \"dffFormData\":null,\r\n        \"dffGridData\":null,\r\n        \"careLabelData\":null,\r\n        \"testPackageIds\":null,\r\n        \"trfCustomer\":{\r\n            \"buyerCustomerId\":\"$.customerList[customerUsage=3].customerId\",\r\n            \"buyerAccountId\":\"$.customerList[customerUsage=3].customerContactList[0].customerContactId\",\r\n            \"buyerCustomerNameCn\":\"$.customerList[customerUsage=3].languageList[language=2].customerName\",\r\n            \"buyerCustomerNameEn\":\"$.customerList[customerUsage=3].customerName\",\r\n            \"buyerCustomerGroupId\":null,\r\n            \"buyerCustomerGroupCode\":\"$.customerList[customerUsage=3].customerGroupCode\",\r\n            \"buyerCustomerGroupName\":\"$.customerList[customerUsage=3].customerName\",\r\n            \"agentCustomerId\":\"$.customerList[customerUsage=4].customerId\",\r\n            \"agentAccountId\":\"$.customerList[customerUsage=4].customerContactList[0].customerContactId\",\r\n            \"agentCustomerNameCn\":\"$.customerList[customerUsage=4].languageList[languageId=2].customerName\",\r\n            \"agentCustomerNameEn\":\"$.customerList[customerUsage=4].customerName\",\r\n            \"agentCustomerGroupId\":null,\r\n            \"agentCustomerGroupCode\":\"$.customerList[customerUsage=4].customerGroupCode\",\r\n            \"agentCustomerGroupName\":\"$.customerList[customerUsage=4].customerName\",\r\n            \"sgsCustomerId\":null,\r\n            \"sgsAccountId\":null,\r\n            \"customerId\":\"$.customerList[customerUsage=1].customerId\",\r\n            \"customerNo\":\"$.customerList[customerUsage=1].bossNo\",\r\n            \"trfCustomerRole\":null,\r\n            \"customerAddressId\":null,\r\n            \"customerNameZh\":\"$.customerList[customerUsage=1].languageList[languageId=2].customerName\",\r\n            \"customerNameEn\":\"$.customerList[customerUsage=1].customerName\",\r\n            \"customerAddressZh\":\"$.customerList[customerUsage=1].languageList[languageId=2].customerAddress\",\r\n            \"customerAddressEn\":\"$.customerList[customerUsage=1].customerName\",\r\n            \"payCustomerNameZh\":\"$.customerList[customerUsage=2].languageList[languageId=2].customerName\",\r\n            \"payCustomerNameEn\":\"$.customerList[customerUsage=2].customerName\",\r\n            \"payCustomerAddressZh\":\"$.customerList[customerUsage=2].languageList[languageId=2].customerAddress\",\r\n            \"payCustomerAddressEn\":\"$.customerList[customerUsage=2].customerName\",\r\n            \"isSame\":1,\r\n            \"beLongId\":null\r\n        },\r\n        \"trfCustomerContact\":{\r\n            \"applyContactName\":\"$.customerList[customerUsage=1].customerContactList[0].contactName\",\r\n            \"applyContactTel\":\"$.customerList[customerUsage=1].customerContactList[0].contactTelephone\",\r\n            \"applyContactEmail\":\"$.customerList[customerUsage=1].customerContactList[0].contactEmail\",\r\n            \"applyContactFax\":\"$.customerList[customerUsage=1].customerContactList[0].contactFax\",\r\n            \"payContactName\":\"$.customerList[customerUsage=2].customerContactList[0].contactName\",\r\n            \"payContactTel\":\"$.customerList[customerUsage=2].customerContactList[0].contactTelephone\",\r\n            \"payContactEmail\":\"$.customerList[customerUsage=2].customerContactList[0].contactEmail\",\r\n            \"payContactFax\":\"$.customerList[customerUsage=2].customerContactList[0].contactFax\",\r\n            \"isSame\":1\r\n        },\r\n        \"invoice\":{\r\n            \"invoiceType\":\"$.trfList[0].serviceRequirement.invoice.invoiceType\",\r\n            \"invoiceTypeName\":null,\r\n            \"invoiceTitle\":\"$.trfList[0].serviceRequirement.invoice.invoiceTitle\",\r\n            \"taxNo\":\"$.trfList[0].serviceRequirement.invoice.taxNo\",\r\n            \"invoiceTel\":\"$.trfList[0].serviceRequirement.invoice.registerPhone\",\r\n            \"invoiceAddress\":\"$.trfList[0].serviceRequirement.invoice.registerAddr\",\r\n            \"bankName\":\"$.trfList[0].serviceRequirement.invoice.bankName\",\r\n            \"bankAccount\":\"$.trfList[0].serviceRequirement.invoice.bankNumber\"\r\n        },\r\n        \"trfLab\":{\r\n            \"labCode\":\"$.trfList[0].lab.labCode\",\r\n            \"labName\":null,\r\n            \"labAddress\":null,\r\n            \"locationId\":null,\r\n            \"locationCode\":null,\r\n            \"locationName\":null,\r\n            \"countryCode\":null,\r\n            \"countryName\":null,\r\n            \"countryId\":null\r\n        },\r\n        \"trfLabContact\":{\r\n            \"labCode\":\"$.trfList[0].lab.labCode\",\r\n            \"labName\":null,\r\n            \"labAddress\":null,\r\n            \"contactName\":\"$.trfList[0].lab.labContact.contactName\",\r\n            \"contactTel\":\"$.trfList[0].lab.labContact.contactTelephone\",\r\n            \"contactEmail\":\"$.trfList[0].lab.labContact.contactEmail\"\r\n        },\r\n        \"trfProduct\":{\r\n            \"dffFormId\":\"$.productList[0].templateId\",\r\n            \"dffGridId\":\"$.sampleList[0].templateId\",\r\n            \"dffFormData\":\"#dffJsonFn(productList,$.productList)\",\r\n            \"dffGridData\":\"#dffJsonFn(sampleList,$.sampleList)\"\r\n        },\r\n        \"servicRequire\":{\r\n            \"isComment\":null,\r\n            \"isCopy\":null,\r\n            \"isPhoto\":\"$.trfList[0].serviceRequirement.report.needPhoto\",\r\n            \"isConfimCover\":null,\r\n            \"isQuotation\":null,\r\n            \"returnSampleRequire\":\"#sgsMartGetReturnSampleRequireFn($.serviceRequirement.sample)\",\r\n            \"returnSampleName\":null,\r\n            \"reportLanguage\":\"$.trfList[0].serviceRequirement.report.reportLanguage\",\r\n	    \"reportLanguageName\":\"#mapping($.trfList[0].serviceRequirement.report.reportLanguage,\'1\':\'英文报告\'&\'2\':\'中文报告\'&\'3\':\'中英文报告\')\",\r\n            \"reportHeader\":\"$.trfList[0].serviceRequirement.report.languageList[languageId=2].reportHeader \",\r\n            \"reportAddress\":\"$.trfList[0].serviceRequirement.report.languageList.reportAddress\",\r\n            \"otherRequirea\":null,\r\n            \"reportHeaderEn\":\"$.trfList[0].serviceRequirement.report.reportHeader\",\r\n            \"reportAddressEn\":\"$.trfList[0].serviceRequirement.report.reportAddress\"\r\n        },\r\n        \"trfAttachments\":[\r\n            {\r\n                \"attachmentId\":\"$.trfList[0].attachmentList[*].cloudId\",\r\n                \"fileName\":\"$.trfList[0].attachmentList[*].fileName\",\r\n                \"size\":null\r\n            }\r\n        ],\r\n        \"carelabelInstances\":[\r\n            {\r\n                \"careInstruction\":\"$.careLabelList[*].careInstruction\",\r\n                \"radioType\":\"$.careLabelList[*].radioType\",\r\n                \"selectCountry\":\"$.careLabelList[*].selectCountry\",\r\n                \"selectImgIds\":null,\r\n                \"productItemNos\":null,\r\n                \"careLabelSeq\":\"$.careLabelList[*].careLabelSeq\",\r\n                \"careLabelFileId\":null,\r\n                \"productItemNo\":null,\r\n                \"imgArray\":[\r\n                    {\r\n                        \"imageDetailId\":null,\r\n                        \"sequenceNo\":null,\r\n                        \"picture\":null,\r\n                        \"checked\":null\r\n                    }\r\n                ]\r\n            }\r\n        ],\r\n        \"compositionIngredients\":[\r\n            {\r\n                \"chemicalEnName\":null,\r\n                \"chemicalZhName\":null,\r\n                \"casNo\":null,\r\n                \"composition\":null,\r\n                \"sortedIndex\":null\r\n            }\r\n        ],\r\n        \"testPackageList\":[\r\n            {\r\n                \"testPackageId\":null,\r\n                \"parentId\":null,\r\n                \"testPackageName\":null,\r\n                \"isSelect\":null,\r\n                \"remark\":null,\r\n                \"sort\":null\r\n            }\r\n        ]\r\n    }\r\n}', `response_body_template` = '{}', `remark` = 'SgsMart OrderToTrf调用', `created_by` = 'XQW', `created_date` = '2023-12-13 19:30:08', `modified_by` = 'XQW', `modified_date` = '2023-12-13 19:30:13', `last_modified_timestamp` = '2024-01-04 16:04:16' WHERE `id` = 28;

INSERT INTO `tb_cfg_event_subscribe`(`id`, `subscriber`, `ref_system_id`, `event_code`, `api_id`, `priority`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`)
VALUES (52, 10002, 10018, 1, 28, 0, 0, 2023, 'messageNotifyHandler', '', 'XQW', CURRENT_TIMESTAMP, 'XQW', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
