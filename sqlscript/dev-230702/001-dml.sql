INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (12, 12, 10, 1, 24, 2023, 'YANG', '2023-05-11 19:20:49', 'YANG', '2023-05-11 19:21:00', '2023-06-28 16:42:05');

INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (13, 12, 30, 1, 24, 2023, 'YANG', '2023-05-11 19:20:49', 'YANG', '2023-05-11 19:21:00', '2023-06-28 16:42:07');

INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (14, 12, 60, 6, 24, 2023, 'YANG', '2023-05-11 19:20:49', 'YANG', '2023-05-11 19:21:00', '2023-06-28 16:42:10');

INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (15, 12, 82, 1, 24, 2023, 'YANG', '2023-05-11 19:20:49', 'YANG', '2023-05-11 19:21:00', '2023-06-28 16:42:13');

INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (16, 2, 1000010, 7, 0, 0, 'YANG', '2023-06-15 21:04:08', 'YANG', '2023-06-15 21:04:14', '2023-06-16 11:17:24');

INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (17, 2, 1000030, 7, 0, 0, 'YANG', '2023-06-15 21:04:08', 'YANG', '2023-06-15 21:04:14', '2023-06-16 11:17:27');

INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (18, 2, 1000060, 7, 0, 0, 'YANG', '2023-06-15 21:04:08', 'YANG', '2023-06-15 21:04:14', '2023-06-16 11:17:29');

INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (19, 2, 1000082, 7, 0, 0, 'YANG', '2023-06-15 21:04:08', 'YANG', '2023-06-15 21:04:14', '2023-06-16 11:17:31');

INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (20, 2, 1000020, 7, 0, 0, 'YANG', '2023-06-15 21:04:08', 'YANG', '2023-06-15 21:04:14', '2023-06-16 11:17:33');

INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (21, 2, 1000040, 7, 0, 0, 'YANG', '2023-06-15 21:04:08', 'YANG', '2023-06-15 21:04:14', '2023-06-16 11:17:35');

INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (22, 2, 1000050, 7, 0, 0, 'YANG', '2023-06-15 21:04:08', 'YANG', '2023-06-15 21:04:14', '2023-06-16 11:17:37');

INSERT INTO `tb_cfg_event_subscribe` (`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (23, 2, 1000070, 7, 0, 0, 'YANG', '2023-06-15 21:04:08', 'YANG', '2023-06-15 21:04:14', '2023-06-16 11:17:40');

INSERT INTO `tb_cfg_info` (`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (1, 'CG0001085', '', '7', 'SL', 2, 'TrfStatusControl', '{\r\n    \"trfOrderRelationshipRule\": 2,\r\n    \"statusRule\": 4,\r\n    \"statusFlow\": [\r\n        1,\r\n        4,\r\n        5,\r\n        6,\r\n        7\r\n    ],\r\n    \"statusMapping\": {\r\n        \"1\": 1,\r\n        \"4\": 4,\r\n        \"5\": 5,\r\n        \"6\": 6,\r\n        \"7\": 7,\r\n        \"9\": 9\r\n    },\r\n    \"actionStatusMapping\": {\r\n        \"Import\": {\r\n            \"orderRefRule\": 0,\r\n            \"orderOpType\": 1,\r\n            \"ctrlMapping\": {}\r\n        },\r\n        \"SyncConfirmed\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 1,\r\n            \"ctrlMapping\": {\r\n                \"4\":0\r\n            }\r\n        },\r\n        \"SyncTesting\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 3,\r\n            \"ctrlMapping\": {\r\n                \"5\": 0\r\n            }\r\n        },\r\n        \"SyncCompleted\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 3,\r\n            \"ctrlMapping\": {\r\n                \"6\": 0\r\n            }\r\n        },\r\n        \"SyncClosed\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 3,\r\n            \"ctrlMapping\": {\r\n                \"7\": 0\r\n            }\r\n        },\r\n        \"SyncUnBind\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 3,\r\n            \"ctrlMapping\": {\r\n                \"1\": 0,\r\n                \"2\": 0,\r\n                \"3\": 0,\r\n                \"4\": 0,\r\n                \"5\": 0\r\n            }\r\n        }\r\n    }\r\n}', 'system', '2023-05-11 15:12:36', 'system', '2023-05-15 08:20:58', '2023-06-30 19:41:05');

INSERT INTO `tb_cfg_info` (`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (2, 'CG0001085', '', '7', 'SL', 2, 'RdSource', 'SCI', 'system', '2023-05-11 15:12:36', 'system', '2023-05-15 08:20:58', '2023-05-15 08:39:29');

INSERT INTO `tb_cfg_info` (`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (3, NULL, '', '2', 'SL', 2, 'TrfStatusControl', '{\r\n    \"trfOrderRelationshipRule\": 2,\r\n    \"statusRule\": 4,\r\n    \"statusFlow\": [\r\n        1,\r\n        3,\r\n        4,\r\n        5,\r\n        6,\r\n        7\r\n    ],\r\n    \"statusMapping\": {\r\n        \"1\": 1,\r\n        \"3\": 3,\r\n        \"4\": 4,\r\n        \"5\": 5,\r\n        \"6\": 6,\r\n        \"7\": 7\r\n    },\r\n    \"actionStatusMapping\": {\r\n        \"Import\": {\r\n            \"orderRefRule\": 0,\r\n            \"orderOpType\": 1,\r\n            \"ctrlMapping\": {}\r\n        },\r\n        \"SyncToOrder\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 1,\r\n            \"ctrlMapping\": {\r\n                \"3\": 0\r\n            }\r\n        },\r\n        \"SyncConfirmed\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"4\": 0\r\n            }\r\n        },\r\n        \"SyncTesting\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"5\": 0\r\n            }\r\n        },\r\n        \"SyncCompleted\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"6\": 0\r\n            }\r\n        },\r\n        \"SyncClosed\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"7\": 0\r\n            }\r\n        },\r\n        \"SyncPending\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"3\": 3,\r\n                \"4\": 4,\r\n                \"5\": 5\r\n            }\r\n        },\r\n        \"SyncUnPending\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"3\": 3,\r\n                \"4\": 4,\r\n                \"5\": 5\r\n            }\r\n        }\r\n    }\r\n}', 'system', '2023-05-11 15:12:36', 'system', '2023-05-15 08:20:58', '2023-06-30 19:42:42');

INSERT INTO `tb_cfg_info` (`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (4, NULL, '', '5', 'SL', 2, 'TrfStatusControl', '4', 'system', '2023-05-11 15:12:36', 'system', '2023-05-15 08:20:58', '2023-06-12 15:56:37');

INSERT INTO `tb_cfg_info` (`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (5, NULL, '', '0', '', 2, 'TrfStatusControl', '{\r\n    \"actionStatusMapping\": {\r\n        \"CustomerCancelTrf\": {\r\n            \"ctrlMapping\": {\r\n                \"1\": 0,\r\n                \"2\": 0,\r\n                \"3\": 0,\r\n                \"4\": 0\r\n            }\r\n        },\r\n        \"PreOrderCancelTrf\": {\r\n            \"ctrlMapping\": {\r\n                \"1\": 0,\r\n                \"2\": 0,\r\n                \"3\": 0,\r\n                \"4\": 0\r\n            }\r\n        }\r\n    }\r\n}', 'system', '2023-05-11 15:12:36', 'system', '2023-05-15 08:20:58', '2023-06-12 20:25:11');

INSERT INTO `tb_cfg_info` (`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (6, '', '', '12', 'SL', 2, 'TrfStatusControl', '{\r\n    \"trfOrderRelationshipRule\": 2,\r\n    \"statusRule\": 0,\r\n    \"statusFlow\": [\r\n        1,\r\n				3,\r\n        4,\r\n        5,\r\n				6,\r\n        7\r\n    ],\r\n    \"statusMapping\": {\r\n        \"1\": 1,\r\n        \"3\": 3,\r\n        \"4\": 4,\r\n        \"5\": 5,\r\n				\"6\": 6,\r\n        \"7\": 7,\r\n        \"9\": 9\r\n    },\r\n    \"actionStatusMapping\": {\r\n        \"Import\": {\r\n            \"orderRefRule\": 0,\r\n            \"orderOpType\": 1,\r\n            \"ctrlMapping\": {}\r\n        },\r\n        \"SyncToOrder\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 1,\r\n            \"ctrlMapping\": {\r\n                \"3\":0\r\n            }\r\n        },\r\n        \"SyncConfirmed\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"4\":0\r\n            }\r\n        },\r\n        \"SyncTesting\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"5\": 0\r\n            }\r\n        },\r\n        \"SyncCompleted\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"6\": 0\r\n            }\r\n        },\r\n        \"SyncClosed\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"7\": 8\r\n            }\r\n        },\r\n				,\r\n        \"SyncCancel\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"1\": 1,\r\n								\"2\": 9\r\n            }\r\n        },\r\n        \"SyncUnBind\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"1\": 0,\r\n                \"2\": 0,\r\n                \"3\": 0,\r\n                \"4\": 0,\r\n                \"5\": 0\r\n            }\r\n        },\r\n        \"CustomerCancelTrf\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"1\": 0\r\n            }\r\n        },\r\n        \"PreOrderCancelTrf\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"1\": 0\r\n            }\r\n        },\r\n        \"PreOrderReturnTrf\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 3,\r\n            \"ctrlMapping\": {\r\n                \"1\": 0\r\n            }\r\n        },\r\n        \"CustomerReturnTrf\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 3,\r\n            \"ctrlMapping\": {\r\n                \"1\": 0\r\n            }\r\n        }\r\n    }\r\n}', 'system', '2023-05-11 15:12:36', 'system', '2023-05-15 08:20:58', '2023-06-30 19:43:02');

INSERT INTO `tb_cfg_info` (`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (7, NULL, '', '2', 'HL', 2, 'TrfStatusControl', '{\r\n    \"trfOrderRelationshipRule\": 2,\r\n    \"statusRule\": 4,\r\n    \"statusFlow\": [\r\n        1,\r\n        3,\r\n        4,\r\n        5,\r\n        6,\r\n        7\r\n    ],\r\n    \"statusMapping\": {\r\n        \"1\": 1,\r\n        \"3\": 3,\r\n        \"4\": 4,\r\n        \"5\": 5,\r\n        \"6\": 6,\r\n        \"7\": 7\r\n    },\r\n    \"actionStatusMapping\": {\r\n        \"Import\": {\r\n            \"orderRefRule\": 0,\r\n            \"orderOpType\": 1,\r\n            \"ctrlMapping\": {}\r\n        },\r\n        \"SyncToOrder\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 1,\r\n            \"ctrlMapping\": {\r\n                \"3\": 0\r\n            }\r\n        },\r\n        \"SyncConfirmed\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"4\": 0\r\n            }\r\n        },\r\n        \"SyncTesting\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"5\": 0\r\n            }\r\n        },\r\n        \"SyncCompleted\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"6\": 0\r\n            }\r\n        },\r\n        \"SyncClosed\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"7\": 0\r\n            }\r\n        },\r\n        \"SyncPending\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"3\": 3,\r\n                \"4\": 4,\r\n                \"5\": 5\r\n            }\r\n        },\r\n        \"SyncUnPending\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"3\": 3,\r\n                \"4\": 4,\r\n                \"5\": 5\r\n            }\r\n        }\r\n    }\r\n}', 'system', '2023-05-11 15:12:36', 'system', '2023-05-15 08:20:58', '2023-06-30 19:43:11');

INSERT INTO `tb_cfg_system_api` (`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (6, 12, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{\"orderNo\":\"$.trfList[0].trfNo\",\"orderState\":\"#actionMapping($extra.eventName,\'SyncToOrder\':64&\'SyncConfirmed\':14&\'SyncCompleted\':80&\'PreOrderCancelTrf\':91)\",\"orderAmount\":null,\"memo\":\"#getCancelTypeDesc($.trfList[0].others.cancel.cancelType)\",\"operatorDate\":\"#toDate($.extra.syncInfo.reportList[0].softCopyDeliveryDate)\",\"csCode\":\"$.trfList[0].lab.labContact.contactName\",\"slimNo\":\"#listToStr($.orderList.orderNo)\",\"reports\":[{\"cloudId\":\"$.reportList.reportFileList[0].cloudId[0]\",\"fileId\":\"$.reportList.reportFileList[0].cloudId[0]\",\"fileName\":\"$.reportList.reportFileList[0].fileName[0]\",\"reportNo\":\"$.reportList[*].reportNo\",\"reportDate\":\"#toDate($.reportList[*].softCopyDeliveryDate)\"}]}', '{}', 'YANG', '2023-05-11 19:22:59', 'YANG', '2023-05-11 19:23:07', '2023-06-30 17:27:50');

INSERT INTO `tb_cfg_system_api` (`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (7, 2, 1, 'http://cnapp.sgs.net/extsystemapi/api/notifyTrf', 2, '{\r\n    \"action\": \"syncCallback\",\r\n    \"version\": \"$.version\",\r\n    \"data\": {\r\n        \"clientRequestId\": \"$.clientRequestId\",\r\n        \"status\": \"$.status\"\r\n    }\r\n}', 'OK', 'YANG', '2023-06-15 20:55:19', 'YANG', '2023-06-15 20:55:20', '2023-06-27 20:26:48');

UPDATE `tb_cfg_system_api` SET `system_id` = 12, `protocol_type` = 2, `request_url` = 'com.sgs.extsystem.sync.order.forILayer', `request_method` = NULL, `request_body_template` = '{\"orderNo\":\"$.trfList[0].trfNo\",\"orderState\":\"#actionMapping($extra.eventName,\'SyncToOrder\':64&\'SyncConfirmed\':14&\'SyncCompleted\':80&\'PreOrderCancelTrf\':91)\",\"orderAmount\":null,\"memo\":\"#getCancelTypeDesc($.trfList[0].others.cancel.cancelType)\",\"operatorDate\":\"#toDate($.extra.syncInfo.reportList[0].reportDueDate)\",\"csCode\":\"$.trfList[0].lab.labContact.contactName\",\"slimNo\":\"$.extra.syncInfo.order.orderNo\",\"reports\":[{\"cloudId\":\"$.reportList.reportFileList[0].cloudId[0]\",\"fileId\":\"$.reportList[*].reportFileList[0].cloudId[0]\",\"fileName\":\"$.reportList[*].reportFileList[0].fileName[0]\",\"reportNo\":\"$.reportList[*].reportNo\",\"reportDate\":\"#toDate($.reportList[*].softCopyDeliveryDate)\"}]}', `response_body_template` = '{}', `created_by` = 'YANG', `created_date` = '2023-05-11 19:22:59', `modified_by` = 'YANG', `modified_date` = '2023-05-11 19:23:07', `last_modified_timestamp` = '2023-06-30 17:28:03' WHERE `id` = 1;

DELETE FROM `tb_dfv_template_field` WHERE `id` = 62;

DELETE FROM `tb_dfv_template_field` WHERE `id` = 66;

DELETE FROM `tb_dfv_template_field` WHERE `id` = 67;

DELETE FROM `tb_dfv_template_field` WHERE `id` = 68;

DELETE FROM `tb_dfv_template_field` WHERE `id` = 140;

DELETE FROM `tb_dfv_template_field` WHERE `id` = 141;

DELETE FROM `tb_dfv_template_field` WHERE `id` = 142;

INSERT INTO `tb_dfv_template_field` (`id`, `template_id`, `field_id`, `field_path`, `sort_num`, `parent_id`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (810, 5, 570, 'reportList.reportFileList.filePath', 1, 857, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-06-29 14:10:10');

INSERT INTO `tb_dfv_template_field` (`id`, `template_id`, `field_id`, `field_path`, `sort_num`, `parent_id`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (840, 5, 39, 'order.sampleList', 2, 156, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-06-29 19:01:13');

INSERT INTO `tb_dfv_template_field` (`id`, `template_id`, `field_id`, `field_path`, `sort_num`, `parent_id`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (841, 5, 131, 'order.sampleList.sampleInstanceId', 1, 840, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-06-29 19:02:50');

INSERT INTO `tb_dfv_template_field` (`id`, `template_id`, `field_id`, `field_path`, `sort_num`, `parent_id`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (852, 5, 7, 'reportList', 0, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-06-29 14:10:18');

INSERT INTO `tb_dfv_template_field` (`id`, `template_id`, `field_id`, `field_path`, `sort_num`, `parent_id`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (853, 5, 468, 'reportList.reportId', 1, 852, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-06-29 18:39:58');

INSERT INTO `tb_dfv_template_field` (`id`, `template_id`, `field_id`, `field_path`, `sort_num`, `parent_id`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (856, 5, 469, 'reportList.reportMatrixList', 4, 852, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-06-29 19:06:23');

INSERT INTO `tb_dfv_template_field` (`id`, `template_id`, `field_id`, `field_path`, `sort_num`, `parent_id`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (857, 5, 467, 'reportList.reportFileList', 1, 852, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-06-29 14:10:21');

INSERT INTO `tb_dfv_template_field` (`id`, `template_id`, `field_id`, `field_path`, `sort_num`, `parent_id`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (858, 5, 501, 'reportList.reportMatrixList.testMatrixId', 1, 856, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-06-29 19:06:32');

INSERT INTO `tb_dfv_template_field` (`id`, `template_id`, `field_id`, `field_path`, `sort_num`, `parent_id`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (859, 5, 502, 'reportList.reportMatrixList.testSampleInstanceId', 2, 856, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-06-29 19:06:34');

INSERT INTO `tb_dfv_template_field` (`id`, `template_id`, `field_id`, `field_path`, `sort_num`, `parent_id`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (860, 5, 500, 'reportList.reportMatrixList.testLineInstanceId', 3, 856, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-06-29 19:06:35');

INSERT INTO `tb_dfv_template_field` (`id`, `template_id`, `field_id`, `field_path`, `sort_num`, `parent_id`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (873, 5, 10, 'testSampleList', 5, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');

INSERT INTO `tb_dfv_template_field` (`id`, `template_id`, `field_id`, `field_path`, `sort_num`, `parent_id`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (874, 5, 205, 'testSampleList.testSampleInstanceId', 1, 873, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-06-29 19:18:23');

INSERT INTO `tb_dfv_template_field` (`id`, `template_id`, `field_id`, `field_path`, `sort_num`, `parent_id`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (877, 5, 208, 'testSampleList.testSampleSeq', 4, 873, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-06-29 19:18:27');

INSERT INTO `tb_dfv_template_field` (`id`, `template_id`, `field_id`, `field_path`, `sort_num`, `parent_id`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (900, 5, 599, 'reportList.reportMatrixList.testMatrixFileList', 3, 856, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-06-29 19:14:16');

INSERT INTO `tb_dfv_template_field` (`id`, `template_id`, `field_id`, `field_path`, `sort_num`, `parent_id`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (901, 5, 614, 'reportList.reportMatrixList.testMatrixFileList.filePath', 3, 900, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-06-29 19:14:23');

INSERT INTO `tb_dfv_template_field` (`id`, `template_id`, `field_id`, `field_path`, `sort_num`, `parent_id`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (1125, 5, 3, 'invoiceList', 5, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-23 16:50:06');

INSERT INTO `tb_dfv_template_field` (`id`, `template_id`, `field_id`, `field_path`, `sort_num`, `parent_id`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (1126, 5, 442, 'invoiceList.invoiceFileList', 5, 1125, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-06-29 19:24:32');

INSERT INTO `tb_dfv_template_field` (`id`, `template_id`, `field_id`, `field_path`, `sort_num`, `parent_id`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (1127, 5, 451, 'invoiceList.invoiceFileList.filePath', 5, 1126, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-06-29 19:24:38');

UPDATE `tb_dfv_template_field` SET `template_id` = 5, `field_id` = 12, `field_path` = 'header.trfList', `sort_num` = 0, `parent_id` = 152, `active_indicator` = 1, `created_by` = 'system', `created_date` = '2023-05-16 17:44:12', `modified_by` = 'system', `modified_date` = '2023-05-16 17:44:12', `last_modified_timestamp` = '2023-06-29 10:40:49' WHERE `id` = 153;

DELETE FROM `tb_dfv_template_field_constraint` WHERE `id` = 71;

DELETE FROM `tb_dfv_template_field_constraint` WHERE `id` = 72;

DELETE FROM `tb_dfv_template_field_constraint` WHERE `id` = 73;

DELETE FROM `tb_dfv_template_field_constraint` WHERE `id` = 74;

DELETE FROM `tb_dfv_template_field_constraint` WHERE `id` = 75;

DELETE FROM `tb_dfv_template_field_constraint` WHERE `id` = 84;

DELETE FROM `tb_dfv_template_field_constraint` WHERE `id` = 85;

DELETE FROM `tb_dfv_template_field_constraint` WHERE `id` = 86;

DELETE FROM `tb_dfv_template_field_constraint` WHERE `id` = 162;

DELETE FROM `tb_dfv_template_field_constraint` WHERE `id` = 163;

DELETE FROM `tb_dfv_template_field_constraint` WHERE `id` = 164;

DELETE FROM `tb_dfv_template_field_constraint` WHERE `id` = 165;

INSERT INTO `tb_dfv_template_field_constraint` (`id`, `template_id`, `template_field_id`, `field_id`, `field_path`, `type`, `constraint_value1`, `constraint_value2`, `field_id_ref`, `active_indicator`, `sort_num`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (846, 5, 840, 39, 'order.sampleList', 'Only', 'sampleInstanceId', NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-06-29 19:46:40');

INSERT INTO `tb_dfv_template_field_constraint` (`id`, `template_id`, `template_field_id`, `field_id`, `field_path`, `type`, `constraint_value1`, `constraint_value2`, `field_id_ref`, `active_indicator`, `sort_num`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (859, 5, 852, 7, 'reportList', 'Only', 'reportId', NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-06-29 18:51:30');

INSERT INTO `tb_dfv_template_field_constraint` (`id`, `template_id`, `template_field_id`, `field_id`, `field_path`, `type`, `constraint_value1`, `constraint_value2`, `field_id_ref`, `active_indicator`, `sort_num`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (863, 5, 856, 469, 'reportList.reportMatrixList', 'Only', 'testMatrixId', NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-06-29 19:49:08');

INSERT INTO `tb_dfv_template_field_constraint` (`id`, `template_id`, `template_field_id`, `field_id`, `field_path`, `type`, `constraint_value1`, `constraint_value2`, `field_id_ref`, `active_indicator`, `sort_num`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (865, 5, 859, 502, 'reportList.reportMatrixList.testSampleInstanceId', 'RefObject', 'testSampleList.testSampleInstanceId', 'testSampleList', 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-06-29 19:59:00');

INSERT INTO `tb_dfv_template_field_constraint` (`id`, `template_id`, `template_field_id`, `field_id`, `field_path`, `type`, `constraint_value1`, `constraint_value2`, `field_id_ref`, `active_indicator`, `sort_num`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (866, 5, 860, 500, 'reportList.reportMatrixList.testLineInstanceId', 'RefObject', 'testLineList.testLineInstanceId', 'testLineList', 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-06-30 10:09:30');

INSERT INTO `tb_dfv_template_field_constraint` (`id`, `template_id`, `template_field_id`, `field_id`, `field_path`, `type`, `constraint_value1`, `constraint_value2`, `field_id_ref`, `active_indicator`, `sort_num`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (876, 5, 857, 467, 'reportList.reportFileList', 'Other', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-06-30 16:05:11');

INSERT INTO `tb_dfv_template_field_constraint` (`id`, `template_id`, `template_field_id`, `field_id`, `field_path`, `type`, `constraint_value1`, `constraint_value2`, `field_id_ref`, `active_indicator`, `sort_num`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (880, 5, 810, 570, 'reportList.reportFileList.filePath', 'Pattern', '^(http|https)://.*$', NULL, 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-06-30 16:12:16');

INSERT INTO `tb_dfv_template_field_constraint` (`id`, `template_id`, `template_field_id`, `field_id`, `field_path`, `type`, `constraint_value1`, `constraint_value2`, `field_id_ref`, `active_indicator`, `sort_num`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (882, 5, 873, 10, 'testSampleList', 'ListSize', 'testSampleType=104', 'testSampleGroupList=2', 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-06-30 11:23:12');

INSERT INTO `tb_dfv_template_field_constraint` (`id`, `template_id`, `template_field_id`, `field_id`, `field_path`, `type`, `constraint_value1`, `constraint_value2`, `field_id_ref`, `active_indicator`, `sort_num`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (888, 5, 877, 208, 'testSampleList.testSampleSeq', 'Pattern', '^[0-9]*[1-9][0-9]*$', NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-06-30 11:31:06');

INSERT INTO `tb_dfv_template_field_constraint` (`id`, `template_id`, `template_field_id`, `field_id`, `field_path`, `type`, `constraint_value1`, `constraint_value2`, `field_id_ref`, `active_indicator`, `sort_num`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (1155, 5, 1125, 3, 'invoiceList', 'Other', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-06-30 17:37:14');

INSERT INTO `tb_dfv_template_field_constraint` (`id`, `template_id`, `template_field_id`, `field_id`, `field_path`, `type`, `constraint_value1`, `constraint_value2`, `field_id_ref`, `active_indicator`, `sort_num`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (1156, 5, 1126, 442, 'invoiceList.invoiceFileList', 'Other', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-06-30 10:37:56');

INSERT INTO `tb_dfv_template_field_constraint` (`id`, `template_id`, `template_field_id`, `field_id`, `field_path`, `type`, `constraint_value1`, `constraint_value2`, `field_id_ref`, `active_indicator`, `sort_num`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (1157, 5, 1127, 451, 'invoiceList.invoiceFileList.filePath', 'Pattern', '^(http|https)://.*$', NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-06-30 14:37:50');

INSERT INTO `tb_dfv_template_field_constraint` (`id`, `template_id`, `template_field_id`, `field_id`, `field_path`, `type`, `constraint_value1`, `constraint_value2`, `field_id_ref`, `active_indicator`, `sort_num`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (1865, 5, 873, 10, 'testSampleList', 'Only', 'testSampleInstanceId', '', 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-06-30 10:58:06');

INSERT INTO `tb_dfv_template_field_constraint` (`id`, `template_id`, `template_field_id`, `field_id`, `field_path`, `type`, `constraint_value1`, `constraint_value2`, `field_id_ref`, `active_indicator`, `sort_num`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (1900, 5, 900, 599, 'reportList.reportMatrixList.testMatrixFileList', 'Other', '', NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-06-30 10:14:28');

INSERT INTO `tb_dfv_template_field_constraint` (`id`, `template_id`, `template_field_id`, `field_id`, `field_path`, `type`, `constraint_value1`, `constraint_value2`, `field_id_ref`, `active_indicator`, `sort_num`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (1901, 5, 901, 614, 'reportList.reportMatrixList.testMatrixFileList.filePath', 'Pattern', '^(http|https)://.*$', NULL, 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-06-30 15:52:04');

UPDATE `tb_dfv_template_field_constraint` SET `template_id` = 3, `template_field_id` = 63, `field_id` = 545, `field_path` = 'reportList.reportMatrixList.conclusion.conclusionCode', `type` = 'NotEmpty', `constraint_value1` = NULL, `constraint_value2` = NULL, `field_id_ref` = 0, `active_indicator` = 1, `sort_num` = 0, `created_by` = 'system', `created_date` = '2023-05-16 20:02:37', `modified_by` = 'system', `modified_date` = '2023-05-16 20:02:40', `last_modified_timestamp` = '2023-06-25 15:19:37' WHERE `id` = 68;

UPDATE `tb_dfv_template_field_constraint` SET `template_id` = 3, `template_field_id` = 72, `field_id` = 570, `field_path` = 'reportList.reportFileList.filePath', `type` = 'Size', `constraint_value1` = '1', `constraint_value2` = '500', `field_id_ref` = 0, `active_indicator` = 1, `sort_num` = 1, `created_by` = 'system', `created_date` = '2023-05-16 20:02:37', `modified_by` = 'system', `modified_date` = '2023-05-16 20:02:40', `last_modified_timestamp` = '2023-05-30 17:44:53' WHERE `id` = 81;

INSERT INTO `tb_dict_value` (`id`, `system_id`, `dict_type`, `dict_value`, `dict_label`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (17, 40, 'SystemId', '2', 'SODA', 1, 'system', '2023-05-04 15:35:32', 'system', '2023-05-04 15:35:48', '2023-06-12 17:07:08');

INSERT INTO `tb_dict_value` (`id`, `system_id`, `dict_type`, `dict_value`, `dict_label`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (18, 40, 'SCI-SyncAction', 'SyncReporting', 'Reporting', 1, 'system', '2023-06-19 21:09:40', 'system', '2023-06-19 21:09:46', '2023-06-19 21:09:48');

UPDATE `tb_dict_value` SET `system_id` = 40, `dict_type` = 'RefSystemId', `dict_value` = '12', `dict_label` = 'TIC系统', `active_indicator` = 1, `created_by` = 'system', `created_date` = '2023-05-04 15:35:32', `modified_by` = 'system', `modified_date` = '2023-05-04 15:35:48', `last_modified_timestamp` = '2023-05-25 18:58:10' WHERE `id` = 3;
