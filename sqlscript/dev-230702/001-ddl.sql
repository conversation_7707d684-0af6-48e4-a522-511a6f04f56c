
ALTER TABLE `tb_cfg_info` MODIFY COLUMN `config_value` varchar(5000) NOT NULL COMMENT '配置值' AFTER `config_key`;

ALTER TABLE `tb_cfg_system_api` MODIFY COLUMN `request_body_template` text NOT NULL COMMENT '请求数据的模板' AFTER `request_method`;

ALTER TABLE `tb_cfg_system_api` MODIFY COLUMN `response_body_template` varchar(5000) NOT NULL COMMENT '返回数据的模板' AFTER `request_body_template`;

ALTER TABLE `tb_cfg_system_api` ADD COLUMN `remark` varchar(150) NULL DEFAULT NULL COMMENT 'api备注' AFTER `response_body_template`;

ALTER TABLE `tb_customer_trf_info` MODIFY COLUMN `ProductLineCode` varchar(10) NULL DEFAULT NULL COMMENT 'productLineCode BU' AFTER `Id`;

ALTER TABLE `tb_customer_trf_info` MODIFY COLUMN `LabCode` varchar(10) NULL DEFAULT NULL COMMENT 'import 操作时，所在的Lab' AFTER `RefSystemId`;

ALTER TABLE `tb_customer_trf_info` MODIFY COLUMN `TrfNo` varchar(64) NOT NULL COMMENT '客户系统进单单号' AFTER `LabCode`;

ALTER TABLE `tb_customer_trf_info` MODIFY COLUMN `ObjectNo` varchar(64) NULL DEFAULT NULL COMMENT '客户导入的 packageBarcode' AFTER `TrfNo`;

ALTER TABLE `tb_customer_trf_info` MODIFY COLUMN `BatchNo` varchar(50) NULL DEFAULT NULL COMMENT '批次号' AFTER `ObjectNo`;

ALTER TABLE `tb_customer_trf_info` MODIFY COLUMN `CancelReason` varchar(250) NULL DEFAULT NULL COMMENT '取消原因' AFTER `TrfDate`;

ALTER TABLE `tb_customer_trf_info` MODIFY COLUMN `CreatedBy` varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `ActiveIndicator`;

ALTER TABLE `tb_customer_trf_info` MODIFY COLUMN `ModifiedBy` varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `CreatedDate`;

ALTER TABLE `tb_task_info` DROP INDEX `idx_extId`;

ALTER TABLE `tb_task_info` ADD COLUMN `identity_id` int(11) NOT NULL COMMENT 'identity id' AFTER `id`;

ALTER TABLE `tb_task_info` MODIFY COLUMN `group_key` varchar(32) NULL DEFAULT NULL COMMENT '同一个extId下的分组key,可重复\r\n10：TRF_TO_ORDER_EVENT \r\n20：TRF_TO_QUOTATION_EVENT \r\n30：TRF_CONFIRMED_EVENT \r\n40：TRF_TESTING_EVENT \r\n50：TRF_REPORTING_EVENT \r\n60：TRF_COMPLETED_EVENT \r\n70：TRF_CLOSED_EVENT \r\n80：TRF_ORDER_CANCEL_EVENT \r\n81：TRF_CUSTOMER_CANCEL_EVENT \r\n82：TRF_PREORDER_CANCEL_EVENT \r\n90：TRF_UNBIND_EVENT \r\n100：TRF_PENDING_EVENT \r\n110：TRF_UNPENDING_EVENT ' AFTER `ext_id`;

ALTER TABLE `tb_task_info` ADD COLUMN `depend_condition` varchar(500) NULL DEFAULT NULL COMMENT '前置条件,见TaskCondition类' AFTER `depend_task`;

ALTER TABLE `tb_task_info` ADD INDEX `idx_identityId_extId`(`identity_id`, `ext_id`) USING BTREE;


ALTER TABLE `tb_trf` MODIFY COLUMN `ref_system_id` int(11) NULL DEFAULT NULL AFTER `channel`;

ALTER TABLE `tb_trf` MODIFY COLUMN `system_id` int(11) NULL DEFAULT NULL COMMENT '调用系统id' AFTER `ref_system_id`;

ALTER TABLE `tb_trf` MODIFY COLUMN `pending_flag` tinyint(4) NULL DEFAULT 0 COMMENT '0: unpending, 1: pending' AFTER `trf_remark`;

ALTER TABLE `tb_trf` MODIFY COLUMN `pending_type` int(11) NULL DEFAULT NULL COMMENT 'pending类型' AFTER `pending_flag`;

ALTER TABLE `tb_trf` ADD COLUMN `cancel_type` int(11) NULL DEFAULT NULL COMMENT '取消类型' AFTER `pending_remark`;

ALTER TABLE `tb_trf` ADD COLUMN `cancel_remark` varchar(1000) NULL DEFAULT NULL COMMENT '取消原因' AFTER `cancel_type`;

ALTER TABLE `tb_trf` ADD COLUMN `remove_type` int(11) NULL DEFAULT NULL COMMENT '删除类型' AFTER `cancel_remark`;

ALTER TABLE `tb_trf` ADD COLUMN `remove_remark` varchar(1000) NULL DEFAULT NULL COMMENT '删除原因' AFTER `remove_type`;

ALTER TABLE `tb_trf` MODIFY COLUMN `ext_fields` text NULL COMMENT '扩展信息json结构' AFTER `remove_remark`;

ALTER TABLE `tb_trf` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_attachment` MODIFY COLUMN `file_path` varchar(2000) NULL DEFAULT NULL AFTER `file_name`;

ALTER TABLE `tb_trf_attachment` MODIFY COLUMN `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `language_id`;

ALTER TABLE `tb_trf_attachment` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_attachment` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_care_label` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_care_label` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_care_label` ADD INDEX `idx_created_time`(`created_date`) USING BTREE;

ALTER TABLE `tb_trf_customer` MODIFY COLUMN `customer_group_code` varchar(50) NULL DEFAULT NULL COMMENT '客户组代码' AFTER `boss_no`;

ALTER TABLE `tb_trf_customer` MODIFY COLUMN `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `black_flag`;

ALTER TABLE `tb_trf_customer` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_customer` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_customer_contact` MODIFY COLUMN `contact_name` varchar(50) NULL DEFAULT NULL AFTER `contact_region_account`;

ALTER TABLE `tb_trf_customer_contact` MODIFY COLUMN `contact_email` varchar(200) NULL DEFAULT NULL AFTER `contact_name`;

ALTER TABLE `tb_trf_customer_contact` MODIFY COLUMN `contact_fax` varchar(100) NULL DEFAULT NULL AFTER `contact_telephone`;

ALTER TABLE `tb_trf_customer_contact` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_customer_contact` MODIFY COLUMN `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间' AFTER `created_by`;

ALTER TABLE `tb_trf_customer_contact` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_customer_contact` MODIFY COLUMN `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间' AFTER `modified_by`;

ALTER TABLE `tb_trf_customer_lang` MODIFY COLUMN `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `customer_address`;

ALTER TABLE `tb_trf_customer_lang` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_customer_lang` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_invoice` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_invoice` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_order` MODIFY COLUMN `order_id` varchar(80) NULL DEFAULT NULL COMMENT '订单id' AFTER `system_id`;

ALTER TABLE `tb_trf_order` MODIFY COLUMN `order_no` varchar(80) NULL DEFAULT NULL COMMENT '订单号' AFTER `order_id`;

ALTER TABLE `tb_trf_order` ADD COLUMN `pending_flag` tinyint(4) NULL DEFAULT 0 COMMENT '0: activate, 1: pending' AFTER `active_indicator`;

ALTER TABLE `tb_trf_order` ADD COLUMN `pending_type` int(11) NULL DEFAULT NULL AFTER `pending_flag`;

ALTER TABLE `tb_trf_order` ADD COLUMN `pending_remark` varchar(1000) NULL DEFAULT NULL COMMENT 'Pending备注' AFTER `pending_type`;

ALTER TABLE `tb_trf_order` ADD COLUMN `cancel_type` int(11) NULL DEFAULT NULL COMMENT '取消类型' AFTER `pending_remark`;

ALTER TABLE `tb_trf_order` ADD COLUMN `cancel_remark` varchar(1000) NULL DEFAULT NULL COMMENT '取消原因' AFTER `cancel_type`;

ALTER TABLE `tb_trf_order` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `cancel_remark`;

ALTER TABLE `tb_trf_order` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_product` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_product` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_product_attr` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_product_attr` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_quotation` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_quotation` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_report` MODIFY COLUMN `report_no` varchar(50) NULL DEFAULT NULL COMMENT '报告号' AFTER `report_id`;

ALTER TABLE `tb_trf_report` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_report` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_service_requirement` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_service_requirement` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_service_requirement_delivery` MODIFY COLUMN `delivery_type` varchar(50) NULL DEFAULT NULL COMMENT '寄送类型1 Report softcopy;2  Report hardcopy,3 Sample testSample;4 Sample residueSample;5 invoice invoiceDelivery' AFTER `trf_service_requirement_id`;

ALTER TABLE `tb_trf_service_requirement_delivery` MODIFY COLUMN `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `delivery_way`;

ALTER TABLE `tb_trf_service_requirement_delivery` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_service_requirement_delivery` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_service_requirement_lang` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_service_requirement_lang` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_test_item` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_test_item` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_test_item_lang` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_test_item_lang` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_test_sample` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_test_sample` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_test_sample_file` MODIFY COLUMN `file_path` varchar(2000) NULL DEFAULT NULL AFTER `file_name`;

ALTER TABLE `tb_trf_test_sample_file` MODIFY COLUMN `file_type` tinyint(11) NULL DEFAULT NULL AFTER `file_path`;

ALTER TABLE `tb_trf_test_sample_file` ADD COLUMN `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `language_id`;

ALTER TABLE `tb_trf_test_sample_file` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_test_sample_file` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_test_sample_group` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_test_sample_group` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_test_sample_material` MODIFY COLUMN `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_test_sample_material` MODIFY COLUMN `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_todo_info` MODIFY COLUMN `ProductLineCode` varchar(255) NOT NULL COMMENT 'productLineCode BU' AFTER `Id`;

ALTER TABLE `tb_trf_todo_info` MODIFY COLUMN `LabCode` varchar(10) NOT NULL COMMENT '所在的Lab' AFTER `RefSystemId`;

ALTER TABLE `tb_trf_todo_info` MODIFY COLUMN `TrfNo` varchar(64) NOT NULL COMMENT '客户系统进单单号' AFTER `LabCode`;

ALTER TABLE `tb_trf_todo_info` MODIFY COLUMN `CreatedBy` varchar(50) NULL DEFAULT NULL COMMENT '创建人' AFTER `TrfStatus`;

ALTER TABLE `tb_trf_todo_info` MODIFY COLUMN `ModifiedBy` varchar(50) NULL DEFAULT NULL COMMENT '修改人' AFTER `CreatedDate`;

ALTER TABLE `tre_customer_trf_reason_relationship` MODIFY COLUMN `Reason` varchar(255) NOT NULL COMMENT '选择的原因' AFTER `TrfInfoId`;

ALTER TABLE `tre_customer_trf_reason_relationship` MODIFY COLUMN `ReasonContent` varchar(255) NULL DEFAULT NULL COMMENT '手动输入的原因文本' AFTER `Reason`;


CREATE TABLE `tb_trf_lab`  (
                                                `id` bigint(20) NOT NULL,
                                                `lab_id` int(11) NULL DEFAULT NULL,
                                                `trf_id` bigint(20) NULL DEFAULT NULL,
                                                `lab_code` varchar(50) NULL DEFAULT NULL COMMENT '实验室编码',
                                                `bu_code` varchar(50) NULL DEFAULT NULL COMMENT 'bu编码',
                                                `contact_name` varchar(50) NULL DEFAULT NULL,
                                                `email` varchar(50) NULL DEFAULT NULL,
                                                `telephone` varchar(100) NULL DEFAULT NULL,
                                                `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
                                                `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人',
                                                `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人',
                                                `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                                `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
                                                PRIMARY KEY (`id`) USING BTREE,
                                                INDEX `idx_created_date`(`created_date`) USING BTREE,
                                                INDEX `idx_trf_id`(`trf_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '实验室' ROW_FORMAT = Dynamic;


CREATE TABLE `tb_trf_log`  (
                                                `id` bigint(20) NOT NULL COMMENT '主键',
                                                `ref_system_id` int(11) NOT NULL,
                                                `trf_no` varchar(50) NOT NULL COMMENT 'Trf 编号',
                                                `from_status` tinyint(4) NOT NULL COMMENT '变化前状态',
                                                `to_status` tinyint(4) NOT NULL COMMENT '变化后状态',
                                                `change_type` tinyint(4) NULL DEFAULT NULL COMMENT 'trf变更类型 1:TRF生命周期正常变化  2:trf被cancel  3:trf被remove 4:trf被pending ',
                                                `change_subtype` int(10) NULL DEFAULT NULL COMMENT 'trf变更具体类型，如：TRF cancel的类型',
                                                `change_remark` varchar(200) NULL DEFAULT NULL COMMENT '变更原因',
                                                `pending_flag` tinyint(4) NULL DEFAULT 0 COMMENT '0: unpending, 1: pending',
                                                `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人',
                                                `created_date` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人',
                                                `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                                `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
                                                PRIMARY KEY (`id`) USING BTREE,
                                                INDEX `idx_trfNo`(`ref_system_id`, `trf_no`) USING BTREE,
                                                INDEX `idx_created_date`(`created_date`) USING BTREE
) ENGINE = InnoDB ROW_FORMAT = Dynamic;


CREATE TABLE `tb_trf_order_log`  (
                                                      `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                                      `trf_order_id` bigint(20) NOT NULL,
                                                      `trf_id` bigint(20) NULL DEFAULT NULL,
                                                      `system_id` int(11) NULL DEFAULT NULL COMMENT '归属系统id',
                                                      `order_id` varchar(80) NULL DEFAULT NULL COMMENT '订单id',
                                                      `order_no` varchar(80) NULL DEFAULT NULL COMMENT '订单号',
                                                      `order_status` int(11) NULL DEFAULT NULL,
                                                      `bound_status` int(11) NOT NULL DEFAULT 0 COMMENT 'Trf绑定状态（1：已绑定、2：已解绑）',
                                                      `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
                                                      `pending_flag` tinyint(4) NULL DEFAULT 0 COMMENT '0: activate, 1: pending',
                                                      `pending_type` int(11) NULL DEFAULT NULL,
                                                      `pending_remark` varchar(1000) NULL DEFAULT NULL COMMENT 'Pending备注',
                                                      `cancel_type` int(11) NULL DEFAULT NULL COMMENT '取消类型',
                                                      `cancel_remark` varchar(1000) NULL DEFAULT NULL COMMENT '取消原因',
                                                      `created_by` varchar(50) NULL DEFAULT 'system' COMMENT '创建人',
                                                      `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                                      `modified_by` varchar(50) NULL DEFAULT 'system' COMMENT '修改人',
                                                      `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                                      `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
                                                      PRIMARY KEY (`id`) USING BTREE,
                                                      INDEX `idx_created_time`(`created_date`) USING BTREE,
                                                      INDEX `idx_trf_id`(`trf_id`) USING BTREE,
                                                      INDEX `idx_trf_order_id`(`trf_order_id`) USING BTREE
) ENGINE = InnoDB ROW_FORMAT = Dynamic;

CREATE TABLE `tb_trf_todo_list`  (
                                                      `ID` int(11) NOT NULL AUTO_INCREMENT,
                                                      `TrfNo` varchar(50) NULL DEFAULT NULL,
                                                      `TrfSubmissionDate` datetime(0) NULL DEFAULT NULL,
                                                      `LabCode` varchar(50) NULL DEFAULT NULL COMMENT 'lab code',
                                                      `BuyerNameEN` varchar(250) NULL DEFAULT NULL,
                                                      `ApplicantNameEN` varchar(250) NULL DEFAULT NULL,
                                                      `PayerNameEN` varchar(250) NULL DEFAULT NULL,
                                                      `LabContact` varchar(500) NULL DEFAULT NULL,
                                                      `Status` int(11) NOT NULL DEFAULT 0,
                                                      `Source` int(2) NULL DEFAULT NULL COMMENT '1 oldsgsmart,2newsgsmart,3olb',
                                                      PRIMARY KEY (`ID`) USING BTREE,
                                                      INDEX `idx_trf_todo_list_TrfNo`(`TrfNo`) USING BTREE
) ENGINE = InnoDB ROW_FORMAT = Dynamic;
