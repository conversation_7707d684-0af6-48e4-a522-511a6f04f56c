ALTER TABLE `tb_trf`
    MODIFY COLUMN `pending_type` varchar (50) NULL DEFAULT NULL AFTER `pending_flag`;
ALTER TABLE `tb_trf`
    ADD COLUMN `system_id` int(11) NULL COMMENT '调用方系统' AFTER `channel`,
ADD INDEX `idx_trfNo`(`trf_no`, `ref_system_id`) USING BTREE;

ALTER TABLE `tb_trf_care_label`
    ADD COLUMN `test_sample_ids` text NULL AFTER `select_img_ids`;

ALTER TABLE `tb_trf`
    MODIFY COLUMN `source` int (11) NULL DEFAULT NULL COMMENT '1：Online TRF、2：Order To TRF' AFTER `self_test_flag`;

ALTER TABLE `tb_trf_order_relationship`
    CHANGE COLUMN `ref_system_id` `system_id` int (11) NULL DEFAULT NULL COMMENT '归属系统id' AFTER `service_start_date`;

ALTER TABLE `tb_trf`
    ADD COLUMN `lab_id` int(0) NULL COMMENT '实验室id' AFTER `integration_level`,
MODIFY COLUMN `ext_fields` text   NULL AFTER `product_line_code`;

CREATE TABLE `tb_api_request`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `system_id`               int(11) NOT NULL COMMENT '调用方系统id',
    `lab_code`                varchar(20)           DEFAULT NULL COMMENT 'lab代码',
    `request_id`              varchar(100) NOT NULL COMMENT '请求id',
    `request_header`          json                  DEFAULT NULL COMMENT '请求头',
    `request_body`            json                  DEFAULT NULL COMMENT '请求body',
    `response_body`           json                  DEFAULT NULL COMMENT '响应body',
    `response_status`         varchar(50)           DEFAULT NULL COMMENT '响应状态码',
    `created_by`              varchar(50)           DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime     NOT NULL COMMENT '创建时间',
    `modified_by`             varchar(50)           DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime     NOT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
    PRIMARY KEY (`id`),
    KEY                       `idx_last_modified_timestamp` (`last_modified_timestamp`) USING BTREE,
    KEY                       `idx_system_lab` (`system_id`,`lab_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1;


drop table tb_trf_quotation_service_item_test_sample_relationship;

drop table tb_trf_quotation_service_item;

drop table tb_trf_invoice_service_item;

drop table tb_trf_invoice_test_line;

drop table tb_trf_invoice_quotation;

drop table tb_trf_report_test_matrix;

drop table tb_trf_conclusion;

drop table tb_trf_report_file;

drop table tb_trf_test_data;

drop table tb_trf_test_sample_group;

drop table tb_trf_test_sample;

drop table tb_trf_test_matrix;

drop table tb_trf_test_line;

drop table tb_trf_test_line_lang;


ALTER TABLE `tb_trf_quotation`
DROP
COLUMN `service_type`,
DROP
COLUMN `net_amount`,
DROP
COLUMN `total_amount`,
DROP
COLUMN `currency`,
DROP
COLUMN `vat_amount`,
DROP
COLUMN `discount`,
DROP
COLUMN `adjustment_amount`,
DROP
COLUMN `final_amount`,
DROP
COLUMN `main_currency_total_amount`,
DROP
COLUMN `free_quotation_flag`,
DROP
COLUMN `quotation_status`,
DROP
COLUMN `quotation_version_id`,
ADD COLUMN `order_id` varchar(100) NULL AFTER `quotation_no`,
ADD COLUMN `order_no` varchar(100) NULL AFTER `order_id`;


ALTER TABLE `tb_trf_invoice`
DROP
COLUMN `total_amount`,
DROP
COLUMN `net_amount`,
DROP
COLUMN `status`,
DROP
COLUMN `currency`,
DROP
COLUMN `vat_amount`,
ADD COLUMN `order_id` varchar(100) NULL AFTER `invoice_no`,
ADD COLUMN `order_no` varchar(100) NULL AFTER `order_id`;


ALTER TABLE `tb_trf_report`
DROP
COLUMN `actual_report_no`,
DROP
COLUMN `report_status`,
DROP
COLUMN `seal_code`,
DROP
COLUMN `report_due_date`,
DROP
COLUMN `approve_date`,
DROP
COLUMN `soft_copy_delivery_date`,
DROP
COLUMN `parent_report_no`,
DROP
COLUMN `report_type`,
DROP
COLUMN `report_approve_date`,
DROP
COLUMN `external_report_no`,
ADD COLUMN `order_no` varchar(100) NULL AFTER `order_id`;
