
UPDATE `tb_cfg_system_api` SET `request_body_template` = '{\r\n  \"_reports\":[\r\n    {\r\n		\"_reportNo\": \"$.reportList[*].reportNo\",\r\n      \"data\": [\r\n        {\r\n				\"_reportNo\": \"@_reportNo\",\r\n					\"_testSampleInstanceId\":\"$.reportList[reportNo=@._reportNo].reportMatrixList[*].testSampleInstanceId\",\r\n					\r\n          \"G_ID\": \"$.testSampleList[testSampleInstanceId=@._testSampleInstanceId][0].external.testSampleId\",\r\n          \"WTDH\": \"$.trfList[0].trfNo\",\r\n          \"SFSC\": \"$.testSampleList[testSampleInstanceId=@._testSampleInstanceId][0].testSampleNo\",\r\n          \"SC\": \"$.testSampleList[testSampleInstanceId=@._testSampleInstanceId][0].external.testSampleNo\",\r\n          \"PDFTIME\": \"#toDate($.reportList[reportNo=@._reportNo].softCopyDeliveryDate[0],\'yyyy-MM-dd\')\",\r\n          \"VER\": \"#yiliReportDeliveryCounter($.extra.refSystemId,$.extra.trfNo)\",\r\n          \"YPBMWD\": \"-\",\r\n          \"PDFFILES\": [\r\n            {\r\n              \"PDFNAME\": \"$.reportList[reportNo = @_reportNo].reportFileList[*].fileName\",\r\n              \"PDFSTRING\": null,\r\n              \"PDFURI\": \"$.reportList[reportNo = @_reportNo].reportFileList[*].cloudId\"\r\n            }\r\n          ],\r\n          \"PADATA\": [\r\n            {\r\n              \"G_ID\": \"$.reportList[reportNo = @_reportNo].reportMatrixList[*].testMatrixId\",\r\n              \"SC\": null,\r\n							\"_testLineInstanceId\":\"$.reportList[reportNo=@_reportNo].reportMatrixList[*].testLineInstanceId\",\r\n              \"PA\": \"$.orderList[0].testItemMappingList[testLineInstanceId =@._testLineInstanceId].external.testItemId[0]\",\r\n              \"PANAME\": \"$.orderList[0].testItemMappingList[testLineInstanceId =@._testLineInstanceId].external.testItemName[0]\",\r\n              \"UNIT\": \"$.testResultList[testMatrixId = @.G_ID].testResult.resultUnit[0]\",\r\n              \"VALUE_S\": \"$.testResultList[testMatrixId = @.G_ID].testResult.resultValue[0]\",\r\n              \"CONCLUSION\": \"$.reportList[reportNo =@_reportNo][0].reportMatrixList[testMatrixId = @.G_ID][0].conclusion.customerConclusion\",\r\n              \"METHOD\": \"$.testLineList[testLineInstanceId = @._testLineInstanceId][0].citation.languageList[0].citationFullName\",\r\n              \"METHODNAME\": \"$.testLineList[testLineInstanceId = @._testLineInstanceId][0].citation.languageList[0].citationFullName\",\r\n              \"LODORLOQ\": \"$.testResultList[testMatrixId = @.G_ID].methodLimit.limitType[0]\",\r\n              \"LIMITVALUE\": \"$.testResultList[testMatrixId = @.G_ID].methodLimit.limitValueFullName[0]\",\r\n              \"TESTTIME\": \"#toDate($.orderList[0].testingStartDate)\",\r\n              \"TESTENDTIME\": \"#toDate($.orderList[0].testingEndDate)\",\r\n              \"EQNAME\": null,\r\n              \"SYTJ\": null,\r\n              \"STATUS\": null,\r\n              \"BZYQ\": \"$.testResultList[testMatrixId = @.G_ID].reportLimit.limitValueFullName[0]\",\r\n              \"HighLimit\": null,\r\n              \"LowLimit\": null\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    }]\r\n  ,\r\n  \"_buildReportsNode\": \"#buildVirtualNode(\'_reports\',@._reports)\",\r\n  \"action\": \"SyncReport\",\r\n  \"dataStatus\": \"Completed\",\r\n  \"bu\": \"$.extra.productLineCode\",\r\n  \"objectNumber\": \"$.extra.trfNo\",\r\n  \"refSystemId\": \"$.extra.refSystemId\",\r\n  \"msgId\": \"$.extra.randomSequence\",\r\n  \"body\": {\r\n    \"reports\": \"#listJoinFn($._reports,data)\"\r\n  }\r\n}' WHERE `id` = 20;

INSERT INTO `tb_cfg_system_api`(`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`)
    VALUES (22, 10002, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{\r\n    \"refSystemId\": \"$.refSystemId\",\r\n    \"productLineCode\": \"SL\",\r\n  \"trfNo\": \"$.trfNo\",\r\n  \"action\": \"GetSGSMartTrfInfo\",\r\n  \"customerGroupCode\": \"12\"\r\n}', '{\r\n  \"_init\": \"#buildVirtualNode(\'customerList\',$.agent,$.payer,$.applicant,$.buyer)\",\r\n  \"header\": {\r\n    \"refSystemId\": 2,\r\n    \"source\":1,\r\n    \"trfNo\": \"$.headers.trfNo\",\r\n    \"serviceType\": \"$.headers.serviceLevel\",\r\n    \"sampleLevel\": null,\r\n    \"parcelNoList\": null,\r\n    \"trfSubmissionDate\": \"$.headers.trfSubmissionDate\",\r\n    \"selfTestFlag\": \"$.headers.selfTestFlag\",\r\n    \"others\": {\r\n      \"trfRemark\": \"$.headers.customerRemark\"\r\n    },\r\n    \"lab\": {\r\n      \"labId\": \"$.lab.labId\",\r\n      \"labCode\": \"$.lab.labCode\",\r\n      \"buCode\": \"$.headers.buCode\",\r\n      \"labContract\": {\r\n        \"contractName\": null,\r\n        \"telephone\": null,\r\n        \"email\": null\r\n      }\r\n    },\r\n    \"extFields\": null\r\n  },\r\n  \"serviceRequirement\": {\r\n    \"report\": {\r\n      \"reportLanguage\": \"$.serviceRequirement.reportLanguage\",\r\n      \"reportForm\": null,\r\n      \"reportHeader\": \"$.serviceRequirement.reportHeader\",\r\n      \"reportAddress\": \"$.serviceRequirement.reportAddress\",\r\n      \"accreditation\": null,\r\n      \"needConclusion\": null,\r\n      \"needDraft\": \"\",\r\n      \"needPhoto\": \"$.serviceRequirement.isTakePhoto\",\r\n      \"languageList\": [\r\n        {\r\n          \"_templateId\": \"$.customerList[0].customerUsage\",\r\n          \"languageId\": 1,\r\n          \"reportHeader\": \"$.serviceRequirement.reportHeaderEn\",\r\n          \"reportAddress\": \"$.serviceRequirement.reportAddressEn\"\r\n        }\r\n      ],\r\n      \"softcopy\": {\r\n        \"required\": 1,\r\n        \"deliveryTo\": \"$.serviceRequirement.reportDeliveredTo\",\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      },\r\n      \"hardcopy\": {\r\n        \"required\": \"$.serviceRequirement.isHardCopy\",\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      }\r\n    },\r\n    \"sample\": {\r\n      \"returnResidueSample\": {\r\n        \"required\": \"$.serviceRequirement.returnSampleResidueRequirement\",\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      },\r\n      \"returnTestSample\": {\r\n        \"required\": \"$.serviceRequirement.returnSampleTestRequirement\",\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      }\r\n    },\r\n    \"__invoice\": {\r\n      \"invoiceType\": null,\r\n      \"invoiceTitle\": null,\r\n      \"taxNo\": null,\r\n      \"registerAddr\": null,\r\n      \"registerPhone\": null,\r\n      \"bankName\": null,\r\n      \"bankNumber\": null,\r\n      \"actualAmountPaid\": null,\r\n      \"invoiceDelivery\": {\r\n        \"required\": null,\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      }\r\n    }\r\n  },\r\n  \"customerList\": [\r\n    {\r\n      \"customerInstanceId\": null,\r\n      \"customerUsage\": \"#customerUsageFrom($.customerList[*].customerUsage)\",\r\n      \"customerId\": \"$.customerList[*].customerId\",\r\n      \"bossNo\": \"$.customerList[*].bossNumber\",\r\n      \"customerGroupCode\": \"$.customerList[*].customerGroupCode\",\r\n      \"customerName\": \"$.customerList[*].customerNameCn\",\r\n      \"customerAddress\": \"$.customerList[*].addressCn\",\r\n      \"_tempKey1\": \"$.customerList[*].customerNameEn\",\r\n      \"_tempKey2\": \"$.customerList[*].addressEn\",\r\n      \"languageList\": [\r\n        {\r\n          \"_templateId\": \"$.customerList[0].customerUsage\",\r\n          \"languageId\": 1,\r\n          \"customerName\": \"@_tempKey1\",\r\n          \"customerAddress\": \"@_tempKey2\"\r\n        }\r\n      ],\r\n      \"customerContactList\": [\r\n        {\r\n          \"customerContactId\": null,\r\n          \"bossContactId\": null,\r\n          \"bossSiteUseId\": null,\r\n          \"contactName\": \"$.customerList[*].customerPersonName\",\r\n          \"contactTelephone\": \"$.customerList[*].customerPersonPhone1\",\r\n          \"contactMobile\": \"$.customerList[*].customerPersonPhone2\",\r\n          \"contactFax\": \"$.customerList[*].customerPersonFax\",\r\n          \"contactEmail\": \"$.customerList[*].customerPersonEmail\"\r\n        }\r\n      ]\r\n    }\r\n  ],\r\n  \"product\": {\r\n    \"templateId\": \"$.productSampleRspList[0].product.dFFFormID\",\r\n    \"productAttrList\": [\r\n      {\r\n        \"labelCode\": \"\",\r\n        \"labelName\": null,\r\n        \"labelValue\": \"\",\r\n        \"attrSeq\": null,\r\n        \"customerLabel\": null,\r\n        \"dataType\": null,\r\n        \"languageList\": [\r\n          {\r\n            \"languageId\": \"\",\r\n            \"labelName\": \"\",\r\n            \"labelValue\": \"\",\r\n            \"customerLabel\": \"\"\r\n          }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  \"sampleList\": [\r\n    {\r\n      \"templateId\": \"\",\r\n      \"sampleInstanceId\": null,\r\n      \"sampleNo\": null,\r\n      \"externalSampleNo\": null,\r\n      \"sampleAttrList\": [\r\n        {\r\n          \"labelCode\": \"\",\r\n          \"labelName\": null,\r\n          \"labelValue\": \"\",\r\n          \"attrSeq\": null,\r\n          \"customerLabel\": null,\r\n          \"languageList\": [\r\n            {\r\n              \"languageId\": \"\",\r\n              \"labelName\": \"\",\r\n              \"labelValue\": \"\",\r\n              \"customerLabel\": \"\"\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  ],\r\n  \"careLabelList\": [\r\n    {\r\n      \"careInstruction\": \"$.careLabels[*].careInstruction\",\r\n      \"radioType\": \"$.careLabels[*].radioType\",\r\n      \"selectCountry\": \"$.careLabels[*].selectCountry\",\r\n      \"careLabelSeq\": \"$.careLabels[*].careLabelSeq\",\r\n      \"sampleIds\": \"#splitStr($.careLabels[*].selectImgIds)\"\r\n    }\r\n  ],\r\n  \"__testSampleList\": [\r\n    {\r\n      \"testSampleType\": null,\r\n      \"testSampleSeq\": null,\r\n      \"materialAttrList\": [\r\n        {\r\n          \"materialDescription\": null,\r\n          \"materialEndUse\": null,\r\n          \"materialColor\": null,\r\n          \"materialTexture\": null,\r\n          \"materialSampleRemark\": null,\r\n          \"extFields\": {\r\n            \"materialCategory\": null,\r\n            \"materialSku\": null,\r\n            \"materialItem\": null\r\n          }\r\n        }\r\n      ]\r\n    }\r\n  ],\r\n  \"__testLineList\": [\r\n    {\r\n      \"testLineId\": null,\r\n      \"evaluationAlias\": null,\r\n      \"testLineSeq\": null,\r\n      \"citation\": {\r\n        \"citationId\": null,\r\n        \"citationType\": null,\r\n        \"citationFullName\": null\r\n      },\r\n      \"ppTestLineRelList\": [\r\n        {\r\n          \"ppNo\": null,\r\n          \"ppName\": null\r\n        }\r\n      ],\r\n      \"externalInfo\": {\r\n        \"testItemId\": null,\r\n        \"testItemName\": null,\r\n        \"testCitationId\": null,\r\n        \"testCitationName\": null,\r\n        \"checkType\": null\r\n      }\r\n    }\r\n  ],\r\n  \"attachmentList\": [\r\n    {\r\n      \"fileName\": \"$.trfAttachments[*].fileName\",\r\n      \"fileType\": null,\r\n      \"filePath\": null,\r\n      \"cloudId\": \"$.trfAttachments[*].cloudId\"\r\n    }\r\n  ]\r\n}', 'SGSMart Import API', 'system', CURRENT_TIMESTAMP, 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
INSERT INTO `tb_cfg_system_api`(`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`)
    VALUES (23,    12, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{\r\n    \"refSystemId\": \"$.refSystemId\",\r\n    \"productLineCode\": \"SL\",\r\n    \"trfNo\": \"$.trfNo\",\r\n    \"action\": \"GetTICTrfInfo\"}', '{\r\n  \"header\": {\r\n    \"refSystemId\": 12,\r\n    \"source\":1,\r\n    \"trfNo\": \"$.orderNo\",\r\n    \"serviceType\": \"$.baseInfo.isUrgent\",\r\n    \"sampleLevel\": null,\r\n    \"parcelNoList\": \"$.delivers[deliverType = 10].expressNo\",\r\n    \"trfSubmissionDate\": null,\r\n    \"selfTestFlag\": null,\r\n    \"others\": {\r\n      \"trfRemark\": \"#join(\' \',\'发票邮寄地址：\',$.delivers[deliverType=20].receiveName[0],$.delivers[deliverType=20].receivePhone[0],$.delivers[deliverType=20].receiveEmail[0],$.delivers[deliverType=20].receiveCity[0],$.delivers[deliverType=20].receiveTown[0])\"\r\n    },\r\n    \"lab\": {\r\n      \"labId\": \"$.applicationAttr[areaCode=\'labInfo\'][attrCode=\'labId\'].attrValue[0]\",\r\n      \"labCode\": \"$.applicationAttr[areaCode=\'labInfo\'][attrCode=\'labCode\'].attrValue[0]\",\r\n      \"buCode\": \"$.baseInfo.bu\",\r\n      \"labContract\": {\r\n        \"contractName\": null,\r\n        \"telephone\": null,\r\n        \"email\": null\r\n      }\r\n    },\r\n    \"extFields\": {\r\n      \"tic\": {\r\n        \"sampleRequirements\": \"$.baseInfo.sampleRequirements\",\r\n        \"ticCsCode\": \"$.baseInfo.csCode\",\r\n        \"applicationTestMemo\": \"$.applicationForm.testMemo\",\r\n        \"memoInfo\": \"$.memos[memoCode =\'formMemo\'].memoInfo[0]\",\r\n        \"subBuCode\": \"$.baseInfo.subBuCode\"\r\n      }\r\n    }\r\n  },\r\n  \"serviceRequirement\": {\r\n    \"report\": {\r\n      \"reportLanguage\": \"$.baseInfo.reportLua\",\r\n      \"reportForm\": \"$.baseInfo.reportForm\",\r\n      \"reportHeader\": null,\r\n      \"reportAddress\": null,\r\n      \"accreditation\": null,\r\n      \"needConclusion\": null,\r\n      \"needDraft\": \"\",\r\n      \"needPhoto\": \"\",\r\n      \"_languageList\": [\r\n        {\r\n          \"languageId\": null,\r\n          \"reportHeader\": null,\r\n          \"reportAddress\": null\r\n        }\r\n      ],\r\n      \"softcopy\": {\r\n        \"required\": \"#mapping($.baseInfo.reportForm,\'2\':1&\'3\':1,0)\",\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      },\r\n      \"hardcopy\": {\r\n        \"required\": \"#mapping($.baseInfo.reportForm,\'1\':1&\'3\':1,0)\",\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": \"#join(\' \',\'报告邮寄地址：\',$.delivers[deliverType=31].receiveName[0],$.delivers[deliverType=31].receivePhone[0],$.delivers[deliverType=31].receiveEmail[0],$.delivers[deliverType=31].receiveCity[0],$.delivers[deliverType=31].receiveTown[0])\",\r\n        \"deliveryWay\": null\r\n      }\r\n    },\r\n    \"sample\": {\r\n      \"returnResidueSample\": {\r\n        \"required\": null,\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": \"#join(\' \',\'样品退回地址：\',$.delivers[deliverType=30].receiveName[0],$.delivers[deliverType=30].receivePhone[0],$.delivers[deliverType=30].receiveEmail[0],$.delivers[deliverType=30].receiveCity[0],$.delivers[deliverType=30].receiveTown[0])\",\r\n        \"deliveryWay\": null\r\n      },\r\n      \"_returnTestSample\": {\r\n        \"required\": null,\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      }\r\n    },\r\n    \"invoice\": {\r\n      \"invoiceType\": \"$.invoice.invoiceType\",\r\n      \"invoiceTitle\": \"$.invoice.invoiceTitle\",\r\n      \"taxNo\": \"$.invoice.taxNo\",\r\n      \"registerAddr\": \"$.invoice.registerAddr\",\r\n      \"registerPhone\": \"$.invoice.registerPhone\",\r\n      \"bankName\": \"$.invoice.bankAddr\",\r\n      \"bankNumber\": \"$.invoice.bankNumber\",\r\n      \"actualAmountPaid\": \"$.baseInfo.realAmount\",\r\n      \"_invoiceDelivery\": {\r\n        \"required\": null,\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      }\r\n    }\r\n  },\r\n  \"customerList\": [\r\n    {\r\n      \"_templateId\": \"$.applicationAttr[0].areaCode\",\r\n      \"customerInstanceId\": null,\r\n      \"customerUsage\": 1,\r\n      \"customerId\": null,\r\n      \"bossNo\": null,\r\n      \"customerGroupCode\": null,\r\n      \"customerName\": \"$.applicationForm.companyName\",\r\n      \"customerAddress\": null,\r\n      \"_languageList\": [\r\n        {\r\n          \"languageId\": null,\r\n          \"customerName\": null,\r\n          \"customerAddress\": null\r\n        }\r\n      ],\r\n      \"_customerContactList\": [\r\n        {\r\n          \"_templateId\": \"$.applicationAttr[0].areaCode\",\r\n          \"customerContactId\": null,\r\n          \"bossContactId\": null,\r\n          \"bossSiteUseId\": null,\r\n          \"contactName\": null,\r\n          \"contactTelephone\": null,\r\n          \"contactMobile\": null,\r\n          \"contactFax\": null,\r\n          \"contactEmail\": null\r\n        }\r\n      ]\r\n    }\r\n  ],\r\n  \"product\": {\r\n    \"templateId\": \"#dffFormId(12,$.baseInfo.subBuCode)\",\r\n    \"productAttrList\": [\r\n      {\r\n        \"labelCode\": \"#dffFormLabel(12,$.baseInfo.subBuCode,$.sampleForm[*].sampleKey)\",\r\n        \"labelName\": \"#dffFormDisplay(12,$.baseInfo.subBuCode,$.sampleForm[*].sampleKey)\",\r\n        \"labelValue\": \"$.sampleForm[*].sampleValue\",\r\n        \"attrSeq\": \".[index]\",\r\n        \"customerLabel\": \"$.sampleForm[*].sampleKey\",\r\n        \"dataType\": null\r\n      }\r\n    ]\r\n  },\r\n  \"sampleList\": [\r\n    {\r\n      \"templateId\": \"#dffGridId(12,$.baseInfo.subBuCode)\",\r\n      \"sampleInstanceId\": null,\r\n      \"sampleNo\": null,\r\n      \"externalSampleNo\": null,\r\n      \"sampleAttrList\": [\r\n        {\r\n          \"labelCode\": \"#dffGridLabel(12,$.baseInfo.subBuCode,$.sampleForm[*].sampleKey)\",\r\n          \"labelName\": \"#dffGridDisplay(12,$.baseInfo.subBuCode,$.sampleForm[*].sampleKey)\",\r\n          \"labelValue\": \"$.sampleForm[*].sampleValue\",\r\n          \"attrSeq\": \".[index]\",\r\n          \"customerLabel\": \"$.sampleForm[*].sampleKey\"\r\n        }\r\n      ]\r\n    }\r\n  ],\r\n  \"_careLabelList\": [\r\n    {\r\n      \"careInstruction\": null,\r\n      \"radioType\": null,\r\n      \"selectCountry\": null,\r\n      \"careLabelSeq\": null,\r\n      \"sampleIds\": null\r\n    }\r\n  ],\r\n  \"_testSampleList\": [\r\n    {\r\n      \"testSampleType\": null,\r\n      \"testSampleSeq\": null,\r\n      \"materialAttrList\": [\r\n        {\r\n          \"materialDescription\": null,\r\n          \"materialEndUse\": null,\r\n          \"materialColor\": null,\r\n          \"materialTexture\": null,\r\n          \"materialSampleRemark\": null,\r\n          \"extFields\": {\r\n            \"materialCategory\": null,\r\n            \"materialSku\": null,\r\n            \"materialItem\": null\r\n          }\r\n        }\r\n      ]\r\n    }\r\n  ],\r\n  \"testLineList\": [\r\n    {\r\n      \"_tempKey1\": \"$.items[*].itemName\",\r\n      \"_tempKey2\": \"$.items[*].standardCode\",\r\n      \"testLineId\": null,\r\n      \"evaluationAlias\": null,\r\n      \"testLineSeq\": \".[index]\",\r\n      \"_citation\": {\r\n        \"citationId\": null,\r\n        \"citationType\": null,\r\n        \"citationFullName\": null\r\n      },\r\n      \"_ppTestLineRelList\": [\r\n        {\r\n          \"ppNo\": null,\r\n          \"ppName\": null\r\n        }\r\n      ],\r\n      \"externalInfo\": {\r\n        \"testItemId\": null,\r\n        \"testItemName\": \"@_tempKey1\",\r\n        \"testCitationId\": null,\r\n        \"testCitationName\": \"@_tempKey2\",\r\n        \"checkType\": null\r\n      }\r\n    }\r\n  ],\r\n  \"attachmentList\": [\r\n    {\r\n      \"fileName\": \"$.attachments[*].fileName\",\r\n      \"fileType\": null,\r\n      \"filePath\": \"$.attachments[*].fileUrl\",\r\n      \"cloudId\": \"$.attachments[*].cloudId\"\r\n    }\r\n  ]\r\n}\r\n', 'TIC Import API', 'system', CURRENT_TIMESTAMP, 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
INSERT INTO `tb_cfg_system_api`(`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`)
    VALUES (24, 10016, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{\r\n    \"refSystemId\": \"$.refSystemId\",\r\n    \"productLineCode\": \"AFL\",\r\n    \"trfNo\": \"$.trfNo\",\r\n    \"action\": \"GetYiliTrf\"}', '{\r\n  \"header\": {\r\n    \"refSystemId\": \"#refSystemId(\'YiLi\')\",\r\n    \"source\": 1,\r\n    \"trfNo\": \"$.WTDH\",\r\n    \"serviceType\": \"#mapping($.FWLX,\'标准(几个工作日)\':1&\'加急\':2&\'特级\':4)\",\r\n    \"sampleLevel\": null,\r\n    \"parcelNoList\": \"$.KDDH\",\r\n    \"trfSubmissionDate\": null,\r\n    \"selfTestFlag\": null,\r\n    \"others\": {\r\n      \"trfRemark\": \"$.REMARK\"\r\n    },\r\n    \"_lab\": {\r\n      \"labId\": null,\r\n      \"labCode\": null,\r\n      \"buCode\": null,\r\n      \"labContract\": {\r\n        \"contractName\": null,\r\n        \"telephone\": null,\r\n        \"email\": null\r\n      }\r\n    },\r\n    \"_extFields\": {\r\n    }\r\n  },\r\n  \"serviceRequirement\": {\r\n    \"report\": {\r\n      \"reportLanguage\": \"#mapping($.BGYJ,\'中文\':2&\'英文\':1&\'中英文对照\':3)\",\r\n      \"reportForm\": null,\r\n      \"reportHeader\": null,\r\n      \"reportAddress\": null,\r\n      \"accreditation\": null,\r\n      \"needConclusion\": null,\r\n      \"needDraft\": null,\r\n      \"needPhoto\": null,\r\n      \"splitReportBy\": \"#mapping($.BGFS,\'一个样品一份报告\':\'Sample\'&\'多个样品一份报告\':\'Others\'&\'其他\':\'Others\')\",\r\n      \"_languageList\": [\r\n        {\r\n          \"languageId\": null,\r\n          \"reportHeader\": null,\r\n          \"reportAddress\": null\r\n        }\r\n      ],\r\n      \"softcopy\": {\r\n        \"required\": \"#mapping($.BGLX,\'电子报告\':1,0)\",\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      },\r\n      \"hardcopy\": {\r\n        \"required\": \"#mapping($.BGLX,\'纸质报告\':1,0)\",\r\n        \"deliveryTo\": \"$.BGYJDZ\",\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      }\r\n    },\r\n    \"_sample\": {\r\n      \"returnResidueSample\": {\r\n        \"required\": null,\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      },\r\n      \"_returnTestSample\": {\r\n        \"required\": null,\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      }\r\n    },\r\n    \"invoice\": {\r\n      \"invoiceType\": \"#mapping($.FPLX,\'增值税\':1&\'普通\':2)\",\r\n      \"invoiceTitle\": null,\r\n      \"taxNo\": null,\r\n      \"registerAddr\": null,\r\n      \"registerPhone\": null,\r\n      \"bankName\": null,\r\n      \"bankNumber\": null,\r\n      \"actualAmountPaid\": null,\r\n      \"invoiceDelivery\": {\r\n        \"required\": 1,\r\n        \"deliveryTo\": \"$.BGYJDZ\",\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      }\r\n    }\r\n  },\r\n  \"customerList\": [\r\n    {\r\n      \"customerInstanceId\": null,\r\n      \"customerUsage\": 1,\r\n      \"customerId\": null,\r\n      \"bossNo\": null,\r\n      \"customerGroupCode\": null,\r\n      \"customerName\": \"$.WTDMC\",\r\n      \"customerAddress\": \"$.WTDDZ\",\r\n      \"_customerName\": \"$.SAMPLEDATA[*].G_ID\",\r\n      \"languageList\": [\r\n        {\r\n          \"languageId\": 1,\r\n          \"customerName\": \"$.WTDYWMC\",\r\n          \"customerAddress\": \"$.WTDYWDZ\",\r\n          \"_customerName\": \"$.SAMPLEDATA[*].G_ID\"\r\n        }\r\n      ],\r\n      \"customerContactList\": [\r\n        {\r\n          \"customerContactId\": null,\r\n          \"bossContactId\": null,\r\n          \"bossSiteUseId\": null,\r\n          \"contactName\": \"$.LXR\",\r\n          \"contactTelephone\": \"$.GDDH\",\r\n          \"contactMobile\": \"$.SJ\",\r\n          \"contactFax\": \"$.CZ\",\r\n          \"contactEmail\": \"$.DZYJ\",\r\n          \"_customerName\": \"$.SAMPLEDATA[*].G_ID\"\r\n        }\r\n      ]\r\n    },{\r\n      \"customerUsage\": 2,\r\n      \"customerName\": \"$.FKGS\",\r\n      \"_customerName\": \"$.SAMPLEDATA[*].G_ID\"\r\n    }\r\n  ],\r\n  \"_product\": {\r\n    \"templateId\": null,\r\n    \"productAttrList\": [\r\n      {\r\n        \"labelCode\": null,\r\n        \"labelName\": null,\r\n        \"labelValue\": null,\r\n        \"attrSeq\": null,\r\n        \"customerLabel\": null,\r\n        \"dataType\": null\r\n      }\r\n    ]\r\n  },\r\n  \"sampleList\": [\r\n    {\r\n      \"templateId\": null,\r\n      \"sampleInstanceId\": \"$.SAMPLEDATA[*].G_ID\",\r\n      \"sampleNo\": null,\r\n      \"externalSampleNo\": \"$.SAMPLEDATA[*].SC\",\r\n      \"sampleAttrList\": [\r\n        {\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].YPMC\",\r\n          \"attrSeq\": 1,\r\n          \"customerLabel\": \"YPMC\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].SCRQ\",\r\n          \"attrSeq\": 2,\r\n          \"customerLabel\": \"SCRQ\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].PH\",\r\n          \"attrSeq\": 3,\r\n          \"customerLabel\": \"PH\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].CZ\",\r\n          \"attrSeq\": 4,\r\n          \"customerLabel\": \"CZ\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].SL\",\r\n          \"attrSeq\": 5,\r\n          \"customerLabel\": \"SL\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].GG\",\r\n          \"attrSeq\": 6,\r\n          \"customerLabel\": \"GG\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].SB\",\r\n          \"attrSeq\": 7,\r\n          \"customerLabel\": \"SB\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].GYS\",\r\n          \"attrSeq\": 8,\r\n          \"customerLabel\": \"GYS\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].SCCJ\",\r\n          \"attrSeq\": 9,\r\n          \"customerLabel\": \"SCCJ\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].SCSJDZ\",\r\n          \"attrSeq\": 10,\r\n          \"customerLabel\": \"SCSJDZ\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].YPZT\",\r\n          \"attrSeq\": 11,\r\n          \"customerLabel\": \"YPZT\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].YPWH\",\r\n          \"attrSeq\": 12,\r\n          \"customerLabel\": \"YPWH\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].YPLY\",\r\n          \"attrSeq\": 13,\r\n          \"customerLabel\": \"YPLY\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].LB\",\r\n          \"attrSeq\": 14,\r\n          \"customerLabel\": \"LB\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].PX\",\r\n          \"attrSeq\": 15,\r\n          \"customerLabel\": \"PX\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].SJMD\",\r\n          \"attrSeq\": 16,\r\n          \"customerLabel\": \"SJMD\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].JCXMLB\",\r\n          \"attrSeq\": 17,\r\n          \"customerLabel\": \"JCXMLB\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].CCTJ\",\r\n          \"attrSeq\": 18,\r\n          \"customerLabel\": \"CCTJ\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].BLQX\",\r\n          \"attrSeq\": 19,\r\n          \"customerLabel\": \"BLQX\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].SYTJ\",\r\n          \"attrSeq\": 20,\r\n          \"customerLabel\": \"SYTJ\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].YJYPBMWD\",\r\n          \"attrSeq\": 21,\r\n          \"customerLabel\": \"YJYPBMWD\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].ISXJ\",\r\n          \"attrSeq\": 22,\r\n          \"customerLabel\": \"ISXJ\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].BAK1\",\r\n          \"attrSeq\": 23,\r\n          \"customerLabel\": \"BAK1\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].BAK2\",\r\n          \"attrSeq\": 24,\r\n          \"customerLabel\": \"BAK2\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].BAK3\",\r\n          \"attrSeq\": 25,\r\n          \"customerLabel\": \"BAK3\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].BAK4\",\r\n          \"attrSeq\": 26,\r\n          \"customerLabel\": \"BAK4\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo].BAK5\",\r\n          \"attrSeq\": 27,\r\n          \"customerLabel\": \"BAK5\"\r\n        }\r\n      ]\r\n    }\r\n  ],\r\n  \"_careLabelList\": [\r\n    {\r\n      \"careInstruction\": null,\r\n      \"radioType\": null,\r\n      \"selectCountry\": null,\r\n      \"careLabelSeq\": null,\r\n      \"sampleIds\": null\r\n    }\r\n  ],\r\n  \"testSampleList\": [\r\n    {\r\n      \"testSampleType\": 101,\r\n      \"testSampleSeq\": \".[index]\",\r\n      \"externalSampleNo\": \"$.SAMPLEDATA[*].SC\",\r\n      \"_materialAttrList\": [\r\n        {\r\n          \"materialDescription\": null,\r\n          \"materialEndUse\": null,\r\n          \"materialColor\": null,\r\n          \"materialTexture\": null,\r\n          \"materialSampleRemark\": null,\r\n          \"extFields\": {\r\n            \"materialCategory\": null,\r\n            \"materialSku\": null,\r\n            \"materialItem\": null\r\n          }\r\n        }\r\n      ]\r\n    }\r\n  ],\r\n  \"testMatrixList\": [\r\n    {\r\n      \"testMatrixId\": null,\r\n      \"externalTestMatrixId\": \"$.SAMPLEDATA[*].PADATA[*].G_ID\",\r\n      \"testItemId\": \"$.SAMPLEDATA[*].PADATA[*].PA\",\r\n      \"testSampleNo\": null,\r\n      \"externalTestSampleNo\": \"$.SAMPLEDATA[*].PADATA[*].SC\",\r\n      \"testLineInstanceId\": null,\r\n      \"testSampleInstanceId\": null\r\n    }\r\n  ],\r\n  \"testLineList\": [\r\n    {\r\n      \"testLineId\": null,\r\n      \"evaluationAlias\": null,\r\n      \"testLineSeq\": \".[index]\",\r\n      \"_citation\": {\r\n        \"citationId\": null,\r\n        \"citationType\": null,\r\n        \"citationFullName\": null\r\n      },\r\n      \"_ppTestLineRelList\": [\r\n        {\r\n          \"ppNo\": null,\r\n          \"ppName\": null\r\n        }\r\n      ],\r\n      \"_testItemId\": \"$.SAMPLEDATA[*].PADATA[*].PA\",\r\n      \"_testItemName\": \"$.SAMPLEDATA[*].PADATA[*].PANAME\",\r\n      \"_testCitationId\": \"$.SAMPLEDATA[*].PADATA[*].METHOD\",\r\n      \"_testCitationName\": \"$.SAMPLEDATA[*].PADATA[*].METHODNAME\",\r\n      \"externalInfo\": {\r\n        \"testItemId\": \"@_testItemId\",\r\n        \"testItemName\": \"@_testItemName\",\r\n        \"testCitationId\": \"@_testCitationId\",\r\n        \"testCitationName\": \"@_testCitationName\",\r\n        \"checkType\": null\r\n      }\r\n    }\r\n  ],\r\n  \"_attachmentList\": [\r\n    {\r\n      \"fileName\": null,\r\n      \"fileType\": null,\r\n      \"filePath\": null,\r\n      \"cloudId\": null\r\n    }\r\n  ]\r\n}\r\n', 'YILI Import API', 'system', CURRENT_TIMESTAMP, 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);



INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`)
VALUES (24, NULL, NULL, '10002', 'SL', 2, 'REFSYSTEM.API.IMPORT', '22', 'system', CURRENT_TIMESTAMP, 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`)
VALUES (25, NULL, NULL, '12', 'SL', 2, 'REFSYSTEM.API.IMPORT', '23', 'system', CURRENT_TIMESTAMP, 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`)
VALUES (26, NULL, NULL, '10016', 'SL', 2, 'REFSYSTEM.API.IMPORT', '24', 'system', CURRENT_TIMESTAMP, 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);




-- XQW
INSERT INTO `tb_cfg_event_subscribe`(`id`, `system_id`, `event_code`, `api_id`, `priority`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (51, 10002, 1, 28, 0, 0, 2023, 'messageNotifyHandler', '', 'XQW', '2023-12-19 15:00:00', 'XQW', '2023-12-19 15:00:06', '2023-12-19 15:00:06');
INSERT INTO `tb_cfg_system_api`(`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (28, 10002, 2, 'https://test.sgsmart-online.com/sgs-mart/trf/orderToTrf', 2, '{\r\n    \"trfTemplateId\":null,\r\n    \"templateName\":null,\r\n    \"productLineId\":null,\r\n    \"productLineCode\":\"$.trfList[0].lab.buCode\",\r\n    \"remark\":\"$.trfList[0].others.trfRemark\",\r\n    \"orderNo\":\"\",\r\n    \"serviceType\":\"$.trfList[0].serviceType\",\r\n    \"serviceTypeName\":\"$.trfList[0].serviceType\",\r\n    \"reportDeliveredTo\":\"$.trfList[0].serviceRequirement.report.softcopy.deliveryTo\",\r\n    \"failedReportDeliveredTo\":\"$.trfList[0].serviceRequirement.report.softcopy.deliveryTo\",\r\n    \"applicantUser\":\"$.trfList[0].lab.labContact.contactName\",\r\n    \"testPackageAllIds\":\"\",\r\n    \"trfType\":10,\r\n		\"customerReferenceNo\":\"$.trfList[0].trfNo\",\r\n    \"isSelfRefrence\":0,\r\n    \"trfSourceId\":null,\r\n    \"trfSourceType\":4,\r\n    \"isGeneralRequest\":0,\r\n    \"dffFormId\":null,\r\n    \"dffGridId\":null,\r\n    \"materialIds\":null,\r\n    \"actionType\":\"add\",\r\n    \"dffFormData\":null,\r\n    \"dffGridData\":null,\r\n    \"careLabelData\":null,\r\n    \"testPackageIds\":null,\r\n    \"trfCustomer\":{\r\n        \"buyerCustomerId\":\"$.customerList[customerUsage=3].customerId\",\r\n        \"buyerAccountId\":\"$.customerList[customerUsage=3].customerContactList[0].customerContactId\",\r\n        \"buyerCustomerNameCn\":\"$.customerList[customerUsage=3].languageList[language=2].customerName\",\r\n        \"buyerCustomerNameEn\":\"$.customerList[customerUsage=3].customerName\",\r\n        \"buyerCustomerGroupId\":null,\r\n        \"buyerCustomerGroupCode\":\"$.customerList[customerUsage=3].customerGroupCode\",\r\n        \"buyerCustomerGroupName\":\"$.customerList[customerUsage=3].customerName\",\r\n        \"agentCustomerId\":\"$.customerList[customerUsage=4].customerId\",\r\n        \"agentAccountId\":\"$.customerList[customerUsage=4].customerContactList[0].customerContactId\",\r\n        \"agentCustomerNameCn\":\"$.customerList[customerUsage=4].languageList[languageId=2].customerName\",\r\n        \"agentCustomerNameEn\":\"$.customerList[customerUsage=4].customerName\",\r\n        \"agentCustomerGroupId\":null,\r\n        \"agentCustomerGroupCode\":\"$.customerList[customerUsage=4].customerGroupCode\",\r\n        \"agentCustomerGroupName\":\"$.customerList[customerUsage=4].customerName\",\r\n        \"sgsCustomerId\":null,\r\n        \"sgsAccountId\":null,\r\n        \"customerId\":\"$.customerList[customerUsage=1].customerId\",\r\n        \"customerNo\":\"$.customerList[customerUsage=1].bossNo\",\r\n        \"trfCustomerRole\":null,\r\n        \"customerAddressId\":null,\r\n        \"customerNameZh\":\"$.customerList[customerUsage=1].languageList[languageId=2].customerName\",\r\n        \"customerNameEn\":\"$.customerList[customerUsage=1].customerName\",\r\n        \"customerAddressZh\":\"$.customerList[customerUsage=1].languageList[languageId=2].customerAddress\",\r\n        \"customerAddressEn\":\"$.customerList[customerUsage=1].customerName\",\r\n        \"payCustomerNameZh\":\"$.customerList[customerUsage=2].languageList[languageId=2].customerName\",\r\n        \"payCustomerNameEn\":\"$.customerList[customerUsage=2].customerName\",\r\n        \"payCustomerAddressZh\":\"$.customerList[customerUsage=2].languageList[languageId=2].customerAddress\",\r\n        \"payCustomerAddressEn\":\"$.customerList[customerUsage=2].customerName\",\r\n        \"isSame\":1,\r\n        \"beLongId\":null\r\n    },\r\n    \"trfCustomerContact\":{\r\n        \"applyContactName\":\"$.customerList[customerUsage=1].customerContactList[0].contactName\",\r\n        \"applyContactTel\":\"$.customerList[customerUsage=1].customerContactList[0].contactTelephone\",\r\n        \"applyContactEmail\":\"$.customerList[customerUsage=1].customerContactList[0].contactEmail\",\r\n        \"applyContactFax\":\"$.customerList[customerUsage=1].customerContactList[0].contactFax\",\r\n        \"payContactName\":\"$.customerList[customerUsage=2].customerContactList[0].contactName\",\r\n        \"payContactTel\":\"$.customerList[customerUsage=2].customerContactList[0].contactTelephone\",\r\n        \"payContactEmail\":\"$.customerList[customerUsage=2].customerContactList[0].contactEmail\",\r\n        \"payContactFax\":\"$.customerList[customerUsage=2].customerContactList[0].contactFax\",\r\n        \"isSame\":1\r\n    },\r\n    \"invoice\":{\r\n        \"invoiceType\":\"$.trfList[0].serviceRequirement.invoice.invoiceType\",\r\n        \"invoiceTypeName\":null,\r\n        \"invoiceTitle\":\"$.trfList[0].serviceRequirement.invoice.invoiceTitle\",\r\n        \"taxNo\":\"$.trfList[0].serviceRequirement.invoice.taxNo\",\r\n        \"invoiceTel\":\"$.trfList[0].serviceRequirement.invoice.registerPhone\",\r\n        \"invoiceAddress\":\"$.trfList[0].serviceRequirement.invoice.registerAddr\",\r\n        \"bankName\":\"$.trfList[0].serviceRequirement.invoice.bankName\",\r\n        \"bankAccount\":\"$.trfList[0].serviceRequirement.invoice.bankNumber\"\r\n    },\r\n    \"trfLab\":{\r\n        \"labCode\":\"$.trfList[0].lab.labCode\",\r\n        \"labName\":null,\r\n        \"labAddress\":null,\r\n        \"locationId\":null,\r\n        \"locationCode\":null,\r\n        \"locationName\":null,\r\n        \"countryCode\":null,\r\n        \"countryName\":null,\r\n        \"countryId\":null\r\n    },\r\n    \"trfLabContact\":{\r\n        \"labCode\":\"$.trfList[0].lab.labCode\",\r\n        \"labName\":null,\r\n        \"labAddress\":null,\r\n        \"contactName\":\"$.trfList[0].lab.labContact.contactName\",\r\n        \"contactTel\":\"$.trfList[0].lab.labContact.contactTelephone\",\r\n        \"contactEmail\":\"$.trfList[0].lab.labContact.contactEmail\"\r\n    },\r\n    \"trfProduct\":{\r\n        \"dffFormId\":\"$.productList[0].templateId\",\r\n        \"dffGridId\":\"$.sampleList[0].templateId\",\r\n        \"dffFormData\":\"#dffJsonFn(productList,$.productList)\",\r\n        \"dffGridData\":\"#dffJsonFn(sampleList,$.sampleList)\"\r\n    },\r\n    \"servicRequire\":{\r\n        \"isComment\":null,\r\n        \"isCopy\":null,\r\n        \"isPhoto\":\"$.trfList[0].serviceRequirement.report.needPhoto\",\r\n        \"isConfimCover\":null,\r\n        \"isQuotation\":null,\r\n        \"returnSampleRequire\":\"#sgsMartGetReturnSampleRequireFn($.serviceRequirement.sample)\",\r\n        \"returnSampleName\":null,\r\n        \"reportLanguage\":\"$.trfList[0].serviceRequirement.report.reportLanguage\",\r\n        \"reportLanguageName\":\"#mapping($.trfList[0].serviceRequirement.report.reportLanguage,\'1\'：\'英文报告\'&\'2\'：\'中文报告\'&\'3\'：\'中英文报告\') \",\r\n        \"reportHeader\":\"$.trfList[0].serviceRequirement.report.languageList[languageId=2].reportHeader \",\r\n        \"reportAddress\":\"$.trfList[0].serviceRequirement.report.languageList.reportAddress\",\r\n        \"otherRequirea\":null,\r\n        \"reportHeaderEn\":\"$.trfList[0].serviceRequirement.report.reportHeader\",\r\n        \"reportAddressEn\":\"$.trfList[0].serviceRequirement.report.reportAddress\"\r\n    },\r\n    \"trfAttachments\":[\r\n        {\r\n            \"attachmentId\":\"$.trfList[0].attachmentList[*].cloudId\",\r\n            \"fileName\":\"$.trfList[0].attachmentList[*].fileName\",\r\n            \"size\":null\r\n        }\r\n    ],\r\n    \"carelabelInstances\":[\r\n        {\r\n            \"careInstruction\":\"$.careLabelList[*].careInstruction\",\r\n            \"radioType\":\"$.careLabelList[*].radioType\",\r\n            \"selectCountry\":\"$.careLabelList[*].selectCountry\",\r\n            \"selectImgIds\":null,\r\n            \"productItemNos\":null,\r\n            \"careLabelSeq\":\"$.careLabelList[*].careLabelSeq\",\r\n            \"careLabelFileId\":null,\r\n            \"productItemNo\":null,\r\n            \"imgArray\":[\r\n                {\r\n                    \"imageDetailId\":null,\r\n                    \"sequenceNo\":null,\r\n                    \"picture\":null,\r\n                    \"checked\":null\r\n                }\r\n            ]\r\n        }\r\n    ],\r\n    \"compositionIngredients\":[\r\n        {\r\n            \"chemicalEnName\":null,\r\n            \"chemicalZhName\":null,\r\n            \"casNo\":null,\r\n            \"composition\":null,\r\n            \"sortedIndex\":null\r\n        }\r\n    ],\r\n    \"testPackageList\":[\r\n        {\r\n            \"testPackageId\":null,\r\n            \"parentId\":null,\r\n            \"testPackageName\":null,\r\n            \"isSelect\":null,\r\n            \"remark\":null,\r\n            \"sort\":null\r\n        }\r\n    ]\r\n}', '{}', 'SgsMart OrderToTrf调用', 'XQW', '2023-12-13 19:30:08', 'XQW', '2023-12-13 19:30:13', '2023-12-14 19:26:21');
