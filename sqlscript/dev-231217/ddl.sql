
CREATE TABLE `tb_task_object_rel` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID唯一标识',
                                      `task_id` bigint(20) NOT NULL COMMENT 'taskInfo外键',
                                      `object_type` tinyint(4) NOT NULL COMMENT '数据类型：Report - 1， Invoice - 2， Quotation - 3',
                                      `object_no` varchar(64) NOT NULL COMMENT '关联的数据编号：reportNo、invoiceNo、quotationNo',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4

ALTER TABLE `tb_cfg_system_api`
    MODIFY COLUMN `response_body_template` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '返回数据的模板' AFTER `request_body_template`;