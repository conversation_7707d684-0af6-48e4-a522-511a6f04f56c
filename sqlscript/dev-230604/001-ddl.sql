ALTER TABLE `tb_trf`
    MODIFY COLUMN `pending_flag` tinyint(4) NULL DEFAULT 0 COMMENT '0: activate, 1: pending' AFTER `trf_remark`,
    MODIFY COLUMN `pending_type` int (11) NULL DEFAULT NULL AFTER `pending_flag`;

ALTER TABLE `tb_trf`
    ADD COLUMN `cancel_type` int (11)   NULL DEFAULT NULL COMMENT '取消类型' AFTER `pending_remark`,
ADD COLUMN `cancel_remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取消原因' AFTER `cancel_type`,
ADD COLUMN `remove_type`  int (11)   NULL DEFAULT NULL COMMENT '删除类型' AFTER `cancel_remark`,
ADD COLUMN `remove_remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除原因' AFTER `remove_type`;

ALTER TABLE `tb_cfg_info`
    MODIFY COLUMN `config_value` varchar (5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置值' AFTER `config_key`;

ALTER TABLE `tb_trf_attachment`
    MODIFY COLUMN `file_path` varchar (2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `file_name`;

ALTER TABLE `tb_trf_test_sample_file`
    MODIFY COLUMN `file_path` varchar (2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `file_name`;

ALTER TABLE `tb_trf_order`
ADD COLUMN `pending_flag` tinyint(4) NULL DEFAULT 0 COMMENT '0: activate, 1: pending' AFTER `bound_status`,
ADD COLUMN `pending_type`  int (11)   NULL DEFAULT NULL AFTER `pending_flag`,
ADD COLUMN `pending_remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Pending备注' AFTER `pending_type`,
ADD COLUMN `cancel_type`  int (11)   NULL DEFAULT NULL COMMENT '取消类型' AFTER `pending_remark`,
ADD COLUMN `cancel_remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取消原因' AFTER `cancel_type`;

ALTER TABLE `tb_trf_order`
    MODIFY COLUMN `order_id` varchar (80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单id' AFTER `system_id`,
    MODIFY COLUMN `order_no` varchar (80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单号' AFTER `order_id`;


CREATE TABLE `tb_trf_order_log`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT,
    `trf_order_id`            bigint(20) NOT NULL,
    `trf_id`                  bigint(20) DEFAULT NULL,
    `system_id`               int(11) DEFAULT NULL COMMENT '归属系统id',
    `order_id`                varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   DEFAULT NULL COMMENT '订单id',
    `order_no`                varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   DEFAULT NULL COMMENT '订单号',
    `order_status`            int(11) DEFAULT NULL,
    `bound_status`            int(11) NOT NULL DEFAULT '0' COMMENT 'Trf绑定状态（1：已绑定、2：已解绑）',
    `active_indicator`        tinyint(1) NOT NULL DEFAULT '1' COMMENT '有效无效标记：0: inactive, 1: active',
    `pending_flag`            tinyint(4) DEFAULT '0' COMMENT '0: activate, 1: pending',
    `pending_type`            int(11) DEFAULT NULL,
    `pending_remark`          varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Pending备注',
    `cancel_type`             int(11) DEFAULT NULL COMMENT '取消类型',
    `cancel_remark`           varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '取消原因',
    `created_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   DEFAULT 'system' COMMENT '创建人',
    `created_date`            datetime                                                       DEFAULT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   DEFAULT 'system' COMMENT '修改人',
    `modified_date`           datetime                                                       DEFAULT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime NOT NULL                                              DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                       `idx_created_time` (`created_date`) USING BTREE,
    KEY                       `idx_trf_id` (`trf_id`) USING BTREE,
    KEY                       `idx_trf_order_id` (`trf_order_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

ALTER TABLE `tb_trf`
DROP
COLUMN `pending_type`,
DROP
COLUMN `pending_remark`;



CREATE TABLE `tb_trf_lab`
(
    `id`                bigint(20) NOT NULL ,
    `lab_id`                int(11) NOT NULL ,
    `trf_id`            bigint(20) NULL DEFAULT NULL,
    `lab_code`          varchar(50)  NULL DEFAULT NULL COMMENT '实验室编码',
    `bu_code`          varchar(50)  NULL DEFAULT NULL COMMENT 'bu编码',
    `contact_name`           varchar(50)  NULL DEFAULT NULL,
    `email`          varchar(50)  NULL DEFAULT NULL,
    `telephone`          varchar(100)  NULL DEFAULT NULL,
    `active_indicator` int(1) NOT NULL DEFAULT '1' COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by` varchar(50) DEFAULT 'system' COMMENT '创建人',
    `created_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modified_by` varchar(50) DEFAULT 'system' COMMENT '修改人',
    `modified_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `last_modified_timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX               `idx_created_date`(`created_date`) USING BTREE,
    INDEX               `idx_trf_id`(`trf_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '实验室' ROW_FORMAT = Dynamic;

CREATE TABLE `tb_trf_log` (
                              `id` bigint(20) NOT NULL COMMENT '主键',
                              `ref_system_id` int(11) NOT NULL,
                              `trf_no` varchar(50) NOT NULL COMMENT 'Trf 编号',
                              `from_status` tinyint(4) NOT NULL COMMENT '变化前状态',
                              `to_status` tinyint(4) NOT NULL COMMENT '变化后状态',
                              `change_type` tinyint(4) DEFAULT NULL COMMENT 'trf变更类型 1:TRF生命周期正常变化  2:trf被cancel  3:trf被remove 4:trf被pending ',
                              `change_subtype` int(10) DEFAULT NULL COMMENT 'trf变更具体类型，如：TRF cancel的类型',
                              `change_remark` varchar(200) DEFAULT NULL COMMENT '变更原因',
                              `pending_flag` tinyint(4) DEFAULT '0' COMMENT '0: unpending, 1: pending',
                              `created_by` varchar(50) DEFAULT 'system' COMMENT '创建人',
                              `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `modified_by` varchar(50) DEFAULT 'system' COMMENT '修改人',
                              `modified_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                              `last_modified_timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

ALTER TABLE `tb_task_info`
    ADD COLUMN `depend_condition` varchar(500) NULL COMMENT '前置条件,见TaskCondition类' AFTER `depend_task`;
ALTER TABLE `tb_task_info`
    ADD COLUMN `identity_id` int NOT NULL COMMENT 'identity id' AFTER `id`,
DROP INDEX `idx_extId`,
ADD INDEX `idx_identityId_extId`(`identity_id`, `ext_id`) USING BTREE;

ALTER TABLE `tb_cfg_system_api`
    MODIFY COLUMN `request_body_template` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '请求数据的模板' AFTER `request_method`,
    MODIFY COLUMN `response_body_template` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '返回数据的模板' AFTER `request_body_template`,
    ADD COLUMN `remark` varchar(150) NULL COMMENT 'api备注' AFTER `response_body_template`;


ALTER TABLE `tb_trf_log`
    ADD INDEX `idx_trfNo`(`ref_system_id`, `trf_no`) USING BTREE,
ADD INDEX `idx_created_date`(`created_date`) USING BTREE;


ALTER TABLE `tb_trf`
    MODIFY COLUMN `ext_fields` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展信息json结构' AFTER `remove_remark`;
