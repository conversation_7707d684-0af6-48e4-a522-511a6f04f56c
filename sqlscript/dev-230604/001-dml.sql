
UPDATE `tb_dfv_template_field_constraint` SET `constraint_value2` = '500'  WHERE `id` = 75;
UPDATE `tb_dfv_template_field_constraint` SET `constraint_value2` = '500'  WHERE `id` = 81;
UPDATE `tb_dfv_template_field_constraint` SET `constraint_value2` = '500'  WHERE `id` = 165;


delete from  tb_dfv_template_field_constraint   where  id in(84,85,86);

UPDATE `tb_dict_value` SET `system_id` = 40, `dict_type` = 'RefSystemId', `dict_value` = '12', `dict_label` = 'TIC系统', `active_indicator` = 1, `created_by` = 'system', `created_date` = '2023-05-04 15:35:32', `modified_by` = 'system', `modified_date` = '2023-05-04 15:35:48', `last_modified_timestamp` = '2023-05-25 18:58:10' WHERE `id` = 3;

INSERT INTO `tb_dict_value`(`id`, `system_id`, `dict_type`, `dict_value`, `dict_label`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (17, 40, 'SystemId', '2', 'SODA', 1, 'system', '2023-05-04 15:35:32', 'system', '2023-05-04 15:35:48', '2023-06-12 17:07:08');


UPDATE `tb_dfv_template_field_constraint` SET `type` = 'NotEmpty'  WHERE `id` = 68;
