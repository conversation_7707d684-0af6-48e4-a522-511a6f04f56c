-- 添加新的字段
ALTER TABLE tb_trf_quotation
ADD COLUMN trf_no varchar(50) DEFAULT NULL COMMENT 'TRF 编号',
ADD COLUMN ref_system_id int(11) DEFAULT NULL COMMENT '客户源系统编号',
ADD COLUMN report_no varchar(50) DEFAULT NULL COMMENT '报告号',
ADD COLUMN quotation_version_id int(11) NOT NULL COMMENT '报价单版本号',
ADD COLUMN quotation_status int(11) DEFAULT '2' COMMENT 'Quotation Status（1-Draft；2-Generated；3-Confirmed；4-Closed；5-Phase Out）',
ADD COLUMN currency varchar(10) DEFAULT NULL COMMENT '币种编码',
ADD COLUMN net_amount decimal(20,2) DEFAULT NULL COMMENT '税前总价',
ADD COLUMN vat_amount decimal(20,2) DEFAULT NULL COMMENT '税费',
ADD COLUMN total_amount decimal(20,2) DEFAULT NULL COMMENT '汇总金额',
ADD COLUMN discount decimal(20,2) DEFAULT NULL COMMENT '调整折扣',
ADD COLUMN adjustment_amount decimal(20,2) DEFAULT NULL COMMENT '调整金额',
ADD COLUMN final_amount decimal(20,2) DEFAULT NULL COMMENT '最终金额',
ADD COLUMN free_quotation_flag int(11) DEFAULT '0' COMMENT '是否免单',
ADD COLUMN delivery_flag int(10) DEFAULT '0' COMMENT 'Delivery Flag; 0 : 未发，1：进行中 ；2 发送成功',
ADD COLUMN approve_flag int(10) DEFAULT '0' COMMENT 'Approve Flag: 0 - 未复核 1- 已复核';
-- 添加新的索引
ALTER TABLE `tb_trf_quotation` ADD INDEX `idx_multi_columns`(`trf_no`, `ref_system_id`, `quotation_no`, `quotation_version_id`) USING BTREE;

CREATE TABLE `tb_trf_approval` (
                                   `id` bigint(20) NOT NULL COMMENT 'ID唯一标识',
                                   `trf_id` bigint(20) DEFAULT NULL COMMENT 'tb_trf 唯一标识',
                                   `trf_no` varchar(50) DEFAULT NULL COMMENT 'TRF 编号',
                                   `system_id` int(11) DEFAULT NULL COMMENT '系统编号',
                                   `object_type` int(11) NOT NULL COMMENT '对象类型：3、Report； 5、Quotation；6、Invoice',
                                   `object_no` varchar(80) NOT NULL COMMENT '对象业务编号',
                                   `trf_object_id` bigint(20) DEFAULT NULL COMMENT '关联的Trf_”object“主键 （object No 可能会重复）',
                                   `approve_result` int(11) DEFAULT NULL COMMENT 'Approve result（1-已确认；2-已支付；3-需要修改）',
                                   `approve_result_text` varchar(1024) DEFAULT NULL COMMENT '确认结果文本',
                                   `approve_remark` varchar(1024) DEFAULT NULL COMMENT '确认结果备注',
                                   `approve_by` varchar(50) DEFAULT NULL COMMENT '审批人',
                                   `customer_en` varchar(80) DEFAULT NULL COMMENT '客户英文名',
                                   `customer_cn` varchar(80) DEFAULT NULL COMMENT '客户中文名',
                                   `active_indicator` tinyint(4) DEFAULT '1' COMMENT '有效无效标记：0: inactive, 1: active',
                                   `created_by` varchar(50) DEFAULT 'system' COMMENT '创建人',
                                   `created_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `modified_by` varchar(50) DEFAULT 'system' COMMENT '修改人',
                                   `modified_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                   `last_modified_timestamp` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
                                   PRIMARY KEY (`id`),
                                   KEY `idx_trf_no_approve_result` (`trf_no`,`approve_result`),
                                   KEY `idx_trf_id` (`trf_id`)
) ENGINE=InnoDB;
