INSERT INTO `tb_cfg_event_subscribe`(`id`, `subscriber`, `ref_system_id`, `event_code`, `api_id`, `priority`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (54, 10002, 10002, 1020, 31, 0, 0, 0, 'messageNotifyHandler', '', 'XQW', '2024-01-18 09:58:40', 'XQW', '2024-01-18 09:58:43', '2024-01-18 09:58:43');
INSERT INTO `tb_cfg_event_subscribe`(`id`, `subscriber`, `ref_system_id`, `event_code`, `api_id`, `priority`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (55, 15, 15, 2020, 32, 0, 0, 0, 'messageNotifyHandler', '', 'XQW', '2024-01-18 10:22:30', 'XQW', '2024-01-18 10:22:33', '2024-01-18 10:22:33');
INSERT INTO `tb_cfg_system_api`(`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (31, 10002, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{\n    \"body\": {\n        \"msgId\": \"$.extra.randomSequence\",\n        \"trfList\": [\n            {\n                \"account\": \"$.extra.syncInfo.operator\",\n                \"accountEmail\": \"$.extra.syncInfo.operatorEmail\",\n                \"csEmail\": \"$.extra.syncInfo.orderList[0].contactPersonList[0].contactEmail\",\n                \"csName\": \"$.extra.syncInfo.orderList[0].contactPersonList[0].contactName\",\n                \"csTel\": \"$.extra.syncInfo.$.extra.syncInfo.orderList[0].contactPersonList[0].contactTelephone\",\n                \"nAmount\": \"$.extra.syncInfo.quotationList[*].netAmount\",\n                \"orderNo\": \"$.extra.syncInfo.orderList[0].orderNo\",\n                \"pdfCloudID\": \"$.extra.syncInfo.quotationList[*].quotationFileList[0].cloudId\",\n                \"preQuotationType\": 0,\n                \"quotationNo\": \"$.extra.syncInfo.$.extra.syncInfo.quotationList[*].quotationNo\",\n                \"quotationVersionId\": \"$.extra.syncInfo.$.extra.syncInfo.quotationList[*].quotationVersionId\",\n                \"quoteCurrency\": \"$.extra.syncInfo.quotationList[*].currency\",\n                \"reportNo\": \"$.extra.syncInfo.quotationList[*].reportNo\",\n                \"sumOfTax\": \"$.extra.syncInfo.quotationList[*].vatAmount\",\n                \"totalDiscountAmount\": \"$.extra.syncInfo.quotationList[*].finalAmount\",\n                \"trfNo\": \"$.extra.syncInfo.trfList[0].trfNo\"\n            }\n        ]\n    },\n    \"bu\": \"$.extra.productLineCode\",\n    \"dataStatus\": \"SendQuotation\",\n    \"objectNumber\": \"$.extra.syncInfo.trfList[0].trfNo\",\n    \"refSystemId\": 2\n}', '{}', 'SyncQuotationToTrf', 'XQW', '2024-01-18 09:23:32', 'XQW', '2024-01-18 09:23:32', '2024-01-18 09:23:32');
INSERT INTO `tb_cfg_system_api`(`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (32, 15, 1, 'http://cnapp-test.sgs.net/api/priceEngineApi/quotation/v1/sendQuotationStatusForSgsmart', 2, '{\r\n    \"actionType\":\"$.extra.syncInfo.actionType\",\r\n    \"productLineCode\":\"$.extra.syncInfo.productLineCode\",\r\n    \"userName\":\"$.extra.syncInfo.userName\",\r\n    \"data\":{\r\n        \"trfNo\":\"$.extra.syncInfo.trfNo\",\r\n        \"refSystemId\":\"$.extra.syncInfo.refSystemId\",\r\n        \"labCode\":\"$.extra.syncInfo.labCode\",\r\n        \"quotationNo\":\"$.extra.syncInfo.quotationNo\",\r\n        \"quotationVersionId\":\"$.extra.syncInfo.quotationVersionId\",\r\n        \"quotationStatus\":\"$.extra.syncInfo.quotationStatus\",\r\n        \"quotationStatusText\":\"$.extra.syncInfo.quotationStatusText\",\r\n        \"modifyContent\":\"$.extra.syncInfo.modifyContent\",\r\n        \"account\":\"$.extra.syncInfo.account\",\r\n        \"reportNo\":\"$.extra.syncInfo.reportNo\",\r\n        \"customerEn\":\"$.extra.syncInfo.customerEn\",\r\n        \"customerCn\":\"$.extra.syncInfo.customerCn\"\r\n    }\r\n}', '{}', 'SendQuotationStatus', 'XQW', '2024-01-18 10:21:36', 'XQW', '2024-01-18 10:21:40', '2024-01-18 10:21:40');
