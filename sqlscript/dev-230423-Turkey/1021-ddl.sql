SET NAMES utf8mb4;
SET
FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tb_dfv_field_constraint
-- ----------------------------
CREATE TABLE `tb_dfv_field_constraint`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `function_id`             bigint(20) NOT NULL COMMENT '功能id',
    `field_id`                bigint(20) NOT NULL COMMENT '属性id',
    `type`                    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '约束类型',
    `constraint_value1`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '约束条件值1',
    `constraint_value2`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '约束条件值2',
    `sort_num`                int(11) NOT NULL COMMENT '排序',
    `field_id_ref`            bigint(20) NOT NULL COMMENT '关联属性：例如A属性的值要大于B属性值的约束时，需要指定B属性的属性id',
    `active_indicator`        int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NOT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NOT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uidx_func_field_constraint_type`(`function_id`, `field_id`, `type`) USING BTREE,
    INDEX                     `idx_func_id`(`function_id`) USING BTREE,
    INDEX                     `idx_created_date`(`created_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_dfv_func_field
-- ----------------------------
CREATE TABLE `tb_dfv_func_field`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `system_id`               int(11) NOT NULL COMMENT '归属系统id',
    `function_id`             bigint(20) NOT NULL COMMENT '所属功能id',
    `field_code`              varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '属性code',
    `field_path`              varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '属性路径，由英文逗号链接的对象树父子关系，例如trf.order.lab.labCode',
    `full_field_path`         varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性全路径，与field_path不同，当对象tree某个节点出现集合类型时，full_field_path会描述整个路径，而field path只从集合的item开始，例如\r\ntrf.orderList.orderNo，fullFieldPath为trf.orderList[].orderNo，而fieldPath为orderNo',
    `field_type`              varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '属性类型：\r\ncollection，string，integer，long，digits，object, boolean,  date',
    `label_name`              varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '属性名称',
    `sort_num`                int(11) NOT NULL COMMENT '排序',
    `parent_id`               bigint(20) NOT NULL COMMENT '父属性id，0为无父属性，其他表示父属性id',
    `active_indicator`        int(1) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NOT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NOT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX                     `uidx_func_field`(`system_id`, `function_id`, `field_code`) USING BTREE,
    INDEX                     `idx_created_date`(`created_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT =1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


CREATE TABLE `tb_dfv_function`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `system_id`               int(11) NOT NULL COMMENT '归属系统id',
    `function_code`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '功能代码',
    `description`             varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '功能描述',
    `active_indicator`        int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NOT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NOT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uidx_sys_func`(`system_id`, `function_code`) USING BTREE,
    INDEX                     `idx_created_date`(`created_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE `tb_dfv_template`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `system_id`               int(11) NOT NULL COMMENT '归属系统id',
    `template_code`           varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板代码',
    `function_id`             bigint(20) NOT NULL COMMENT '归属功能id',
    `description`             varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板功能描述',
    `active_indicator`        int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NOT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NOT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `idx_sys_tpl`(`system_id`, `template_code`) USING BTREE,
    INDEX                     `idx_created_date`(`created_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_dfv_template_field
-- ----------------------------
DROP TABLE IF EXISTS `tb_dfv_template_field`;
CREATE TABLE `tb_dfv_template_field`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `template_id`             bigint(20) NOT NULL COMMENT '模板id',
    `field_id`                bigint(20) NOT NULL COMMENT '属性id',
    `sort_num`                int(11) NOT NULL COMMENT '排序',
    `parent_id`               bigint(20) NOT NULL COMMENT '父属性id，0为无父属性，其他表示父属性id',
    `active_indicator`        int(1) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NOT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NOT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX                     `idx_template_id`(`template_id`) USING BTREE,
    INDEX                     `idx_created_date`(`created_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE `tb_dfv_template_field_constraint`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `template_id`             bigint(20) NOT NULL COMMENT '模板id',
    `template_field_id`       bigint(20) NOT NULL COMMENT '模板属性id',
    `field_id`                bigint(20) NOT NULL COMMENT '属性id',
    `type`                    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '约束类型',
    `constraint_value1`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '约束条件值1',
    `constraint_value2`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '约束条件值2',
    `field_id_ref`            bigint(20) NOT NULL COMMENT '关联属性：例如A属性的值要大于B属性值的约束时，需要指定B属性的属性id',
    `active_indicator`        int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
    `sort_num`                int(11) NOT NULL COMMENT '排序',
    `created_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NOT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NOT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX                     `idx_created_date`(`created_date`) USING BTREE,
    INDEX                     `idx_template_id`(`template_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE `tb_dict_value`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `system_id`               int(11) NOT NULL COMMENT '归属系统',
    `dict_type`               varchar(100)      DEFAULT NULL COMMENT '字典类型',
    `dict_value`              varchar(500)      DEFAULT NULL COMMENT '字典值',
    `dict_label`              varchar(500)      DEFAULT NULL COMMENT '字典显示名称',
    `active_indicator`        int(1) DEFAULT '1' COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`              varchar(50)       DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime NOT NULL COMMENT '创建时间',
    `modified_by`             varchar(50)       DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime NOT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
    PRIMARY KEY (`id`),
    KEY                       `idx_dict_type` (`system_id`,`dict_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;



ALTER TABLE `tb_trf_customer_contact`
    MODIFY COLUMN `boss_contact_id` varchar (100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `contact_address_id`,
    MODIFY COLUMN `boss_site_use_id` varchar (100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `boss_contact_id`,
    MODIFY COLUMN `contact_usage` tinyint(4) NULL DEFAULT NULL AFTER `boss_site_use_id`,
    MODIFY COLUMN `contact_region_account` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `contact_usage`,
    MODIFY COLUMN `contact_name` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `contact_region_account`,
    ADD COLUMN `responsible_team_code` varchar (100) NULL AFTER `contact_fax`,
    ADD COLUMN `active_indicator` int (1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `responsible_team_code`,
    ADD COLUMN `created_by` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`,
    CHANGE COLUMN `created_time` `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `created_by`,
    ADD COLUMN `modified_by` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`,
    CHANGE COLUMN `modified_time` `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `modified_by`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;


ALTER TABLE `tb_trf_customer`
    MODIFY COLUMN `black_flag` tinyint(4) NULL DEFAULT NULL AFTER `customer_group_code`,
    ADD COLUMN `active_indicator` int (1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `customer_address`,
    ADD COLUMN `created_by` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`,
    ADD COLUMN `modified_by` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`,
    CHANGE COLUMN `created_time` `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `created_by`,
    CHANGE COLUMN `modified_time` `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `modified_by`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;

ALTER TABLE `tb_trf_customer_lang`
    ADD COLUMN `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `customer_address`,
ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`,
    ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`,
    CHANGE COLUMN `created_time` `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `created_by`,
    CHANGE COLUMN `modified_time` `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `modified_by`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;


ALTER TABLE `tb_trf_service_requirement_delivery`
    CHANGE COLUMN `to` `delivery_to` varchar (100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `required`,
    CHANGE COLUMN `others` `delivery_others` varchar (1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `delivery_to`,
    CHANGE COLUMN `cc` `delivery_cc` varchar (1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `delivery_others`,
    CHANGE COLUMN `way` `delivery_way` varchar (100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `delivery_cc`,
    MODIFY COLUMN `required` tinyint(4) NULL DEFAULT NULL AFTER `delivery_type`,
    ADD COLUMN `active_indicator` int (1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `delivery_way`,
    ADD COLUMN `created_by` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`,
    ADD COLUMN `modified_by` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`,
    CHANGE COLUMN `created_time` `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `created_by`,
    CHANGE COLUMN `modified_time` `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `modified_by`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;

ALTER TABLE `tb_trf`
DROP
COLUMN `trf_template_id`,
    MODIFY COLUMN `product_category` varchar(50) NULL DEFAULT NULL COMMENT '1：TX、2：FW' AFTER `service_type`,
    MODIFY COLUMN `source` int(11) NULL DEFAULT NULL COMMENT 'TRF来源：1 Online TRF，2 Order To TRF' AFTER `self_test_flag`,
    MODIFY COLUMN `channel` varchar(100) CHARACTER SET utf8mb4 COLLATE  utf8mb4_general_ci NULL DEFAULT NULL AFTER `source`,
    MODIFY COLUMN `integration_level` varchar(100) CHARACTER SET utf8mb4 COLLATE  utf8mb4_general_ci NULL DEFAULT NULL COMMENT '样品Level，1 客户提供Component  2、SGS 拆样' AFTER `system_id`,
    ADD COLUMN `sample_level` int(11) NULL AFTER `integration_level`,
    CHANGE COLUMN `del_flag` `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `sample_level`,
    ADD COLUMN `bu_id` int(11) NULL AFTER `lab_code`,
    MODIFY COLUMN `trf_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE  utf8mb4_general_ci NULL DEFAULT NULL AFTER `bu_code`,
    MODIFY COLUMN `pending_type` varchar(50) CHARACTER SET utf8mb4 COLLATE  utf8mb4_general_ci NULL DEFAULT NULL AFTER `trf_remark`,
    MODIFY COLUMN `pending_flag` tinyint(4) NULL DEFAULT 0 COMMENT '0: activate, 1: pending' AFTER `pending_type`,
    ADD COLUMN `pending_remark` varchar(1000) NULL COMMENT 'Pending备注' AFTER `pending_flag`,
    MODIFY COLUMN `ext_fields` text CHARACTER SET utf8mb4 COLLATE  utf8mb4_general_ci NULL COMMENT '扩展信息json结构' AFTER `pending_remark`,
    ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `ext_fields`,
    ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`,
    CHANGE COLUMN `created_time` `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `created_by`,
    CHANGE COLUMN `modified_time` `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `modified_by`,
    CHANGE COLUMN `product_line_code` `bu_code` varchar(50) CHARACTER SET utf8mb4 COLLATE  utf8mb4_general_ci NULL DEFAULT NULL AFTER `bu_id`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON
UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间' AFTER `modified_date`;

ALTER TABLE `tb_trf`
    MODIFY COLUMN `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `ext_fields`;


ALTER TABLE `tb_trf_file`
    ADD COLUMN `language_id` int(0) NULL AFTER `file_size`,
    ADD COLUMN `active_indicator` int (1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `language_id`,
    ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`,
    ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`,
    CHANGE COLUMN `created_time` `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间' AFTER `created_by`,
    CHANGE COLUMN `modified_time` `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间' AFTER `modified_by`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;

ALTER TABLE `tb_trf_invoice`
    ADD COLUMN `active_indicator` int (1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `order_no`,
    ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`,
    CHANGE COLUMN `created_time` `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `created_by`,
    ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`,
    CHANGE COLUMN `modified_time` `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `modified_by`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;


drop table tb_trf_lab_contact;
drop table tb_trf_lab;
drop table tb_trf_lab_lang;


ALTER TABLE `tb_trf_order_relationship`
DROP
COLUMN `service_start_date`,
    MODIFY COLUMN `system_id` int (11) NULL DEFAULT NULL COMMENT '归属系统id' AFTER `trf_id`,
    ADD COLUMN `order_status` int (11) NULL AFTER `order_no`,
    ADD COLUMN `bound_status` int (11) NOT NULL DEFAULT 0 COMMENT 'Trf绑定状态（1：已绑定、2：已解绑）' AFTER `order_status`,
    ADD COLUMN `active_indicator` int (1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `bound_status`,
    ADD COLUMN `created_by` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`,
    CHANGE COLUMN `created_time` `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间' AFTER `created_by`,
    ADD COLUMN `modified_by` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`,
    CHANGE COLUMN `modified_time` `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间' AFTER `modified_by`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`,
    ADD INDEX `idx_order_no`(`system_id`, `order_no`, `bound_status`) USING BTREE;

RENAME
table tb_trf_order_relationship TO tb_trf_order;

drop table tb_trf_order_contact;


ALTER TABLE `tb_trf_product` DROP COLUMN `language_id`,
DROP
COLUMN `product_sample_id`,
ADD COLUMN `active_indicator` INT ( 1 ) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `template_id`,
ADD COLUMN `created_by` VARCHAR ( 50 ) CHARACTER
SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `created_date`,
CHANGE COLUMN `created_time` `created_date` datetime ( 0 ) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `active_indicator`,
ADD COLUMN `modified_by` VARCHAR ( 50 ) CHARACTER
SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `modified_date`,
CHANGE COLUMN `modified_time` `modified_date` datetime ( 0 ) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `created_by`,
ADD COLUMN `last_modified_timestamp` datetime ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ON
UPDATE CURRENT_TIMESTAMP ( 0 ) COMMENT '最近修改时间' AFTER `modified_by`,
    ADD COLUMN `object_type` TINYINT ( 4 ) NOT NULL COMMENT '对象类型：1 Product,2 Sample' AFTER `template_id`,
DROP INDEX `idx_template_id`;

ALTER TABLE `tb_trf_product_attr`
    ADD COLUMN `trf_id` bigint(20) NULL AFTER `id`,
    ADD COLUMN `trf_no` varchar(100) NULL AFTER `trf_id`,
    ADD COLUMN `product_instance_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `trf_product_id`,
    ADD COLUMN `template_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `product_instance_id`,
    ADD COLUMN `object_type` tinyint(4) NOT NULL COMMENT '对象类型：1 Product,2 Sample' AFTER `template_id`,
    ADD COLUMN `language_id` int(11) NULL DEFAULT NULL AFTER `object_type`,
    CHANGE COLUMN `seq` `attr_seq` int(11) NULL DEFAULT NULL AFTER `language_id`,
    CHANGE COLUMN `value` `label_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `label_name`,
    ADD COLUMN `active_indicator` int(1) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `data_type`,
    ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`,
    CHANGE COLUMN `created_time` `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间' AFTER `created_by`,
    ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`,
    CHANGE COLUMN `modified_time` `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间' AFTER `modified_by`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`,
    ADD INDEX `idx_trf_no`(`trf_no`) USING BTREE;


drop table tb_trf_product_sample;
drop table tb_trf_sample;
drop table tb_trf_sample_attr;

ALTER TABLE `tb_trf_quotation`
DROP
COLUMN `service_type`,
DROP
COLUMN `net_amount`,
DROP
COLUMN `total_amount`,
DROP
COLUMN `currency`,
DROP
COLUMN `vat_amount`,
DROP
COLUMN `discount`,
DROP
COLUMN `adjustment_amount`,
DROP
COLUMN `final_amount`,
DROP
COLUMN `main_currency_total_amount`,
DROP
COLUMN `free_quotation_flag`,
DROP
COLUMN `quotation_status`,
DROP
COLUMN `quotation_version_id`,
ADD COLUMN `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `order_no`,
ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`,
CHANGE COLUMN `created_time` `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间' AFTER `created_by`,
ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`,
CHANGE COLUMN `modified_time` `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间' AFTER `modified_by`,
ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;


ALTER TABLE `tb_trf_report`
DROP
COLUMN `actual_report_no`,
DROP
COLUMN `seal_code`,
DROP
COLUMN `report_due_date`,
DROP
COLUMN `approve_date`,
DROP
COLUMN `soft_copy_delivery_date`,
DROP
COLUMN `parent_report_no`,
DROP
COLUMN `report_type`,
DROP
COLUMN `report_approve_date`,
DROP
COLUMN `external_report_no`,
ADD COLUMN `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `report_status`,
ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`,
CHANGE COLUMN `created_time` `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间' AFTER `created_by`,
ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`,
CHANGE COLUMN `modified_time` `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间' AFTER `modified_by`,
ADD COLUMN `system_id` int(11) NULL DEFAULT NULL COMMENT '调用系统id' AFTER `trf_id`,
ADD COLUMN `ref_system_id` int(11) NULL DEFAULT NULL AFTER `system_id`,
ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;

ALTER TABLE `tb_trf_service_requirement`
    ADD COLUMN `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `other_request_remark`,
    CHANGE COLUMN `invoice_vat_type` `invoice_type` tinyint(4) NULL DEFAULT NULL AFTER `need_photo`,
    ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`,
    CHANGE COLUMN `created_time` `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间' AFTER `created_by`,
    ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`,
    CHANGE COLUMN `modified_time` `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间' AFTER `modified_by`;

ALTER TABLE `tb_trf_service_requirement`
    MODIFY COLUMN `sample_save_duration` int (11) NULL DEFAULT NULL AFTER `invoice_type`;

ALTER TABLE `tb_trf_service_requirement_delivery`
    ADD COLUMN `trf_service_requirement_id` bigint(20) NULL DEFAULT NULL AFTER `trf_id`,
ADD INDEX `idx_service_requirement_id`(`trf_service_requirement_id`) USING BTREE;


ALTER TABLE `tb_trf_service_requirement_lang`
DROP
COLUMN `report_language`,
ADD COLUMN `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `report_address`,
ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`,
CHANGE COLUMN `created_time` `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间' AFTER `created_by`,
ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`,
CHANGE COLUMN `modified_time` `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间' AFTER `modified_by`,
ADD COLUMN `trf_id` bigint(20) NULL DEFAULT NULL AFTER `id`,
ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`,
    ADD INDEX `idx_trf_id`(`trf_id`) USING BTREE;


ALTER TABLE `tb_trf_test_item` CHARACTER SET = utf8mb4, COLLATE = utf8mb4_general_ci;

ALTER TABLE `tb_trf_test_item`
    ADD COLUMN `evaluation_alias` varchar(255) NULL AFTER `test_line_id`,
    CHANGE COLUMN `test_line_name` `evaluation_name` varchar (255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `evaluation_alias`,
    CHANGE COLUMN `citation_name` `citation_full_name` varchar (100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `citation_type`,
    CHANGE COLUMN `external_test_standard_id` `external_test_citation_id` varchar (100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `external_test_item_name`,
    CHANGE COLUMN `external_test_standard_name` `external_test_citation_name` varchar (100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `external_test_citation_id`,
    ADD COLUMN `external_check_type` varchar (100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `external_test_citation_name`,
    ADD COLUMN `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `external_check_type`,
    ADD COLUMN `created_by` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`,
    CHANGE COLUMN `created_time` `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间' AFTER `created_by`,
    ADD COLUMN `modified_by` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`,
    CHANGE COLUMN `modified_time` `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间' AFTER `modified_by`,
    MODIFY COLUMN `test_line_id` int (11) NULL DEFAULT NULL AFTER `trf_id`,
    ADD COLUMN `test_line_seq` int (11) NULL AFTER `evaluation_name`,
    MODIFY COLUMN `pp_no` int (11) NULL DEFAULT NULL AFTER `test_line_seq`,
    ADD COLUMN `pp_name` varchar (255) NULL AFTER `pp_no`,
    MODIFY COLUMN `citation_id` int (11) NULL DEFAULT NULL AFTER `pp_name`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;


CREATE TABLE `tb_trf_test_item_lang`
(
    `id`                          bigint(20) NOT NULL AUTO_INCREMENT,
    `trf_id`                      bigint(20) DEFAULT NULL,
    `trf_test_item_id`            bigint(20) DEFAULT NULL,
    `language_id`                 int(11) DEFAULT NULL,
    `evaluation_alias`            varchar(255)      DEFAULT NULL,
    `evaluation_name`             varchar(255)      DEFAULT NULL,
    `pp_name`                     varchar(255)      DEFAULT NULL,
    `citation_full_name`          varchar(100)      DEFAULT NULL,
    `external_test_item_name`     varchar(100)      DEFAULT NULL,
    `external_test_citation_name` varchar(100)      DEFAULT NULL,
    `active_indicator`            tinyint(4) DEFAULT '1' COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`                  varchar(50)       DEFAULT NULL COMMENT '创建人',
    `created_date`                datetime          DEFAULT NULL COMMENT '创建时间',
    `modified_by`                 varchar(50)       DEFAULT NULL COMMENT '修改人',
    `modified_date`               datetime          DEFAULT NULL COMMENT '修改时间',
    `last_modified_timestamp`     datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                           `idx_created_time` (`created_date`) USING BTREE,
    KEY                           `idx_trf_id` (`trf_id`) USING BTREE,
    KEY                           `idx_trf_test_item_id` (`trf_test_item_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;


ALTER TABLE `tb_trf`
    MODIFY COLUMN `trf_no` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Trf 编号' AFTER `id`,
    MODIFY COLUMN `integration_level` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '样品Level，1 客户提供Component  2、SGS 拆样' AFTER `system_id`,
    MODIFY COLUMN `trf_remark` varchar (1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `bu_code`,
    MODIFY COLUMN `ext_fields` json NULL COMMENT '扩展信息json结构' AFTER `pending_remark`,
    CHARACTER SET = utf8mb4, COLLATE = utf8mb4_general_ci;


ALTER TABLE `tb_trf_customer`
    ADD COLUMN `customer_instance_id` varchar(50) NULL AFTER `trf_id`,
    MODIFY COLUMN `customer_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `customer_name`,
    MODIFY COLUMN `payment_term` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `customer_address`,
    MODIFY COLUMN `black_flag` tinyint(4) NULL DEFAULT NULL AFTER `payment_term`,
    CHARACTER SET = utf8mb4, COLLATE = utf8mb4_general_ci;

ALTER TABLE `tb_trf_customer_contact`
    MODIFY COLUMN `boss_contact_id` bigint(20) NULL DEFAULT NULL AFTER `contact_address_id`,
    MODIFY COLUMN `boss_site_use_id` bigint(20) NULL DEFAULT NULL AFTER `boss_contact_id`,
    MODIFY COLUMN `contact_name` varchar (200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `contact_region_account`,
    MODIFY COLUMN `contact_email` varchar (300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `contact_name`,
    MODIFY COLUMN `contact_fax` varchar (128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `contact_telephone`,
    CHARACTER SET = utf8mb4, COLLATE = utf8mb4_general_ci;

ALTER TABLE `tb_trf_product`
    MODIFY COLUMN `template_id` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'dff form id' AFTER `trf_id`;

ALTER TABLE `tb_trf_product_attr`
    MODIFY COLUMN `label_code` varchar (200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `attr_seq`,
    MODIFY COLUMN `label_name` varchar (200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `label_code`,
    MODIFY COLUMN `label_value` varchar (5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `label_name`,
    MODIFY COLUMN `customer_label` varchar (200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `field_code`,
    MODIFY COLUMN `data_type` varchar (200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `customer_label`;


ALTER TABLE `tb_trf_care_label`
    MODIFY COLUMN `test_sample_ids` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL AFTER `trf_id`,
    MODIFY COLUMN `care_label_seq` INT ( 4 ) NULL DEFAULT NULL AFTER `select_country`,
    MODIFY COLUMN `cloud_id` VARCHAR ( 500 ) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL AFTER `select_img_ids`,
    MODIFY COLUMN `care_label_file_path` VARCHAR ( 500 ) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL AFTER `cloud_id`,
    MODIFY COLUMN `product_item_no` VARCHAR ( 500 ) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL AFTER `care_label_file_path`,
    ADD COLUMN `active_indicator` TINYINT ( 4 ) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `product_item_no`,
    ADD COLUMN `created_by` VARCHAR ( 50 ) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`,
    CHANGE COLUMN `created_time` `created_date` datetime ( 0 ) NULL DEFAULT NULL COMMENT '创建时间' AFTER `created_by`,
    ADD COLUMN `modified_by` VARCHAR ( 50 ) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`,
    CHANGE COLUMN `modified_time` `modified_date` datetime ( 0 ) NULL DEFAULT NULL COMMENT '修改时间' AFTER `modified_by`,
    ADD COLUMN `last_modified_timestamp` datetime ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ON
UPDATE CURRENT_TIMESTAMP ( 0 ) COMMENT '最近修改时间' AFTER `modified_date`,
    CHARACTER
SET = utf8mb4, COLLATE = utf8mb4_general_ci;


CREATE TABLE `tb_trf_test_sample`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT,
    `trf_id`                  bigint(20) DEFAULT NULL,
    `test_sample_instance_id` varchar(50)       DEFAULT NULL,
    `parent_test_sample_id`   varchar(50)       DEFAULT NULL,
    `test_sample_no`          varchar(50)       DEFAULT NULL,
    `external_sample_no`      varchar(100)      DEFAULT NULL,
    `test_sample_type`        int(11) DEFAULT NULL,
    `test_sample_seq`         int(11) DEFAULT NULL,
    `active_indicator`        tinyint(4) DEFAULT '1' COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`              varchar(50)       DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime          DEFAULT NULL COMMENT '创建时间',
    `modified_by`             varchar(50)       DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime          DEFAULT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                       `idx_created_time` (`created_date`) USING BTREE,
    KEY                       `idx_trf_id` (`trf_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `tb_trf_test_sample_material`
(
    `id`                         bigint(20) NOT NULL AUTO_INCREMENT,
    `trf_id`                     bigint(20) DEFAULT NULL,
    `material_composition`       varchar(500)      DEFAULT NULL,
    `material_description`       varchar(500)      DEFAULT NULL,
    `material_end_use`           varchar(500)      DEFAULT NULL,
    `material_name`              varchar(500)      DEFAULT NULL,
    `material_color`             varchar(500)      DEFAULT NULL,
    `material_texture`           varchar(500)      DEFAULT NULL,
    `material_other_sample_info` text,
    `material_applicable_flag`   tinyint(4) DEFAULT NULL,
    `material_sample_remark`     varchar(500)      DEFAULT NULL,
    `material_category`          varchar(500)      DEFAULT NULL,
    `material_sku`               varchar(500)      DEFAULT NULL,
    `material_item`              varchar(500)      DEFAULT NULL,
    `active_indicator`           tinyint(4) DEFAULT '1' COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`                 varchar(50)       DEFAULT NULL COMMENT '创建人',
    `created_date`               datetime          DEFAULT NULL COMMENT '创建时间',
    `modified_by`                varchar(50)       DEFAULT NULL COMMENT '修改人',
    `modified_date`              datetime          DEFAULT NULL COMMENT '修改时间',
    `last_modified_timestamp`    datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                          `idx_created_time` (`created_date`) USING BTREE,
    KEY                          `idx_trf_id` (`trf_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

ALTER TABLE `tb_trf_test_sample_material`
    ADD COLUMN `trf_test_sample_id` bigint(20) NULL AFTER `trf_id`,
ADD INDEX `idx_trf_test_sample_id`(`trf_test_sample_id`) USING BTREE;

CREATE TABLE `tb_trf_test_sample_file`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT,
    `trf_id`                  bigint(20) DEFAULT NULL,
    `trf_test_sample_id`      bigint(20) DEFAULT NULL,
    `file_name`               varchar(100)      DEFAULT NULL,
    `file_path`               varchar(255)      DEFAULT NULL,
    `file_type`               tinyint(4) DEFAULT NULL,
    `file_size`               bigint(20) DEFAULT NULL,
    `language_id`             int(11) DEFAULT NULL,
    `created_by`              varchar(50)       DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime          DEFAULT NULL COMMENT '创建时间',
    `modified_by`             varchar(50)       DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime          DEFAULT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                       `idx_created_time` (`created_date`) USING BTREE,
    KEY                       `idx_trf_id` (`trf_id`) USING BTREE,
    KEY                       `idx_trf_test_sample_id` (`trf_test_sample_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `tb_trf_test_sample_group`
(
    `id`                      BIGINT ( 20 ) NOT NULL AUTO_INCREMENT,
    `trf_id`                  BIGINT ( 20 ) DEFAULT NULL,
    `trf_test_sample_id`      BIGINT ( 20 ) DEFAULT NULL,
    `test_sample_instance_id` VARCHAR(50)       DEFAULT NULL COMMENT '测试样实例id',
    `main_sample_flag`        TINYINT ( 4 ) DEFAULT '0',
    `active_indicator`        TINYINT ( 4 ) DEFAULT '1' COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`              VARCHAR(50)       DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime          DEFAULT NULL COMMENT '创建时间',
    `modified_by`             VARCHAR(50)       DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime          DEFAULT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                       `idx_created_time` ( `created_date` ) USING BTREE,
    KEY                       `idx_trf_id` ( `trf_id` ) USING BTREE,
    KEY                       `idx_trf_test_sample_id` ( `trf_test_sample_id` ) USING BTREE
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4;

drop table tb_trf_attr_mapping;
drop table tb_trf_care_label_sample;

RENAME
table tb_trf_file TO tb_trf_attachment;


ALTER TABLE `tb_trf_service_requirement`
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;

ALTER TABLE `tb_trf_customer_contact`
DROP
COLUMN `contact_id`;

ALTER TABLE `tb_trf_customer_contact`
    MODIFY COLUMN `boss_contact_id` bigint(20) NULL DEFAULT NULL AFTER `contact_address_id`,
    MODIFY COLUMN `boss_site_use_id` bigint(20) NULL DEFAULT NULL AFTER `boss_contact_id`;


ALTER TABLE `tb_trf_customer_contact`
    ADD COLUMN `trf_id` bigint(20) NULL DEFAULT NULL AFTER `id`,
ADD INDEX `idx_trf_id`(`trf_id`) USING BTREE;
ALTER TABLE `tb_trf_customer_lang`
    ADD COLUMN `trf_id` bigint(20) NULL DEFAULT NULL AFTER `id`,
ADD INDEX `idx_trf_id`(`trf_id`) USING BTREE;

ALTER TABLE `tb_trf_attachment`
    ADD COLUMN `trf_id` bigint(20) NULL DEFAULT NULL AFTER `id`,
ADD INDEX `idx_trf_id`(`trf_id`) USING BTREE;

ALTER TABLE `tb_trf_attachment`
    MODIFY COLUMN `file_type` int (4) NULL DEFAULT NULL AFTER `file_path`;

ALTER TABLE `tb_trf_test_sample_file`
    MODIFY COLUMN `file_type` tinyint(11) NULL DEFAULT NULL AFTER `file_path`;

ALTER TABLE `tb_trf_report`
DROP
COLUMN `ref_system_id`;

ALTER TABLE `tb_trf_quotation`
    ADD COLUMN `system_id` int(11) NULL DEFAULT NULL COMMENT '调用系统id' AFTER `trf_id`;

ALTER TABLE `tb_trf_invoice`
    ADD COLUMN `system_id` int(11) NULL DEFAULT NULL COMMENT '调用系统id' AFTER `trf_id`;

ALTER TABLE `tb_trf_report`
DROP
COLUMN `report_status`;

ALTER TABLE `tb_trf_invoice`
    ADD COLUMN `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `order_no`;

ALTER TABLE `tb_trf_test_item`
    MODIFY COLUMN `external_check_type` int(11) NULL DEFAULT NULL AFTER `external_test_citation_name`;

SET
FOREIGN_KEY_CHECKS = 1;


