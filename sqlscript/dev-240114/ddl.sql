CREATE TABLE `tb_cfg_dff_mapping` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID唯一标识',
                                      `bu_code` varchar(50) DEFAULT NULL COMMENT 'BU Code',
                                      `customer_group_code` varchar(50) DEFAULT NULL COMMENT '客户组代码',
                                      `customer_no` varchar(32) DEFAULT NULL COMMENT '客户编码',
                                      `ref_system_id` int(11) DEFAULT NULL COMMENT '客户系统编号',
                                      `type` varchar(32) NOT NULL COMMENT 'DFF 字段适用类型',
                                      `field_code` varchar(128) DEFAULT NULL,
                                      `label_code` varchar(128) NOT NULL COMMENT '标准字段属性名',
                                      `label_name` varchar(50) NOT NULL COMMENT '标准字段属性显示名称',
                                      `customer_label` varchar(128) NOT NULL COMMENT '客户侧字段属性显示名称',
                                      `customer_field` varchar(128) NOT NULL COMMENT '客户侧字段属性名',
                                      `seq` int(11) NOT NULL DEFAULT '0' COMMENT '属性排序',
                                      `active_indicator` tinyint(4) NOT NULL DEFAULT '1' COMMENT '有效无效标记：0 - inactive， 1 - active',
                                      `created_by` varchar(50) DEFAULT 'system' COMMENT '创建人',
                                      `created_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `modified_by` varchar(50) DEFAULT 'system' COMMENT '修改人',
                                      `modified_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                      `last_modified_timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB;
