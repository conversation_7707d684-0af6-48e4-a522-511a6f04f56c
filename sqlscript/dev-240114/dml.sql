INSERT INTO `tb_cfg_system_api`(`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`)
VALUES (30, 10018, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{\r\n    \"action\":\"SyncReport\",\r\n    \"dataStatus\":\"Completed\",\r\n    \"bu\":\"$.extra.productLineCode\",\r\n    \"objectNumber\":\"$.trfList[0].trfNo\",\r\n    \"refSystemId\":10018,\r\n    \"msgId\":\"$.extra.randomSequence\",\r\n    \"body\":{\r\n        \"reportPdfUrl\":\"$.reportList[*].reportFileList[0].cloudId\",\r\n        \"reportFileName\":\"#join(\' \',$.reportList[*].reportNo,\'_\',$.trfList[0].trfNo,\'-\',$.trfList[0].extFields.trfVersionNumber,\'_\',$.trfList[0].extFields.labShortName)\",\r\n        \"referenceId\":\"$.trfList[0].trfNo\",\r\n        \"labCompany\":\"SGS\",\r\n        \"labContact\":\"$.orderList[0].contactPersonList[0].contactName\",\r\n        \"reportNumber\":\"$.reportList[*].reportNo\",\r\n        \"dateIn\":\"$.orderList[0].testingStartDate\",\r\n        \"dateOut\":\"$.orderList[0].softCopyDeliveryDate\",\r\n        \"revisedReport\":\"#lowsGetReviseInfoFn(revisedReport,$.reportList[*].originalReportNo,$.orderList[0].softCopyDeliveryDate)\",\r\n        \"reportVersionNumber\":\"#sizeFn($.reportList)\",\r\n        \"trfVersionNumber\":\"$.trfList[0].extFields.trfVersionNumber\",\r\n        \"revisedDate\":\"#lowsGetReviseInfoFn(revisedDate,$.reportList[*].originalReportNo,$.orderList[0].softCopyDeliveryDate)\",\r\n        \"revisedCode\":\"$.orderList[0].productList[0].productAttrList[labelCode=\'revisedCode\'][0].value\",\r\n        \"usRating\":\"$.orderList[0].productList[0].productAttrList[labelCode=\'usRating\'][0].value\",\r\n        \"canadaRating\":\"$.orderList[0].productList[0].productAttrList[labelCode=\'canadaRating\'][0].value\",\r\n        \"itemClass\":\"$.orderList[0].productList[0].productAttrList[labelCode=\'itemClass\'][0].value\",\r\n        \"protocolUsed\":\"$.orderList[0].productList[0].productAttrList[labelCode=\'protocolUsed\'][0].value\",\r\n        \"GCC\":\"$.orderList[0].productList[0].productAttrList[labelCode=\'GCC\'][0].value\",\r\n        \"toyChild\":\"$.orderList[0].productList[0].productAttrList[labelCode=\'toyChild\'][0].value\",\r\n        \"energyStar\":\"$.orderList[0].productList[0].productAttrList[labelCode=\'energyStar\'][0].value\",\r\n        \"containsWood\":\"$.orderList[0].productList[0].productAttrList[labelCode=\'containsWood\'][0].value\",\r\n        \"nrtlListed\":\"$.orderList[0].productList[0].productAttrList[labelCode=\'nrtlListed\'][0].value\",\r\n        \"pfasTesting\":\"$.orderList[0].productList[0].productAttrList[labelCode=\'pfasTesting\'][0].value\",\r\n        \"totalFlourinePPM\":\"$.orderList[0].productList[0].productAttrList[labelCode=\'totalFlourinePPM\'][0].value\",\r\n        \"pfasIntentionallyAdded\":\"$.orderList[0].productList[0].productAttrList[labelCode=\'pfasIntentionallyAdded\'][0].value\",\r\n        \"testCost\":\"$.orderList[0].productList[0].productAttrList[labelCode=\'testCost\'][0].value\",\r\n        \"sellingCarton\":{\r\n            \"length\":{\r\n                \"value\":\"#getResultValueByTypeFn($.reportList[*],$.testResultList,$.testLineList,sellingCarton,length,$.extra.productLineCode)\",\r\n                \"uom\":\"#getUomFn(@.value,length)\"\r\n            },\r\n            \"width\":{\r\n                \"value\":\"#getResultValueByTypeFn($.reportList[*],$.testResultList,$.testLineList,sellingCarton,width,$.extra.productLineCode)\",\r\n                \"uom\":\"#getUomFn(@.value,width)\"\r\n            },\r\n            \"height\":{\r\n                \"value\":\"#getResultValueByTypeFn($.reportList[*],$.testResultList,$.testLineList,sellingCarton,height,$.extra.productLineCode)\",\r\n                \"uom\":\"#getUomFn(@.value,height)\"\r\n            },\r\n            \"weight\":{\r\n                \"value\":\"#getResultValueByTypeFn($.reportList[*],$.testResultList,$.testLineList,sellingCarton,weight,$.extra.productLineCode)\",\r\n                \"uom\":\"#getUomFn(@.value,weight)\"\r\n            }\r\n        },\r\n        \"masterCarton\":{\r\n            \"length\":{\r\n                \"value\":\"#getResultValueByTypeFn($.reportList[*],$.testResultList,$.testLineList,masterCarton,length,$.extra.productLineCode)\",\r\n                \"uom\":\"#getUomFn(@.value,length)\"\r\n            },\r\n            \"width\":{\r\n                \"value\":\"#getResultValueByTypeFn($.reportList[*],$.testResultList,$.testLineList,masterCarton,width,$.extra.productLineCode)\",\r\n                \"uom\":\"#getUomFn(@.value,width)\"\r\n            },\r\n            \"height\":{\r\n                \"value\":\"#getResultValueByTypeFn($.reportList[*],$.testResultList,$.testLineList,masterCarton,height,$.extra.productLineCode)\",\r\n                \"uom\":\"#getUomFn(@.value,height)\"\r\n            },\r\n            \"weight\":{\r\n                \"value\":\"#getResultValueByTypeFn($.reportList[*],$.testResultList,$.testLineList,masterCarton,weight,$.extra.productLineCode)\",\r\n                \"uom\":\"#getUomFn(@.value,weight)\"\r\n            }\r\n        },\r\n        \"palletCarton\":{\r\n            \"length\":{\r\n                \"value\":\"#getResultValueByTypeFn($.reportList[*],$.testResultList,$.testLineList,palletCarton,length,$.extra.productLineCode)\",\r\n                \"uom\":\"#getUomFn(@.value,length)\"\r\n            },\r\n            \"width\":{\r\n                \"value\":\"#getResultValueByTypeFn($.reportList[*],$.testResultList,$.testLineList,palletCarton,width,$.extra.productLineCode)\",\r\n                \"uom\":\"#getUomFn(@.value,width)\"\r\n            },\r\n            \"height\":{\r\n                \"value\":\"#getResultValueByTypeFn($.reportList[*],$.testResultList,$.testLineList,palletCarton,height,$.extra.productLineCode)\",\r\n                \"uom\":\"#getUomFn(@.value,height)\"\r\n            },\r\n            \"weight\":{\r\n                \"value\":\"#getResultValueByTypeFn($.reportList[*],$.testResultList,$.testLineList,palletCarton,weight,$.extra.productLineCode)\",\r\n                \"uom\":\"#getUomFn(@.value,weight)\"\r\n            }\r\n        }\r\n    }\r\n}', '{}', 'Low\'s Completed', 'XQW', current_timestamp, 'XQW', current_timestamp, current_timestamp);

INSERT INTO `tb_cfg_event_subscribe`(`id`, `subscriber`, `ref_system_id`, `event_code`, `api_id`, `priority`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`)
    VALUES (53, 10018, 10018, 60, 30, 0, 0, 2023, 'messageNotifyHandler', '', 'XQW', current_timestamp, 'XQW', current_timestamp, current_timestamp);

UPDATE `tb_cfg_info` SET `csutomer_group` = NULL, `customer_no` = NULL, `identity_id` = '10018', `product_line` = 'HL', `config_type` = 2, `config_key` = 'REFSYSTEM.API.IMPORT', `config_value` = '29', `created_by` = 'system', `created_date` = '2023-12-18 21:49:49', `modified_by` = 'system', `modified_date` = '2023-12-18 21:49:49', `last_modified_timestamp` = '2024-01-10 15:58:22' WHERE `id` = 27;


INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (34, NULL, NULL, '10018', 'HL', 2, 'TrfStatusControl', '{\n     \"trfOrderRelationshipRule\": 4,\n     \"statusRule\": 4,\n     \"statusFlow\": [\n         1,\n         4,\n         6,\n         100\n     ],\n     \"statusMapping\": {\n         \"1\": 1,\n         \"4\": 4,\n         \"6\": 6,\n         \"100\": 100\n     },\n     \"actionStatusMapping\": {\n         \"Import\": {\n             \"orderRefRule\": 0,\n             \"orderOpType\": 1,\n             \"ctrlMapping\": {}\n         },\n        \n         \"SyncConfirmed\": {\n             \"orderRefRule\": 1,\n             \"orderOpType\": 1,\n             \"ctrlMapping\": {\n                 \"4\": 0\n             }\n         },\n         \n         \"SyncCompleted\": {\n             \"orderRefRule\": 1,\n             \"orderOpType\": 7,\n             \"ctrlMapping\": {\n                 \"6\": 0\n             }\n         },\n        \n         \"SyncReviseReport\": {\n             \"orderOpType\": 7,\n             \"ctrlMapping\": {\n                 \"100\": 0,\n 		\"6\":0\n             }\n         }\n         \n     }\n }', 'system', '2024-01-10 16:03:33', 'system', '2024-01-10 16:03:39', '2024-01-10 16:04:05');
INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (35, NULL, NULL, '10018', 'HL', 2, 'DeliveryReportMode', '2', 'system', '2024-01-10 16:06:30', 'system', '2024-01-10 16:06:36', '2024-01-10 16:06:36');

UPDATE `tb_cfg_system_api` SET `system_id` = 10002, `protocol_type` = 2, `request_url` = 'com.sgs.extsystem.sync.order.forILayer', `request_method` = 2, `request_body_template` = '{\r\n    \"action\":\"OrderToTrf\",\r\n    \"dataStatus\":\"New\",\r\n    \"bu\":\"SL\",\r\n    \"objectNumber\":\"$.trfList[0].trfNo\",\r\n    \"refSystemId\":2,\r\n    \"msgId\":\"$.extra.randomSequence\",\r\n    \"body\":{\r\n        \"trfTemplateId\":null,\r\n        \"templateName\":null,\r\n        \"productLineId\":null,\r\n        \"productLineCode\":\"$.trfList[0].lab.buCode\",\r\n        \"remark\":\"$.trfList[0].others.trfRemark\",\r\n        \"orderNo\":\"\",\r\n        \"serviceType\":\"$.trfList[0].serviceType\",\r\n        \"serviceTypeName\":\"$.trfList[0].serviceType\",\r\n        \"reportDeliveredTo\":\"$.trfList[0].serviceRequirement.report.softcopy.deliveryTo\",\r\n        \"failedReportDeliveredTo\":\"$.trfList[0].serviceRequirement.report.softcopy.deliveryTo\",\r\n        \"applicantUser\":\"$.trfList[0].lab.labContact.contactName\",\r\n        \"testPackageAllIds\":\"\",\r\n        \"trfType\":\"10\",\r\n        \"customerReferenceNo\":\"$.trfList[0].trfNo\",\r\n        \"isSelfRefrence\":0,\r\n        \"trfSourceId\":null,\r\n        \"trfSourceType\":\"4\",\r\n        \"isGeneralRequest\":0,\r\n        \"dffFormId\":null,\r\n        \"dffGridId\":null,\r\n        \"materialIds\":null,\r\n        \"actionType\":\"add\",\r\n        \"dffFormData\":null,\r\n        \"dffGridData\":null,\r\n        \"careLabelData\":null,\r\n        \"testPackageIds\":null,\r\n        \"trfCustomer\":{\r\n            \"buyerCustomerId\":\"#toStringFn($.customerList[customerUsage=3][0].customerId)\",\r\n						\"buyerCustomerNo\":\"#toStringFn($.customerList[customerUsage=3][0].bossNo)\",\r\n            \"buyerAccountId\":\"#toStringFn($.customerList[customerUsage=3][0].customerContactList[0].customerContactId)\",\r\n            \"buyerCustomerNameCn\":\"#toStringFn($.customerList[customerUsage=3].languageList[language=2].customerName)\",\r\n            \"buyerCustomerNameEn\":\"#toStringFn($.customerList[customerUsage=3][0].customerName)\",\r\n            \"buyerCustomerGroupId\":null,\r\n            \"buyerCustomerGroupCode\":\"#toStringFn($.customerList[customerUsage=3][0].customerGroupCode)\",\r\n            \"buyerCustomerGroupName\":\"#toStringFn($.customerList[customerUsage=3][0].customerName)\",\r\n            \"agentCustomerId\":\"#toStringFn($.customerList[customerUsage=4][0].customerId)\",\r\n            \"agentAccountId\":\"#toStringFn($.customerList[customerUsage=4][0].customerContactList[0].customerContactId)\",\r\n            \"agentCustomerNameCn\":\"#toStringFn($.customerList[customerUsage=4].languageList[languageId=2].customerName)\",\r\n            \"agentCustomerNameEn\":\"#toStringFn($.customerList[customerUsage=4][0].customerName)\",\r\n            \"agentCustomerGroupId\":null,\r\n            \"agentCustomerGroupCode\":\"#toStringFn($.customerList[customerUsage=4][0].customerGroupCode)\",\r\n            \"agentCustomerGroupName\":\"#toStringFn($.customerList[customerUsage=4][0].customerName)\",\r\n            \"sgsCustomerId\":null,\r\n            \"sgsAccountId\":null,\r\n            \"customerId\":\"$.customerList[customerUsage=1][0].customerId\",\r\n            \"customerNo\":\"#toStringFn($.customerList[customerUsage=1][0].bossNo)\",\r\n            \"trfCustomerRole\":null,\r\n            \"customerAddressId\":null,\r\n            \"customerNameZh\":\"#toStringFn($.customerList[customerUsage=1][0].languageList[languageId=2].customerName)\",\r\n            \"customerNameEn\":\"#toStringFn($.customerList[customerUsage=1][0].customerName)\",\r\n            \"customerAddressZh\":\"#toStringFn($.customerList[customerUsage=1][0].languageList[languageId=2].customerAddress)\",\r\n            \"customerAddressEn\":\"#toStringFn($.customerList[customerUsage=1][0].customerName)\",\r\n            \"payCustomerNameZh\":\"#toStringFn($.customerList[customerUsage=2][0].languageList[languageId=2].customerName)\",\r\n            \"payCustomerNameEn\":\"#toStringFn($.customerList[customerUsage=2][0].customerName)\",\r\n            \"payCustomerAddressZh\":\"#toStringFn($.customerList[customerUsage=2][0].languageList[languageId=2].customerAddress)\",\r\n            \"payCustomerAddressEn\":\"#toStringFn($.customerList[customerUsage=2][0].customerName)\",\r\n            \"isSame\":\"1\",\r\n            \"beLongId\":null\r\n        },\r\n        \"trfCustomerContact\":{\r\n            \"applyContactName\":\"$.customerList[customerUsage=1].customerContactList[0].contactName\",\r\n            \"applyContactTel\":\"$.customerList[customerUsage=1].customerContactList[0].contactTelephone\",\r\n            \"applyContactEmail\":\"$.customerList[customerUsage=1].customerContactList[0].contactEmail\",\r\n            \"applyContactFax\":\"$.customerList[customerUsage=1].customerContactList[0].contactFax\",\r\n            \"payContactName\":\"$.customerList[customerUsage=2].customerContactList[0].contactName\",\r\n            \"payContactTel\":\"$.customerList[customerUsage=2].customerContactList[0].contactTelephone\",\r\n            \"payContactEmail\":\"$.customerList[customerUsage=2].customerContactList[0].contactEmail\",\r\n            \"payContactFax\":\"$.customerList[customerUsage=2].customerContactList[0].contactFax\",\r\n            \"isSame\":\"1\"\r\n        },\r\n        \"invoice\":{\r\n            \"invoiceType\":\"$.trfList[0].serviceRequirement.invoice.invoiceType\",\r\n            \"invoiceTypeName\":null,\r\n            \"invoiceTitle\":\"$.trfList[0].serviceRequirement.invoice.invoiceTitle\",\r\n            \"taxNo\":\"$.trfList[0].serviceRequirement.invoice.taxNo\",\r\n            \"invoiceTel\":\"$.trfList[0].serviceRequirement.invoice.registerPhone\",\r\n            \"invoiceAddress\":\"$.trfList[0].serviceRequirement.invoice.registerAddr\",\r\n            \"bankName\":\"$.trfList[0].serviceRequirement.invoice.bankName\",\r\n            \"bankAccount\":\"$.trfList[0].serviceRequirement.invoice.bankNumber\"\r\n        },\r\n        \"trfLab\":{\r\n            \"labCode\":\"$.trfList[0].lab.labCode\",\r\n            \"labName\":null,\r\n            \"labAddress\":null,\r\n            \"locationId\":null,\r\n            \"locationCode\":null,\r\n            \"locationName\":null,\r\n            \"countryCode\":null,\r\n            \"countryName\":null,\r\n            \"countryId\":null\r\n        },\r\n        \"trfLabContact\":{\r\n            \"labCode\":\"$.trfList[0].lab.labCode\",\r\n            \"labName\":null,\r\n            \"labAddress\":null,\r\n            \"contactName\":\"$.trfList[0].lab.labContact.contactName\",\r\n            \"contactTel\":\"$.trfList[0].lab.labContact.contactTelephone\",\r\n            \"contactEmail\":\"$.trfList[0].lab.labContact.contactEmail\"\r\n        },\r\n        \"trfProduct\":{\r\n            \"dffFormId\":\"$.productList[0].templateId\",\r\n            \"dffGridId\":\"$.sampleList[0].templateId\",\r\n            \"dffFormData\":\"#dffJsonFn(productList,$.productList)\",\r\n            \"dffGridData\":\"#dffJsonFn(sampleList,$.sampleList)\"\r\n        },\r\n        \"servicRequire\":{\r\n            \"isComment\":null,\r\n            \"isCopy\":null,\r\n            \"isPhoto\":\"$.trfList[0].serviceRequirement.report.needPhoto\",\r\n            \"isConfimCover\":null,\r\n            \"isQuotation\":null,\r\n            \"returnSampleRequire\":\"#sgsMartGetReturnSampleRequireFn($.serviceRequirement.sample)\",\r\n            \"returnSampleName\":null,\r\n            \"reportLanguage\":\"#toStringFn($.trfList[0].serviceRequirement.report.reportLanguage)\",\r\n	        \"reportLanguageName\":\"#mapping($.trfList[0].serviceRequirement.report.reportLanguage,\'1\':\'英文报告\'&\'2\':\'中文报告\'&\'3\':\'中英文报告\')\",\r\n            \"reportHeader\":\"#toStringFn($.trfList[0].serviceRequirement.report.languageList[languageId=2].reportHeader)\",\r\n            \"reportAddress\":\"#toStringFn($.trfList[0].serviceRequirement.report.languageList.reportAddress)\",\r\n            \"otherRequirea\":null,\r\n            \"reportHeaderEn\":\"$.trfList[0].serviceRequirement.report.reportHeader\",\r\n            \"reportAddressEn\":\"$.trfList[0].serviceRequirement.report.reportAddress\"\r\n        },\r\n        \"trfAttachments\":[\r\n            {\r\n                \"attachmentId\":\"$.trfList[0].attachmentList[*].cloudId\",\r\n                \"fileName\":\"$.trfList[0].attachmentList[*].fileName\",\r\n                \"size\":null\r\n            }\r\n        ],\r\n        \"carelabelInstances\":[\r\n            {\r\n                \"careInstruction\":\"$.careLabelList[*].careInstruction\",\r\n                \"radioType\":\"$.careLabelList[*].radioType\",\r\n                \"selectCountry\":\"$.careLabelList[*].selectCountry\",\r\n                \"selectImgIds\":null,\r\n                \"productItemNos\":null,\r\n                \"careLabelSeq\":\"$.careLabelList[*].careLabelSeq\",\r\n                \"careLabelFileId\":null,\r\n                \"productItemNo\":null,\r\n                \"imgArray\":[\r\n                    {\r\n                        \"imageDetailId\":null,\r\n                        \"sequenceNo\":null,\r\n                        \"picture\":null,\r\n                        \"checked\":null\r\n                    }\r\n                ]\r\n            }\r\n        ],\r\n        \"compositionIngredients\":[\r\n            {\r\n                \"chemicalEnName\":null,\r\n                \"chemicalZhName\":null,\r\n                \"casNo\":null,\r\n                \"composition\":null,\r\n                \"sortedIndex\":null\r\n            }\r\n        ],\r\n        \"testPackageList\":[\r\n            {\r\n                \"testPackageId\":null,\r\n                \"parentId\":null,\r\n                \"testPackageName\":null,\r\n                \"isSelect\":null,\r\n                \"remark\":null,\r\n                \"sort\":null\r\n            }\r\n        ]\r\n    }\r\n}', `response_body_template` = '{}', `remark` = 'SgsMart OrderToTrf调用', `created_by` = 'XQW', `created_date` = '2023-12-13 19:30:08', `modified_by` = 'XQW', `modified_date` = current_timestamp, `last_modified_timestamp` = current_timestamp
                           WHERE `id` = 28;

INSERT INTO `tb_dict_value`(`id`, `system_id`, `dict_type`, `dict_value`, `dict_label`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (28, 40, 'SystemId', '47', 'SGSMart_DateEntry', 1, 'system', '2024-01-11 17:43:18', 'system', '2024-01-11 17:43:23', '2024-01-11 17:43:28');

-- 缺少 Lows 的DFF　Mapping
INSERT INTO tb_cfg_dff_mapping (bu_code,ref_system_id,　type, label_code, label_name, customer_label, customer_field)
VALUES
    ('HL',10018,'productList', 'specialProductAttribute6', 'Testing Type', 'typeOfTest', 'typeOfTest'),
    ('HL',10018,'productList', 'specialProductAttribute2', 'Submission Type', 'submissionType', 'submissionType'),
    ('HL',10018,'productList', 'previousReportNo', 'Previous Report Number', 'previousReportNumber', 'previousReportNumber'),
    ('HL',10018,'productList', 'specialProductAttribute1', 'Testing Program', 'sourcingType', 'sourcingType'),
    ('HL',10018,'productList', 'productCategory1', 'Item Category', 'testItemCategory', 'testItemCategory'),
    ('HL',10018,'productList', 'specialProductAttribute15', 'Lab Type', 'labType', 'labType'),
    ('HL',10018,'productList', 'buyerOrgannizationCode3', 'Brand', 'brand', 'brand'),
    ('HL',10018,'productList', 'collection', 'Private Brand', 'privateBrand', 'privateBrand'),
    ('HL',10018,'productList', 'refCode8', 'Version No', 'trfVersionNumber', 'trfVersionNumber'),
    ('HL',10018,'productList', 'specialProductAttribute16', 'Invoice for Billing', 'invoiceForBilling', 'additionalQuestions.invoiceForBilling'),
    ('HL',10018,'productList', 'specialProductAttribute8', 'Is this a side stack', 'sideStack', 'additionalQuestions.sideStack'),
    ('HL',10018,'productList', 'construction', 'Other Specialized Instruction', 'otherSpecializedInstruction', 'additionalQuestions.otherSpecializedInstruction'),
    ('HL',10018,'productList', 'fiberWeight', 'Product Weight', 'productWeight', 'additionalQuestions.productWeight'),
    ('HL',10018,'productList', 'fabricWidth', 'Product Dimensions(LxWxH)', 'productDimensions', 'additionalQuestions.productDimensions'),
    ('HL',10018,'productList', 'productColor', 'Product Color', 'productColor', 'additionalQuestions.productColor'),
    ('HL',10018,'productList', 'refCode3', 'Previously Tested Component', 'previouslyTestedComponent', 'additionalQuestions.previouslyTestedComponent'),
    ('HL',10018,'productList', 'specialCustomerAttribute16', 'Additional Packaging Claims', 'additionalPackagingClaims', 'additionalQuestions.additionalPackagingClaims'),
    ('HL',10018,'productList', 'specialProductAttribute5', 'Sealing Tape #', 'sealingTape', 'additionalQuestions.sealingTape'),
    ('HL',10018,'productList', 'vendorNo', 'Vendor ID', 'vid', 'vendorDetails.vid'),
    ('HL',10018,'productList', 'specialProductAttribute14', 'Vendor Name', 'vendorName', 'vendorDetails.vendorName'),
    ('HL',10018,'productList', 'specialCustomerAttribute6', 'Vendor Email', 'email', 'vendorDetails.email'),
    ('HL',10018,'productList', 'specialCustomerAttribute8', 'Vendor Contact Person', 'contact', 'vendorDetails.contact'),
    ('HL',10018,'productList', 'specialCustomerAttribute17', 'Vendor Address', 'address', 'vendorDetails.address'),
    ('HL',10018,'productList', 'specialCustomerAttribute5', 'Vendor Tel', 'telephone', 'vendorDetails.telephone'),
    ('HL',10018,'productList', 'buyerOrgannization3', 'Vendor Alt-Address', 'altAddress', 'vendorDetails.altAddress'),
    ('HL',10018,'productList', 'specialCustomerAttribute4', 'Vendor Fax', 'fax', 'vendorDetails.fax'),
    ('HL',10018,'productList', 'factoryID', 'Factory ID', 'fid', 'factoryDetails.fid'),
    ('HL',10018,'productList', 'supplier', 'Factory Name', 'factoryName', 'factoryDetails.factoryName'),
    ('HL',10018,'productList', 'specialCustomerAttribute3', 'Factory Email', 'email', 'factoryDetails.email'),
    ('HL',10018,'productList', 'specialCustomerAttribute10', 'Factory Contact Person', 'contact', 'factoryDetails.contact'),
    ('HL',10018,'productList', 'specialCustomerAttribute18', 'Factory Address', 'address', 'factoryDetails.address'),
    ('HL',10018,'productList', 'specialCustomerAttribute2', 'Factory Tel', 'telephone', 'factoryDetails.telephone'),
    ('HL',10018,'productList', 'refCode6', 'Factory Alt-Address', 'altAddress', 'factoryDetails.altAddress'),
    ('HL',10018,'productList', 'specialCustomerAttribute1', 'Factory Fax', 'fax', 'factoryDetails.fax'),
    ('HL',10018,'productList', 'specialProductAttribute11', 'Reference ID', 'referenceId', 'referenceId'),
    ('HL',10018,'sampleList', 'pONo', 'Item Retailer', 'itemRetailer', 'testedItem.itemRetailer/repItems.itemRetailer'),
    ('HL',10018,'sampleList', 'productDescription', 'Sample Description(For GPO)', 'itemDescription', 'testedItem.itemDescription/repItems.itemDescription'),
    ('HL',10018,'sampleList', 'itemNo', 'Item Number', 'itemNumber', 'testedItem.itemNumber/repItems.itemNumber'),
    ('HL',10018,'sampleList', 'sampleID', 'Sample No', 'itemNumber', 'testedItem.itemNumber/repItems.itemNumber'),
    ('HL',10018,'sampleList', 'styleNo', 'Vendor Model/Style#', 'modelNumber', 'testedItem.modelNumber/repItems.modelNumber'),
    ('HL',10018,'sampleList', 'specialProductAttribute10', 'Vendor No. (VBU) (Item Level)', 'hovbu', 'testedItem.hovbu/repItems.hovbu'),
    ('HL',10018,'sampleList', 'season', 'Sourcing Type (Item Level)', 'sourcingType', 'testedItem.sourcingType/repItems.sourcingType'),
    ('HL',10018,'sampleList', 'countryOfDestination', 'Country of Destination', 'countryOfDestination', 'testedItem.countryOfDestination/repItems.countryOfDestination'),
    ('HL',10018,'sampleList', 'fiberComposition', 'Testing Required', 'testingRequired', 'testedItem.testingRequired/repItems.testingRequired'),
    ('HL',10018,'sampleList', 'refCode4', 'Testing Comments', 'testingComment', 'testedItem.testingComment/repItems.testingComment');

-- 缺少 TIC 的DFF Mapping
INSERT INTO tb_cfg_dff_mapping (bu_code, ref_system_id, TYPE, label_code, label_name, customer_label, customer_field)
VALUES
    ('SL',12, 'productList', 'ProductDescription', 'Product Name', 'sampleName', 'sampleName'),
    ('SL',12, 'productList', 'Collection', 'Brand', 'brandName', 'brandName'),
    ('SL',12, 'productList', 'SpecialProductAttribute10', 'Product Grade', 'productGrade', 'productGrade'),
    ('SL',12, 'productList', 'SpecialProductAttribute9', 'Safety Category', 'safetyTechnicalLevel', 'safetyTechnicalLevel'),
    ('SL',12, 'productList', 'FactoryName', 'Manufacturer', 'manufacturerName', 'manufacturerName'),
    ('SL',12, 'productList', 'SpecialProductAttribute1', 'Special information', 'specialInfo', 'specialInfo'),
    ('SL',12, 'productList', 'ProductColor', 'Sample Color', 'colour', 'colour'),
    ('SL',12, 'productList', 'BuyerOrgannization1', 'Buyer', 'buyer', 'buyer'),
    ('SL',12, 'productList', 'ProductDescription', 'Sample Description', 'sampleDescription', 'sampleDescription'),
    ('SL',12, 'productList', 'StyleNo', 'Style No.', 'styleNo', 'styleNo'),
    ('SL',12, 'productList', 'RefCode1', 'Order No.', 'orderNo', 'orderNo'),
    ('SL',12, 'productList', 'SpecialProductAttribute2', 'Other Info', 'others', 'others'),
    ('SL',12, 'productList', 'NoOfSample', 'No. Of Sample', 'sampleNum', 'sampleNum'),
    ('SL',12, 'productList', 'FiberComposition', 'Composition', 'material', 'material'),
    ('SL',12, 'productList', 'EndUse1', 'End Use', 'endUses', 'endUses');

-- 修改Lows Import API.importTrf.template
UPDATE `sgs_todolistdb`.`tb_cfg_system_api` SET `response_body_template` = '{\r\n  \"header\": {\r\n    \"refSystemId\": 10018,\r\n    \"source\":1,\r\n    \"trfNo\": \"$.referenceId\",\r\n    \"serviceType\": \"#mapping($.additionalQuestions.serviceType,\'Regular\':1&\'Rush\':2,null)\",\r\n    \"sampleLevel\": null,\r\n    \"parcelNoList\": null,\r\n    \"trfSubmissionDate\": null,\r\n    \"selfTestFlag\": null,\r\n    \"others\": {\r\n      \"trfRemark\": null\r\n    },\r\n    \"lab\": {\r\n      \"labId\": null,\r\n      \"labCode\": \"#mapping($.labShortName, \'SGS-Shunde HL\':\'SD HL\'&\'SGS-Shanghai HL\':\'SH HL\'&\'SGS-Shanghai E&E\':\'SH EE\'&\'SGS-Ningbo E&E\':\'NB EE\'&\'SGS-Tianjin HL\':\'TJ HL\'&\'SGS-Guangzhou HL\':\'GZ HL\'&\'SGS-Guangzhou E&E\':\'GZ EE\'&\'SGS-Vietnam\':\'HCMC HL\', null)\",\r\n      \"buCode\": \"#mapping($.labShortName, \'SGS-Shunde HL\':\'HL\'&\'SGS-Shanghai HL\':\'HL\'&\'SGS-Shanghai E&E\':\'EE\'&\'SGS-Ningbo E&E\':\'EE\'&\'SGS-Tianjin HL\':\'HL\'&\'SGS-Guangzhou HL\':\'HL\'&\'SGS-Guangzhou E&E\':\'EE\'&\'SGS-Vietnam\':\'HL\', null)\",\r\n      \"labContract\": {\r\n        \"contractName\": null,\r\n        \"telephone\": null,\r\n        \"email\": null\r\n      }\r\n    },\r\n    \"extFields\": {\r\n      \"lowes\": {\r\n        \"trfVersionNumber\": \"$.trfVersionNumber\",\r\n        \"labShortName\": \"$.labShortName\"\r\n      }\r\n    }\r\n  },\r\n  \"serviceRequirement\": {\r\n    \"report\": {\r\n      \"reportLanguage\": 1\r\n    }\r\n  },\r\n  \"customerList\": [\r\n    {\r\n      \"customerInstanceId\": null,\r\n      \"customerUsage\": 3,\r\n      \"customerId\": null,\r\n      \"bossNo\": \"370196\",\r\n      \"customerGroupCode\": \"CG0000846\",\r\n      \"customerName\": \"LOWE\'S\",\r\n      \"customerAddress\": null,\r\n      \"_languageList\": [\r\n        {\r\n          \"languageId\": null,\r\n          \"customerName\": null,\r\n          \"customerAddress\": null\r\n        }\r\n      ]\r\n    },\r\n    {\r\n      \"customerInstanceId\": null,\r\n      \"customerUsage\": 1,\r\n      \"customerId\": null,\r\n      \"bossNo\": \"370196\",\r\n      \"customerGroupCode\": \"CG0000846\",\r\n      \"customerName\": \"LOWE\'S\",\r\n      \"customerAddress\": null,\r\n      \"_languageList\": [\r\n        {\r\n          \"languageId\": null,\r\n          \"customerName\": null,\r\n          \"customerAddress\": null\r\n        }\r\n      ]\r\n    },\r\n    {\r\n      \"customerInstanceId\": null,\r\n      \"customerUsage\": 5,\r\n      \"customerId\": null,\r\n      \"bossNo\": null,\r\n      \"customerGroupCode\": null,\r\n      \"customerName\": \"$.vendorDetails.vendorName\",\r\n      \"itemRetailer\": \"$.vendorDetails.vendorName\",\r\n      \"itemRetailercustomerName\": \"$.testedItem.itemRetailer\",\r\n      \"customerAddress\": \"$.vendorDetails.address\",\r\n      \"languageList\": [\r\n        {\r\n          \"languageId\": 1,\r\n          \"customerName\": \"$.vendorDetails.vendorName\",\r\n          \"customerAddress\": \"$.vendorDetails.address\"\r\n        },\r\n        {\r\n          \"languageId\": 2,\r\n          \"customerName\": null,\r\n          \"customerAddress\": \"$.vendorDetails.altAddress\"\r\n        }\r\n      ],\r\n      \"customerContactList\": [\r\n        {\r\n          \"customerContactId\": null,\r\n          \"bossContactId\": null,\r\n          \"bossSiteUseId\": null,\r\n          \"contactName\": \"$.vendorDetails.contact\",\r\n          \"contactTelephone\": \"$.vendorDetails.telephone\",\r\n          \"contactMobile\": \"$.vendorDetails.email\",\r\n          \"contactFax\": \"$.vendorDetails.fax\",\r\n          \"contactEmail\": null\r\n        }\r\n      ]\r\n    },{\r\n      \"customerInstanceId\": null,\r\n      \"customerUsage\": 6,\r\n      \"customerId\": null,\r\n      \"bossNo\": null,\r\n      \"customerGroupCode\": null,\r\n      \"customerName\": \"$.factoryDetails.factoryName\",\r\n      \"customerAddress\": \"$.factoryDetails.address\",\r\n      \"languageList\": [\r\n        {\r\n          \"languageId\": 1,\r\n          \"customerName\": \"$.factoryDetails.factoryName\",\r\n          \"customerAddress\": \"$.factoryDetails.address\"\r\n        },\r\n        {\r\n          \"languageId\": 2,\r\n          \"customerName\": null,\r\n          \"customerAddress\": \"$.factoryDetails.altAddress\"\r\n        }\r\n      ],\r\n      \"customerContactList\": [\r\n        {\r\n          \"customerContactId\": null,\r\n          \"bossContactId\": null,\r\n          \"bossSiteUseId\": null,\r\n          \"contactName\": \"$.factoryDetails.contact\",\r\n          \"contactTelephone\": \"$.factoryDetails.telephone\",\r\n          \"contactMobile\": \"$.factoryDetails.email\",\r\n          \"contactFax\": \"$.factoryDetails.fax\",\r\n          \"contactEmail\": null\r\n        }\r\n      ]\r\n    }\r\n  ],\r\n  \"product\": {\r\n    \"templateId\": \"#dffFormId(10018,,#mapping($.labShortName, \'SGS-Shunde HL\':\'HL\'&\'SGS-Shanghai HL\':\'HL\'&\'SGS-Shanghai E&E\':\'EE\'&\'SGS-Ningbo E&E\':\'EE\'&\'SGS-Tianjin HL\':\'HL\'&\'SGS-Guangzhou HL\':\'HL\'&\'SGS-Guangzhou E&E\':\'EE\'&\'SGS-Vietnam\':\'HL\', null))\",\r\n    \"productAttrList\": {\r\n      \"typeOfTest\":\"$.typeOfTest\",\r\n      \"submissionType\":\"$.submissionType\",\r\n      \"previousReportNumber\":\"$.previousReportNumber\",\r\n      \"sourcingType\":\"$.sourcingType\",\r\n      \"testItemCategory\":\"$.testItemCategory\",\r\n      \"labType\":\"$.labType\",\r\n      \"brand\":\"$.brand\",\r\n      \"privateBrand\":\"$.privateBrand\",\r\n      \"trfVersionNumber\":\"$.trfVersionNumber\",\r\n      \"additionalQuestions.invoiceForBilling\":\"$.additionalQuestions.invoiceForBilling\",\r\n      \"additionalQuestions.sideStack\":\"$.additionalQuestions.sideStack\",\r\n      \"additionalQuestions.otherSpecializedInstruction\":\"$.additionalQuestions.otherSpecializedInstruction\",\r\n      \"additionalQuestions.productWeight\":\"$.additionalQuestions.productWeight\",\r\n      \"additionalQuestions.productDimensions\":\"$.additionalQuestions.productDimensions\",\r\n      \"additionalQuestions.productColor\":\"$.additionalQuestions.productColor\",\r\n      \"additionalQuestions.previouslyTestedComponent\": \"$.additionalQuestions.previouslyTestedComponent\",\r\n      \"additionalQuestions.additionalPackagingClaims\": \"$.additionalQuestions.additionalPackagingClaims\",\r\n      \"additionalQuestions.sealingTape\": \"$.additionalQuestions.sealingTape\",\r\n      \"vendorDetails.vid\": \"$.vendorDetails.vid\",\r\n      \"vendorDetails.vendorName\": \"$.vendorDetails.vendorName\",\r\n      \"vendorDetails.email\": \"$.vendorDetails.email\",\r\n      \"vendorDetails.contact\": \"$.vendorDetails.contact\",\r\n      \"vendorDetails.address\": \"$.vendorDetails.address\",\r\n      \"vendorDetails.telephone\": \"$.vendorDetails.telephone\",\r\n      \"vendorDetails.altAddress\": \"$.vendorDetails.altAddress\",\r\n      \"vendorDetails.fax\": \"$.vendorDetails.fax\",\r\n      \"factoryDetails.fid\": \"$.factoryDetails.fid\",\r\n      \"factoryDetails.factoryName\": \"$.factoryDetails.factoryName\",\r\n      \"factoryDetails.email\":\"$.factoryDetails.email\",\r\n      \"factoryDetails.contact\": \"$.factoryDetails.contact\",\r\n      \"factoryDetails.address\": \"$.factoryDetails.address\",\r\n      \"factoryDetails.telephone\": \"$.factoryDetails.telephone\",\r\n      \"factoryDetails.altAddress\": \"$.factoryDetails.altAddress\",\r\n      \"factoryDetails.fax\": \"$.factoryDetails.fax\"\r\n    }\r\n  },\r\n  \"sampleList\": [\r\n    {\r\n      \"sampleInstanceId\": null,\r\n      \"templateId\": \"#dffGridId(12,,#mapping($.labShortName, \'SGS-Shunde HL\':\'HL\'&\'SGS-Shanghai HL\':\'HL\'&\'SGS-Shanghai E&E\':\'EE\'&\'SGS-Ningbo E&E\':\'EE\'&\'SGS-Tianjin HL\':\'HL\'&\'SGS-Guangzhou HL\':\'HL\'&\'SGS-Guangzhou E&E\':\'EE\'&\'SGS-Vietnam\':\'HL\', null))\",\r\n      \"sampleNo\": null,\r\n      \"externalSampleNo\": null,\r\n      \"_itemRetailer\": \"$.repItems[*].itemRetailer\",\r\n      \"_itemDescription\": \"$.repItems[*].itemDescription\",\r\n      \"_itemNumber\":\"$.repItems[*].itemNumber\",\r\n      \"_modelNumber\": \"$.repItems[*].modelNumber\",\r\n      \"_hovbu\": \"$.repItems[*].hovbu\",\r\n      \"_sourcingType\": \"$.repItems[*].sourcingType\",\r\n      \"_countryOfDestination\": \"$.repItems[*].countryOfDestination\",\r\n      \"_testingRequired\": \"$.repItems[*].testingRequired\",\r\n      \"_testingComment\": \"$.repItems[*].testingComment\",\r\n      \"sampleAttrList\": {\r\n        \"repItems.itemRetailer\": \"@_itemRetailer\",\r\n        \"repItems.itemDescription\": \"@_itemDescription\",\r\n        \"repItems.itemNumber\":\"@_itemNumber\",\r\n        \"repItems.modelNumber\": \"@_modelNumber\",\r\n        \"repItems.hovbu\": \"@_hovbu\",\r\n        \"repItems.sourcingType\": \"@_sourcingType\",\r\n        \"repItems.countryOfDestination\": \"@_countryOfDestination\",\r\n        \"repItems.testingRequired\": \"@_testingRequired\",\r\n        \"repItems.testingComment\": \"@_testingComment\"\r\n      }\r\n    },\r\n    {\r\n      \"sampleInstanceId\": null,\r\n      \"templateId\": \"86a25e58-e61c-4e45-adc8-626eb7fe9249\",\r\n      \"sampleNo\": null,\r\n      \"externalSampleNo\": null,\r\n      \"sampleAttrList\": {\r\n        \"testedItem.itemRetailer\": \"$.testedItem.itemRetailer\",\r\n        \"testedItem.itemDescription\": \"$.testedItem.itemDescription\",\r\n        \"testedItem.itemNumber\":\"$.testedItem.itemNumber\",\r\n        \"testedItem.modelNumber\": \"$.testedItem.modelNumber\",\r\n        \"testedItem.hovbu\": \"$.testedItem.hovbu\",\r\n        \"testedItem.sourcingType\": \"$.testedItem.sourcingType\",\r\n        \"testedItem.countryOfDestination\": \"$.testedItem.countryOfDestination\",\r\n        \"testedItem.testingRequired\": \"$.testedItem.testingRequired\",\r\n        \"testedItem.testingComment\": \"$.testedItem.testingComment\"\r\n      }\r\n    }\r\n  ],\r\n  \"attachmentList\": [\r\n    {\r\n      \"fileName\": \"$.pdfFileName\",\r\n      \"fileType\": null,\r\n      \"filePath\": null,\r\n      \"cloudId\": \"$.pdfFileCloudId\"\r\n    }\r\n  ]\r\n}\r\n' WHERE `id` = 29;

-- 修改TIC Import API.importTrf.template
UPDATE `sgs_todolistdb`.`tb_cfg_system_api` SET `response_body_template` = '{\r\n  \"header\": {\r\n    \"refSystemId\": 12,\r\n    \"source\":1,\r\n    \"trfNo\": \"$.orderNo\",\r\n    \"serviceType\": \"$.baseInfo.isUrgent\",\r\n    \"productCategory\":\"#mapping($.baseInfo.subBuCode,\'SL-HZ-WB-REGULAR\':\'TX\',null)\",\r\n    \"sampleLevel\": null,\r\n    \"parcelNoList\": \"$.delivers[deliverType = 10].expressNo\",\r\n    \"trfSubmissionDate\": null,\r\n    \"selfTestFlag\": null,\r\n    \"others\": {\r\n      \"trfRemark\": \"#join(\' \',\'发票邮寄地址：\',$.delivers[deliverType=20].receiveName[0],$.delivers[deliverType=20].receivePhone[0],$.delivers[deliverType=20].receiveEmail[0],$.delivers[deliverType=20].receiveCity[0],$.delivers[deliverType=20].receiveTown[0])\"\r\n    },\r\n    \"lab\": {\r\n      \"labId\": \"$.applicationAttr[areaCode=\'labInfo\'][attrCode=\'labId\'].attrValue[0]\",\r\n      \"labCode\": \"$.applicationAttr[areaCode=\'labInfo\'][attrCode=\'labCode\'].attrValue[0]\",\r\n      \"buCode\": \"$.baseInfo.bu\",\r\n      \"labContract\": {\r\n        \"contractName\": null,\r\n        \"telephone\": null,\r\n        \"email\": null\r\n      }\r\n    },\r\n    \"extFields\": {\r\n      \"tic\": {\r\n        \"sampleRequirements\": \"$.baseInfo.sampleRequirements\",\r\n        \"ticCsCode\": \"$.baseInfo.csCode\",\r\n        \"applicationTestMemo\": \"$.applicationForm.testMemo\",\r\n        \"memoInfo\": \"$.memos[memoCode =\'formMemo\'].memoInfo[0]\",\r\n        \"subBuCode\": \"$.baseInfo.subBuCode\"\r\n      }\r\n    }\r\n  },\r\n  \"serviceRequirement\": {\r\n    \"report\": {\r\n      \"reportLanguage\": \"$.baseInfo.reportLua\",\r\n      \"reportForm\": \"$.baseInfo.reportForm\",\r\n      \"reportHeader\": null,\r\n      \"reportAddress\": null,\r\n      \"accreditation\": null,\r\n      \"needConclusion\": null,\r\n      \"needDraft\": \"\",\r\n      \"needPhoto\": \"\",\r\n      \"_languageList\": [\r\n        {\r\n          \"languageId\": null,\r\n          \"reportHeader\": null,\r\n          \"reportAddress\": null\r\n        }\r\n      ],\r\n      \"softcopy\": {\r\n        \"required\": \"#mapping($.baseInfo.reportForm,\'2\':1&\'3\':1,0)\",\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      },\r\n      \"hardcopy\": {\r\n        \"required\": \"#mapping($.baseInfo.reportForm,\'1\':1&\'3\':1,0)\",\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": \"#join(\' \',\'报告邮寄地址：\',$.delivers[deliverType=31].receiveName[0],$.delivers[deliverType=31].receivePhone[0],$.delivers[deliverType=31].receiveEmail[0],$.delivers[deliverType=31].receiveCity[0],$.delivers[deliverType=31].receiveTown[0])\",\r\n        \"deliveryWay\": null\r\n      }\r\n    },\r\n    \"sample\": {\r\n      \"returnResidueSample\": {\r\n        \"required\": null,\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": \"#join(\' \',\'样品退回地址：\',$.delivers[deliverType=30].receiveName[0],$.delivers[deliverType=30].receivePhone[0],$.delivers[deliverType=30].receiveEmail[0],$.delivers[deliverType=30].receiveCity[0],$.delivers[deliverType=30].receiveTown[0])\",\r\n        \"deliveryWay\": null\r\n      },\r\n      \"_returnTestSample\": {\r\n        \"required\": null,\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      }\r\n    },\r\n    \"invoice\": {\r\n      \"invoiceType\": \"$.invoice.invoiceType\",\r\n      \"invoiceTitle\": \"$.invoice.invoiceTitle\",\r\n      \"taxNo\": \"$.invoice.taxNo\",\r\n      \"registerAddr\": \"$.invoice.registerAddr\",\r\n      \"registerPhone\": \"$.invoice.registerPhone\",\r\n      \"bankName\": \"$.invoice.bankAddr\",\r\n      \"bankNumber\": \"$.invoice.bankNumber\",\r\n      \"actualAmountPaid\": \"$.baseInfo.realAmount\",\r\n      \"_invoiceDelivery\": {\r\n        \"required\": null,\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      }\r\n    }\r\n  },\r\n  \"customerList\": [\r\n    {\r\n      \"_templateId\": \"$.applicationAttr[0].areaCode\",\r\n      \"customerInstanceId\": null,\r\n      \"customerUsage\": 1,\r\n      \"customerId\": null,\r\n      \"bossNo\": null,\r\n      \"customerGroupCode\": null,\r\n      \"customerName\": \"$.applicationForm.companyName\",\r\n      \"customerAddress\": null,\r\n      \"_languageList\": [\r\n        {\r\n          \"languageId\": null,\r\n          \"customerName\": null,\r\n          \"customerAddress\": null\r\n        }\r\n      ],\r\n      \"_customerContactList\": [\r\n        {\r\n          \"_templateId\": \"$.applicationAttr[0].areaCode\",\r\n          \"customerContactId\": null,\r\n          \"bossContactId\": null,\r\n          \"bossSiteUseId\": null,\r\n          \"contactName\": null,\r\n          \"contactTelephone\": null,\r\n          \"contactMobile\": null,\r\n          \"contactFax\": null,\r\n          \"contactEmail\": null\r\n        }\r\n      ]\r\n    }\r\n  ],\r\n  \"product\": {\r\n    \"templateId\": \"#dffFormId(12,$.baseInfo.subBuCode,$.baseInfo.bu)\",\r\n    \"productAttrList\":\r\n    {\r\n      \"sampleName\": \"$.sampleForm[sampleKey=\'sampleName\'][0].sampleValue\",\r\n      \"brandName\": \"$.sampleForm[sampleKey=\'brandName\'][0].sampleValue\",\r\n      \"styleNo\": \"$.sampleForm[sampleKey=\'styleNo\'][0].sampleValue\",\r\n      \"productGrade\": \"$.sampleForm[sampleKey=\'productGrade\'][0].sampleValue\",\r\n      \"safetyTechnicalLevel\": \"$.sampleForm[sampleKey=\'safetyTechnicalLevel\'][0].sampleValue\",\r\n      \"manufacturerName\": \"$.sampleForm[sampleKey=\'manufacturerName\'][0].sampleValue\",\r\n      \"specialInfo\":\"$.sampleForm[sampleKey=\'specialInfo\'][0].sampleValue\",\r\n      \"buyer\": \"$.sampleForm[sampleKey=\'buyer\'][0].sampleValue\",\r\n      \"sampleDescription\": \"$.sampleForm[sampleKey=\'sampleDescription\'][0].sampleValue)\",\r\n      \"orderNo\": \"$.sampleForm[sampleKey=\'orderNo\'][0].sampleValue\",\r\n      \"others\":\"$.sampleForm[sampleKey=\'others\'][0].sampleValue\"\r\n    }\r\n  },\r\n  \"sampleList\": [\r\n    {\r\n      \"templateId\": \"#dffGridId(12,$.baseInfo.subBuCode,$.baseInfo.bu)\",\r\n      \"sampleInstanceId\": null,\r\n      \"sampleNo\": null,\r\n      \"externalSampleNo\": null,\r\n      \"_sampleNum\": \"$.sampleForm[sampleKey=\'sampleNum\'][0].sampleValue\",\r\n      \"_colour\": \"$.sampleForm[sampleKey=\'colour\'][0].sampleValue\",\r\n      \"_material\": \"$.sampleForm[sampleKey=\'material\'][0].sampleValue\",\r\n      \"_endUses\": \"$.sampleForm[sampleKey=\'endUses\'][0].sampleValue\",\r\n      \"_sampleName\": \"$.sampleForm[sampleKey=\'sampleName\'][0].sampleValue\",\r\n      \"sampleAttrList\":\r\n      {\r\n        \"sampleNum\": \"@_sampleNum\",\r\n        \"colour\": \"@_colour\",\r\n        \"material\": \"@_material\",\r\n        \"endUses\": \"@_endUses\",\r\n        \"sampleName\": \"@_sampleName\"\r\n      }\r\n    }\r\n  ],\r\n  \"_careLabelList\": [\r\n    {\r\n      \"careInstruction\": null,\r\n      \"radioType\": null,\r\n      \"selectCountry\": null,\r\n      \"careLabelSeq\": null,\r\n      \"sampleIds\": null\r\n    }\r\n  ],\r\n  \"_testSampleList\": [\r\n    {\r\n      \"testSampleType\": null,\r\n      \"testSampleSeq\": null,\r\n      \"materialAttrList\": [\r\n        {\r\n          \"materialDescription\": null,\r\n          \"materialEndUse\": null,\r\n          \"materialColor\": null,\r\n          \"materialTexture\": null,\r\n          \"materialSampleRemark\": null,\r\n          \"extFields\": {\r\n            \"materialCategory\": null,\r\n            \"materialSku\": null,\r\n            \"materialItem\": null\r\n          }\r\n        }\r\n      ]\r\n    }\r\n  ],\r\n  \"testLineList\": [\r\n    {\r\n      \"_tempKey1\": \"$.items[*].itemName\",\r\n      \"_tempKey2\": \"$.items[*].standardCode\",\r\n      \"testLineId\": null,\r\n      \"evaluationAlias\": null,\r\n      \"testLineSeq\": \".[index]\",\r\n      \"_citation\": {\r\n        \"citationId\": null,\r\n        \"citationType\": null,\r\n        \"citationFullName\": null\r\n      },\r\n      \"_ppTestLineRelList\": [\r\n        {\r\n          \"ppNo\": null,\r\n          \"ppName\": null\r\n        }\r\n      ],\r\n      \"externalInfo\": {\r\n        \"testItemId\": null,\r\n        \"testItemName\": \"@_tempKey1\",\r\n        \"testCitationId\": null,\r\n        \"testCitationName\": \"@_tempKey2\",\r\n        \"checkType\": null\r\n      }\r\n    }\r\n  ],\r\n  \"attachmentList\": [\r\n    {\r\n      \"fileName\": \"$.attachments[*].fileName\",\r\n      \"fileType\": null,\r\n      \"filePath\": \"$.attachments[*].fileUrl\",\r\n      \"cloudId\": \"$.attachments[*].cloudId\"\r\n    }\r\n  ]\r\n}\r\n'  WHERE `id` = 22;
