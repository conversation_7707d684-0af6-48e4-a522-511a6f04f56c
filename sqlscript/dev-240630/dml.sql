-- SCI-986
UPDATE `tb_cfg_system_api` SET  `request_body_template` = '{\r\n  \"refSystemId\": \"$.refSystemId\",\r\n  \"productLineCode\": \"AFL\",\r\n  \"trfNo\": \"$.trfNo\",\r\n  \"action\": \"GetYiliTrf\"\r\n}\r\n\r\n', `response_body_template` = '{\r\n  \"header\": {\r\n    \"refSystemId\": \"#refSystemId(\'YiLi\')\",\r\n    \"source\": 1,\r\n    \"trfNo\": \"$.WTDH\",\r\n    \"serviceType\": \"#mapping($.FWLX,\'标准*\':1&\'加急\':2&\'特级\':4)\",\r\n    \"sampleLevel\": null,\r\n    \"parcelNoList\": \"$.KDDH\",\r\n    \"trfSubmissionDate\": null,\r\n    \"selfTestFlag\": null,\r\n    \"others\": {\r\n      \"trfRemark\": \"$.REMARK\"\r\n    },\r\n    \"_lab\": {\r\n      \"labId\": null,\r\n      \"labCode\": null,\r\n      \"buCode\": null,\r\n      \"labContract\": {\r\n        \"contractName\": null,\r\n        \"telephone\": null,\r\n        \"email\": null\r\n      }\r\n    },\r\n    \"_extFields\": {\r\n    }\r\n  },\r\n  \"serviceRequirement\": {\r\n    \"report\": {\r\n      \"reportLanguage\": \"#mapping($.BGYJ,\'中文\':2&\'英文\':1&\'中英文对照\':3)\",\r\n      \"reportForm\": null,\r\n      \"reportHeader\": null,\r\n      \"reportAddress\": null,\r\n      \"accreditation\": null,\r\n      \"needConclusion\": null,\r\n      \"needDraft\": null,\r\n      \"needPhoto\": null,\r\n      \"splitReportBy\": \"#mapping($.BGFS,\'一个样品一份报告\':\'Sample\'&\'多个样品一份报告\':\'Others\'&\'其他\':\'Others\')\",\r\n      \"_languageList\": [\r\n        {\r\n          \"languageId\": null,\r\n          \"reportHeader\": null,\r\n          \"reportAddress\": null\r\n        }\r\n      ],\r\n      \"softcopy\": {\r\n        \"required\": \"#mapping($.BGLX,\'电子报告\':1,0)\",\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      },\r\n      \"hardcopy\": {\r\n        \"required\": \"#mapping($.BGLX,\'纸质报告\':1,0)\",\r\n        \"deliveryTo\": \"$.BGYJDZ\",\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      }\r\n    },\r\n    \"_sample\": {\r\n      \"returnResidueSample\": {\r\n        \"required\": null,\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      },\r\n      \"_returnTestSample\": {\r\n        \"required\": null,\r\n        \"deliveryTo\": null,\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      }\r\n    },\r\n    \"invoice\": {\r\n      \"invoiceType\": \"#mapping($.FPLX,\'增值税\':1&\'普通\':2)\",\r\n      \"invoiceTitle\": null,\r\n      \"taxNo\": null,\r\n      \"registerAddr\": null,\r\n      \"registerPhone\": null,\r\n      \"bankName\": null,\r\n      \"bankNumber\": null,\r\n      \"actualAmountPaid\": null,\r\n      \"invoiceDelivery\": {\r\n        \"required\": 1,\r\n        \"deliveryTo\": \"$.BGYJDZ\",\r\n        \"deliveryCc\": null,\r\n        \"deliveryOthers\": null,\r\n        \"deliveryWay\": null\r\n      }\r\n    }\r\n  },\r\n  \"customerList\": [\r\n    {\r\n      \"customerInstanceId\": null,\r\n      \"customerUsage\": 1,\r\n      \"customerId\": null,\r\n      \"bossNo\": null,\r\n      \"customerGroupCode\": null,\r\n      \"customerName\": \"$.WTDMC\",\r\n      \"customerAddress\": \"$.WTDDZ\",\r\n      \"languageList\": [\r\n        {\r\n          \"languageId\": 1,\r\n          \"customerName\": \"$.WTDYWMC\",\r\n          \"customerAddress\": \"$.WTDYWDZ\"\r\n        }\r\n      ],\r\n      \"customerContactList\": [\r\n        {\r\n          \"customerContactId\": null,\r\n          \"bossContactId\": null,\r\n          \"bossSiteUseId\": null,\r\n          \"contactName\": \"$.LXR\",\r\n          \"contactTelephone\": \"$.GDDH\",\r\n          \"contactMobile\": \"$.SJ\",\r\n          \"contactFax\": \"$.CZ\",\r\n          \"contactEmail\": \"$.DZYJ\"\r\n        }\r\n      ]\r\n    },\r\n    {\r\n      \"customerUsage\": 2,\r\n      \"customerName\": \"$.FKGS\"\r\n    }\r\n  ],\r\n  \"_product\": {\r\n    \"templateId\": null,\r\n    \"productAttrList\": [\r\n      {\r\n        \"labelCode\": null,\r\n        \"labelName\": null,\r\n        \"labelValue\": null,\r\n        \"attrSeq\": null,\r\n        \"customerLabel\": null,\r\n        \"dataType\": null\r\n      }\r\n    ]\r\n  },\r\n  \"sampleList\": [\r\n    {\r\n      \"templateId\": null,\r\n      \"sampleInstanceId\": \"$.SAMPLEDATA[*].G_ID\",\r\n      \"sampleNo\": null,\r\n      \"externalSampleNo\": \"$.SAMPLEDATA[*].SC\",\r\n      \"sampleAttrList\": [\r\n        {\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].YPMC\",\r\n          \"attrSeq\": 1,\r\n          \"customerLabel\": \"YPMC\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].SCRQ\",\r\n          \"attrSeq\": 2,\r\n          \"customerLabel\": \"SCRQ\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].PH\",\r\n          \"attrSeq\": 3,\r\n          \"customerLabel\": \"PH\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].CZ\",\r\n          \"attrSeq\": 4,\r\n          \"customerLabel\": \"CZ\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].SL\",\r\n          \"attrSeq\": 5,\r\n          \"customerLabel\": \"SL\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].GG\",\r\n          \"attrSeq\": 6,\r\n          \"customerLabel\": \"GG\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].SB\",\r\n          \"attrSeq\": 7,\r\n          \"customerLabel\": \"SB\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].GYS\",\r\n          \"attrSeq\": 8,\r\n          \"customerLabel\": \"GYS\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].SCCJ\",\r\n          \"attrSeq\": 9,\r\n          \"customerLabel\": \"SCCJ\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].SCSJDZ\",\r\n          \"attrSeq\": 10,\r\n          \"customerLabel\": \"SCSJDZ\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].YPZT\",\r\n          \"attrSeq\": 11,\r\n          \"customerLabel\": \"YPZT\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].YPWH\",\r\n          \"attrSeq\": 12,\r\n          \"customerLabel\": \"YPWH\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].YPLY\",\r\n          \"attrSeq\": 13,\r\n          \"customerLabel\": \"YPLY\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].LB\",\r\n          \"attrSeq\": 14,\r\n          \"customerLabel\": \"LB\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].PX\",\r\n          \"attrSeq\": 15,\r\n          \"customerLabel\": \"PX\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].SJMD\",\r\n          \"attrSeq\": 16,\r\n          \"customerLabel\": \"SJMD\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].JCXMLB\",\r\n          \"attrSeq\": 17,\r\n          \"customerLabel\": \"JCXMLB\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].CCTJ\",\r\n          \"attrSeq\": 18,\r\n          \"customerLabel\": \"CCTJ\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].BLQX\",\r\n          \"attrSeq\": 19,\r\n          \"customerLabel\": \"BLQX\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].SYTJ\",\r\n          \"attrSeq\": 20,\r\n          \"customerLabel\": \"SYTJ\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].YJYPBMWD\",\r\n          \"attrSeq\": 21,\r\n          \"customerLabel\": \"YJYPBMWD\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].ISXJ\",\r\n          \"attrSeq\": 22,\r\n          \"customerLabel\": \"ISXJ\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].BAK1\",\r\n          \"attrSeq\": 23,\r\n          \"customerLabel\": \"BAK1\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].BAK2\",\r\n          \"attrSeq\": 24,\r\n          \"customerLabel\": \"BAK2\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].BAK3\",\r\n          \"attrSeq\": 25,\r\n          \"customerLabel\": \"BAK3\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].BAK4\",\r\n          \"attrSeq\": 26,\r\n          \"customerLabel\": \"BAK4\"\r\n        },{\r\n          \"labelCode\": null,\r\n          \"labelName\": null,\r\n          \"labelValue\": \"$.SAMPLEDATA[SC = @externalSampleNo][0].BAK5\",\r\n          \"attrSeq\": 27,\r\n          \"customerLabel\": \"BAK5\"\r\n        }\r\n      ]\r\n    }\r\n  ],\r\n  \"_careLabelList\": [\r\n    {\r\n      \"careInstruction\": null,\r\n      \"radioType\": null,\r\n      \"selectCountry\": null,\r\n      \"careLabelSeq\": null,\r\n      \"sampleIds\": null\r\n    }\r\n  ],\r\n  \"testSampleList\": [\r\n    {\r\n      \"testSampleType\": 101,\r\n      \"testSampleSeq\": \".[index]\",\r\n      \"externalSampleNo\": \"$.SAMPLEDATA[*].SC\",\r\n      \"_materialAttrList\": [\r\n        {\r\n          \"materialDescription\": null,\r\n          \"materialEndUse\": null,\r\n          \"materialColor\": null,\r\n          \"materialTexture\": null,\r\n          \"materialSampleRemark\": null,\r\n          \"extFields\": {\r\n            \"materialCategory\": null,\r\n            \"materialSku\": null,\r\n            \"materialItem\": null\r\n          }\r\n        }\r\n      ]\r\n    }\r\n  ],\r\n  \"testMatrixList\": [\r\n    {\r\n      \"#LOOP\":\"$.SAMPLEDATA[*].PADATA[*]\",\r\n      \"testMatrixId\":null,\r\n      \"externalTestMatrixId\":\"$._ITEM.G_ID\",\r\n      \"testItemId\":\"$._ITEM.PA\",\r\n      \"testSampleNo\":null,\r\n      \"externalTestSampleNo\":\"$._ITEM.SC\",\r\n      \"testLineInstanceId\":\"#join(\'\',$._ITEM.PA,$._ITEM.PANAME,$._ITEM.METHOD1)\",\r\n      \"testSampleInstanceId\":null\r\n    }\r\n  ],\r\n  \"testLineList\": [\r\n    {\r\n      \"#LOOP\":\"$.SAMPLEDATA[*].PADATA[*]\",\r\n      \"testLineInstanceId\":\"#join(\'\',$._ITEM.PA,$._ITEM.PANAME,$._ITEM.METHOD1)\",\r\n      \"testLineSeq\":\".[index]\",\r\n      \"_testItemId\":\"$._ITEM.PA\",\r\n      \"_testItemName\":\"$._ITEM.PANAME\",\r\n      \"_testCitationName\":\"$._ITEM.METHOD1\",\r\n      \"externalInfo\":{\r\n        \"testItemId\":\"@_testItemId\",\r\n        \"testItemName\":\"@_testItemName\",\r\n        \"testCitationId\":\"@_testCitationId\",\r\n        \"testCitationName\":\"@_testCitationName\"\r\n      }\r\n    }\r\n  ],\r\n  \"_attachmentList\": [\r\n    {\r\n      \"fileName\": null,\r\n      \"fileType\": null,\r\n      \"filePath\": null,\r\n      \"cloudId\": null\r\n    }\r\n  ]\r\n}\r\n' WHERE `id` = 24;
