-- auto-generated definition
create table tb_trf_event_pool
(
    id            varchar(32) not null comment 'ID'
        primary key,
    subscriber    int         not null comment '订阅方',
    ref_system_id int         not null comment 'refSystemId',
    event_code    int         not null comment '事件编号',
    trf_event     longtext    not null comment 'TrfEvent',
    task_id       bigint      null comment '关联的task_info的task_id',
    created_date  datetime    not null comment '创建时间',
    status        int         not null comment '状态 0 初始化 1 处理中 2 处理成功 3 处理失败'
)
    comment '存放定时消息通知处理器产生的trf事件';

create index tb_trf_event_pool_idx_created_date
    on tb_trf_event_pool (created_date);

create index tb_trf_event_pool_idx_subscriber
    on tb_trf_event_pool (subscriber);

-- auto-generated definition
create table tb_trf_report_delivery_info
(
    subscriber    int         not null comment '订阅号',
    report_no     varchar(50) not null comment '报告号',
    delivery_flag int         not null comment '发送标记 1: ING, 2:SUCCESS',
    create_date   datetime    not null comment '创建时间',
    primary key (report_no, subscriber)
)
    comment 'trf报告投递记录表';

