-- 添加字段header.trfList.lab.labContact.contactEmail 和 order.productList
INSERT INTO tb_dfv_func_field (id, system_id, function_id, full_field_path, field_path, field_code, field_type, label_name, sort_num, parent_id, active_indicator, created_by, created_date, modified_by, modified_date)
VALUES (634, 40, 1, 'header.trfList.lab', 'header.trfList.lab', 'lab', 'object', 'Lab', 1, 12, 1, 'system', now(), 'system', now());

INSERT INTO tb_dfv_func_field (id, system_id, function_id, full_field_path, field_path, field_code, field_type, label_name, sort_num, parent_id, active_indicator, created_by, created_date, modified_by, modified_date)
VALUES (635, 40, 1, 'header.trfList.lab.labContact', 'header.trfList.lab.labContact', 'labContact', 'object', 'LabContact', 1, 634, 1, 'system', now(), 'system', now());

INSERT INTO tb_dfv_func_field (id, system_id, function_id, full_field_path, field_path, field_code, field_type, label_name, sort_num, parent_id, active_indicator, created_by, created_date, modified_by, modified_date)
VALUES (636, 40, 1, 'header.trfList.lab.labContact.contactEmail', 'header.trfList.lab.labContact.contactEmail', 'contactEmail', 'string', 'ContactEmail', 1, 635, 1, 'system', now(), 'system', now());

INSERT INTO tb_dfv_func_field (id, system_id, function_id, full_field_path, field_path, field_code, field_type, label_name, sort_num, parent_id, active_indicator, created_by, created_date, modified_by, modified_date)
VALUES (637, 40, 1, 'order.productList', 'order.productList', 'productList', 'string', 'ProductList', 0, 4, 1, 'system', now(), 'system', now());


-- 添加模板 SyncCompleted 10018(RefSystemId = lowes) 47 (systemId=SGSMart)
INSERT INTO sgs_todolistdb.tb_dfv_template (id, system_id, template_code, function_id, description, active_indicator, created_by, created_date, modified_by, modified_date, last_modified_timestamp)
VALUES (7, 40, 'sci-syncTrf-SyncCompleted-10018-47', 1, 'Sync Trf接口：SyncCompleted action，lowes客户，SGSMart系统场景', 1, 'system', '2024-03-19 19:17:59', 'system', '2024-03-19 19:17:59', '2024-03-19 19:22:31');

-- 添加 template_field
INSERT INTO tb_dfv_template_field (id, template_id, field_id, field_path, sort_num, parent_id, active_indicator, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1128, 7, 2, 'header', 0, 0, 1, 'System', '2024-03-21 14:57:00', 'System', '2024-03-21 14:57:00', '2024-03-21 14:57:00');
INSERT INTO tb_dfv_template_field (id, template_id, field_id, field_path, sort_num, parent_id, active_indicator, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1129, 7, 12, 'header.trfList', 0, 1128, 1, 'System', '2024-03-21 14:57:00', 'System', '2024-03-21 14:57:00', '2024-03-21 14:57:00');
INSERT INTO tb_dfv_template_field (id, template_id, field_id, field_path, sort_num, parent_id, active_indicator, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1130, 7, 634, 'header.trfList.lab', 0, 1129, 1, 'System', '2024-03-21 14:57:01', 'System', '2024-03-21 14:57:01', '2024-03-21 14:57:00');
INSERT INTO tb_dfv_template_field (id, template_id, field_id, field_path, sort_num, parent_id, active_indicator, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1131, 7, 635, 'header.trfList.lab.labContact', 0, 1130, 1, 'System', '2024-03-21 14:57:01', 'System', '2024-03-21 14:57:01', '2024-03-21 14:57:00');
INSERT INTO tb_dfv_template_field (id, template_id, field_id, field_path, sort_num, parent_id, active_indicator, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1132, 7, 636, 'header.trfList.lab.labContact.contactEmail', 0, 1131, 1, 'System', '2024-03-21 14:57:01', 'System', '2024-03-21 14:57:01', '2024-03-21 14:57:00');
INSERT INTO tb_dfv_template_field (id, template_id, field_id, field_path, sort_num, parent_id, active_indicator, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1133, 7, 7, 'reportList', 0, 0, 1, 'System', '2024-03-21 14:57:01', 'System', '2024-03-21 14:57:01', '2024-03-21 14:57:00');
INSERT INTO tb_dfv_template_field (id, template_id, field_id, field_path, sort_num, parent_id, active_indicator, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1134, 7, 470, 'reportList.reportNo', 0, 1133, 1, 'System', '2024-03-21 14:57:01', 'System', '2024-03-21 14:57:01', '2024-03-21 14:57:00');
INSERT INTO tb_dfv_template_field (id, template_id, field_id, field_path, sort_num, parent_id, active_indicator, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1135, 7, 472, 'reportList.softCopyDeliveryDate', 0, 1133, 1, 'System', '2024-03-21 14:57:01', 'System', '2024-03-21 14:57:01', '2024-03-21 14:57:00');
INSERT INTO tb_dfv_template_field (id, template_id, field_id, field_path, sort_num, parent_id, active_indicator, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1136, 7, 4, 'order', 0, 0, 1, 'System', '2024-03-21 14:57:01', 'System', '2024-03-21 14:57:01', '2024-03-21 14:57:01');
INSERT INTO tb_dfv_template_field (id, template_id, field_id, field_path, sort_num, parent_id, active_indicator, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1137, 7, 49, 'order.testingStartDate', 0, 1136, 1, 'System', '2024-03-21 14:57:01', 'System', '2024-03-21 14:57:01', '2024-03-21 14:57:01');
INSERT INTO tb_dfv_template_field (id, template_id, field_id, field_path, sort_num, parent_id, active_indicator, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1138, 7, 637, 'order.productList', 0, 1136, 1, 'System', '2024-03-21 14:57:01', 'System', '2024-03-21 14:57:01', '2024-03-21 14:57:01');
INSERT INTO tb_dfv_template_field (id, template_id, field_id, field_path, sort_num, parent_id, active_indicator, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1139, 7, 9, 'testResultList', 0, 0, 1, 'System', '2024-03-21 14:57:02', 'System', '2024-03-21 14:57:02', '2024-03-21 14:57:01');
INSERT INTO tb_dfv_template_field (id, template_id, field_id, field_path, sort_num, parent_id, active_indicator, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1140, 7, 341, 'testResultList.testResult', 0, 1139, 1, 'System', '2024-03-21 14:57:02', 'System', '2024-03-21 14:57:02', '2024-03-21 14:57:01');
INSERT INTO tb_dfv_template_field (id, template_id, field_id, field_path, sort_num, parent_id, active_indicator, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1141, 7, 353, 'testResultList.testResult.resultUnit', 0, 1140, 1, 'System', '2024-03-21 14:57:02', 'System', '2024-03-21 14:57:02', '2024-03-21 14:57:01');
INSERT INTO tb_dfv_template_field (id, template_id, field_id, field_path, sort_num, parent_id, active_indicator, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1142, 7, 354, 'testResultList.testResult.resultValue', 0, 1140, 1, 'System', '2024-03-21 14:57:02', 'System', '2024-03-21 14:57:02', '2024-03-21 14:57:01');

-- 添加较检

INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1901, 7, 1128, 2, 'header', 'NotNull', null, null, 0, 1, 0, 'system', '2024-03-21 15:19:04', 'system', '2024-03-21 15:19:04', '2024-03-21 15:19:04');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1902, 7, 1129, 12, 'header.trfList', 'NotEmpty', null, null, 0, 1, 0, 'system', '2024-03-21 15:19:04', 'system', '2024-03-21 15:19:04', '2024-03-21 15:19:04');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1903, 7, 1130, 634, 'header.trfList.lab', 'NotNull', null, null, 0, 1, 0, 'system', '2024-03-21 15:19:04', 'system', '2024-03-21 15:19:04', '2024-03-21 15:19:04');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1904, 7, 1131, 635, 'header.trfList.lab.labContact', 'NotNull', null, null, 0, 1, 0, 'system', '2024-03-21 15:19:04', 'system', '2024-03-21 15:19:04', '2024-03-21 15:19:04');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1905, 7, 1132, 636, 'header.trfList.lab.labContact.contactEmail', 'NotEmpty', null, null, 0, 1, 0, 'system', '2024-03-21 15:19:04', 'system', '2024-03-21 15:19:04', '2024-03-21 15:19:04');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1906, 7, 1133, 7, 'reportList', 'NotEmpty', null, null, 0, 1, 0, 'system', '2024-03-21 15:19:04', 'system', '2024-03-21 15:19:04', '2024-03-21 15:19:04');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1907, 7, 1134, 470, 'reportList.reportNo', 'NotEmpty', null, null, 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:04');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1908, 7, 1135, 472, 'reportList.softCopyDeliveryDate', 'NotNull', null, null, 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:04');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1909, 7, 1135, 472, 'reportList.softCopyDeliveryDate', 'BeforeOrEqToday', null, null, 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:04');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1910, 7, 1135, 472, 'reportList.softCopyDeliveryDate', 'AfterOrEqRefDate', 'order.testingStartDate', null, 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:04');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1911, 7, 1136, 4, 'order', 'NotNull', null, null, 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:04');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1912, 7, 1137, 49, 'order.testingStartDate', 'NotNull', null, null, 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:04');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1913, 7, 1137, 49, 'order.testingStartDate', 'BeforeOrEqToday', null, null, 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:04');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1914, 7, 1138, 637, 'order.productList', 'NotNull', null, null, 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:04');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1915, 7, 1138, 637, 'order.productList', 'NotNullIfRefNotNull', 'reportList[0].originalReportNo', 'order.productList[0].productAttrList[labelCode=\'threadCount\'][0].labelValue', 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:04');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1916, 7, 1138, 637, 'order.productList', 'RefNotNull', 'order.productList[0].productAttrList[labelCode=\'refCode1\'][0].labelValue', null, 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:04');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1917, 7, 1138, 637, 'order.productList', 'RefNotNull', 'order.productList[0].productAttrList[labelCode=\'refCode2\'][0].labelValue', null, 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:04');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1918, 7, 1138, 637, 'order.productList', 'RefNotNull', 'order.productList[0].productAttrList[labelCode=\'specialCustomerAttribute13\'][0].labelValue', null, 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:04');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1919, 7, 1138, 637, 'order.productList', 'RefNotNull', 'order.productList[0].productAttrList[labelCode=\'specialCustomerAttribute12\'][0].labelValue', null, 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:04');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1920, 7, 1138, 637, 'order.productList', 'NotNullIfRefCondition', '{"expr":"order.productList[0].productAttrList[labelCode=\'specialProductAttribute6\'][0].labelValue","value":"Product Testing"}', 'order.productList[0].productAttrList[labelCode=\'specialCustomerAttribute14\'][0].labelValue', 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:04');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1921, 7, 1138, 637, 'order.productList', 'NotNullIfRefCondition', '{"expr":"order.productList[0].productAttrList[labelCode=\'specialProductAttribute6\'][0].labelValue","value":"Product Testing"}', 'order.productList[0].productAttrList[labelCode=\'specialProductAttribute4\'][0].labelValue', 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:05');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1922, 7, 1138, 637, 'order.productList', 'NotNullIfRefCondition', '{"expr":"order.productList[0].productAttrList[labelCode=\'specialProductAttribute6\'][0].labelValue","value":"Product Testing"}', 'order.productList[0].productAttrList[labelCode=\'specialProductAttribute9\'][0].labelValue', 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:05');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1923, 7, 1138, 637, 'order.productList', 'NotNullIfRefCondition', '{"expr":"order.productList[0].productAttrList[labelCode=\'specialProductAttribute6\'][0].labelValue","value":"Product Testing"}', 'order.productList[0].productAttrList[labelCode=\'specialProductAttribute7\'][0].labelValue', 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:05');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1924, 7, 1138, 637, 'order.productList', 'NotNullIfRefCondition', '{"expr":"order.productList[0].productAttrList[labelCode=\'specialProductAttribute6\'][0].labelValue","value":"Product Testing"}', 'order.productList[0].productAttrList[labelCode=\'peformanceCode\'][0].labelValue', 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:05');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1925, 7, 1138, 637, 'order.productList', 'NotNullIfRefCondition', '{"expr":"order.productList[0].productAttrList[labelCode=\'specialProductAttribute6\'][0].labelValue","value":"Product Testing"}', 'order.productList[0].productAttrList[labelCode=\'productionStage\'][0].labelValue', 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:05');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1926, 7, 1138, 637, 'order.productList', 'NotNullIfRefCondition', '{"expr":"order.productList[0].productAttrList[labelCode=\'productionStage\'][0].labelValue","value":"Y"}', 'order.productList[0].productAttrList[labelCode=\'trimReportNo\'][0].labelValue', 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:05');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1927, 7, 1138, 637, 'order.productList', 'NotNullIfRefCondition', '{"expr":"order.productList[0].productAttrList[labelCode=\'productionStage\'][0].labelValue","value":"Y"}', 'order.productList[0].productAttrList[labelCode=\'buyerOrgannization2\'][0].labelValue', 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:05');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1928, 7, 1138, 637, 'order.productList', 'NotNullIfRefCondition', '{"expr":"order.productList[0].productAttrList[labelCode=\'specialProductAttribute6\'][0].labelValue","value":"Transit Testing"}', 'testResultList', 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:05');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1929, 7, 1139, 9, 'testResultList', 'NotEmpty', null, null, 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:05');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1930, 7, 1140, 341, 'testResultList.testResult', 'NotNull', null, null, 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:05');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1931, 7, 1141, 353, 'testResultList.testResult.resultUnit', 'NotNull', null, null, 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:05');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1932, 7, 1141, 353, 'testResultList.testResult.resultUnit', 'Dict', null, 'In,Inches,IN,INCHES,in,in.,IN.,In.,Pounds,POUNDS,LBS,lbs,lb.,LB.,Lb.', 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:05');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1933, 7, 1142, 354, 'testResultList.testResult.resultValue', 'NotNull', null, null, 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:05');
INSERT INTO tb_dfv_template_field_constraint (id, template_id, template_field_id, field_id, field_path, type, constraint_value1, constraint_value2, field_id_ref, active_indicator, sort_num, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (1934, 7, 1142, 354, 'testResultList.testResult.resultValue', 'Digits', '20', '8', 0, 1, 0, 'system', '2024-03-21 15:19:05', 'system', '2024-03-21 15:19:05', '2024-03-21 15:19:05');
