INSERT INTO `preorder`.`tb_cfg_info` (`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (16, '', '', '10017', 'SL', 2, 'TrfStatusControl', '{\r\n    \"trfOrderRelationshipRule\":4,\r\n    \"statusRule\":0,\r\n    \"statusFlow\":[\r\n        1,\r\n        3,\r\n        4,\r\n        5,\r\n        6,\r\n        7,\r\n				100\r\n    ],\r\n    \"statusMapping\":{\r\n        \"1\":1,\r\n        \"3\":3,\r\n        \"4\":4,\r\n        \"5\":5,\r\n        \"6\":6,\r\n        \"7\":7,\r\n        \"9\":9,\r\n				\"100\":100\r\n    },\r\n    \"actionStatusMapping\":{\r\n        \"SyncToOrder\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":1,\r\n            \"ctrlMapping\":{\r\n                \"3\":0\r\n            }\r\n        },\r\n        \"SyncConfirmed\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"4\":0\r\n            }\r\n        },\r\n        \"SyncTesting\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"5\":0\r\n            }\r\n        },\r\n        \"SyncCompleted\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"6\":0\r\n            }\r\n        },\r\n        \"SyncClosed\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"7\":8\r\n            }\r\n        },\r\n        \"SyncUnBind\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"1\":0,\r\n                \"2\":0,\r\n                \"3\":0,\r\n                \"4\":0,\r\n                \"5\":0\r\n            }\r\n        },\r\n        \"PreOrderReturnTrf\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":3,\r\n            \"ctrlMapping\":{\r\n                \"1\":0\r\n            }\r\n        },\r\n        \"CustomerReturnTrf\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":3,\r\n            \"ctrlMapping\":{\r\n                \"1\":0\r\n            }\r\n        },\r\n        \"SyncReviseReport\": {\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"100\": 0,\r\n								\"6\":0\r\n            }\r\n        }\r\n    }\r\n}', 'system', '2023-10-27 15:12:36', 'system', '2023-10-27 08:20:58', '2023-10-27 11:41:33');
INSERT INTO `preorder`.`tb_cfg_info` (`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (17, NULL, NULL, '10017', 'SL', 2, 'DeliveryReportMode', '1', 'system', '2023-10-27 14:35:20', 'system', '2023-10-27 14:35:23', '2023-10-27 14:35:54');
INSERT INTO `preorder`.`tb_cfg_info` (`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (18, 'CG0001085', '', '10017', 'SL', 2, 'DataSend', '{\"mode\":1,\"time\":\"2023-10-27 15:12:36\",\"identityId\":2}', 'system', '2023-10-27 15:12:36', 'system', '2023-10-27 15:12:36', '2023-10-27 15:12:36');