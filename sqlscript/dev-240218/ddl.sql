-- 添加新的字段
ALTER TABLE tb_api_request
    ADD COLUMN method_name varchar(100) DEFAULT NULL COMMENT '请求方法名',
    ADD COLUMN ext_id varchar(100) DEFAULT NULL COMMENT '业务ID，OrderNo or trfNo';


CREATE INDEX idx_api_request_ext_id ON tb_api_request (ext_id);
CREATE INDEX idx_api_request_method_name ON tb_api_request (method_name);

alter table tb_local_event modify event_content LongText NULL COMMENT '事件内容';
