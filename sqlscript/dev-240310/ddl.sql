CREATE TABLE `tb_cfg_mapping_info` (
                                       `id` int(11) NOT NULL AUTO_INCREMENT,
                                       `ref_system_id` int(11) DEFAULT NULL COMMENT '客户系统ID',
                                       `system_id` int(11) DEFAULT NULL COMMENT '执行系统ID',
                                       `bu` varchar(50) DEFAULT NULL COMMENT 'BU',
                                       `system_filed_name` varchar(100) DEFAULT NULL COMMENT 'System Filed Name',
                                       `system_filed_code` varchar(100) DEFAULT NULL COMMENT 'System Filed Code',
                                       `system_filed_value` varchar(100) DEFAULT NULL COMMENT 'System Filed Value',
                                       `system_filed_value_code` varchar(100) DEFAULT NULL COMMENT 'System Filed Value Code',
                                       `customer_filed_name` varchar(100) DEFAULT NULL COMMENT 'Customer Filed Name',
                                       `customer_filed_code` varchar(100) DEFAULT NULL COMMENT 'Customer Filed Code',
                                       `customer_filed_value` varchar(100) DEFAULT NULL COMMENT 'Customer Filed Value',
                                       `customer_filed_value_code` varchar(100) DEFAULT NULL COMMENT 'Customer Filed Value Code',
                                       `language_id` int(11) DEFAULT NULL COMMENT 'LanguageId',
                                       `created_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                                       `created_date` datetime DEFAULT NULL COMMENT '创建时间',
                                       `modified_by` varchar(255) DEFAULT NULL COMMENT '更新人',
                                       `modified_date` datetime DEFAULT NULL COMMENT '更新时间',
                                       `last_modified_timestamp` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- 添加新的字段
ALTER TABLE tb_trf_test_item
    ADD COLUMN test_line_instance_id varchar(36) DEFAULT NULL COMMENT 'testLineInstanceId' ;


