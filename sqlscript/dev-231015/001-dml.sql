INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (12, '', '', '10016', 'AFL', 2, 'TrfStatusControl', '{\r\n    \"trfOrderRelationshipRule\":4,\r\n    \"statusRule\":0,\r\n    \"statusFlow\":[\r\n        1,\r\n        3,\r\n        4,\r\n        5,\r\n        6,\r\n        7,\r\n				100\r\n    ],\r\n    \"statusMapping\":{\r\n        \"1\":1,\r\n        \"3\":3,\r\n        \"4\":4,\r\n        \"5\":5,\r\n        \"6\":6,\r\n        \"7\":7,\r\n        \"9\":9,\r\n				\"100\":100\r\n    },\r\n    \"actionStatusMapping\":{\r\n        \"SyncToOrder\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":1,\r\n            \"ctrlMapping\":{\r\n                \"3\":0\r\n            }\r\n        },\r\n        \"SyncConfirmed\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"4\":0\r\n            }\r\n        },\r\n        \"SyncTesting\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"5\":0\r\n            }\r\n        },\r\n        \"SyncCompleted\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"6\":0\r\n            }\r\n        },\r\n        \"SyncClosed\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"7\":8\r\n            }\r\n        },\r\n        \"SyncUnBind\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"1\":0,\r\n                \"2\":0,\r\n                \"3\":0,\r\n                \"4\":0,\r\n                \"5\":0\r\n            }\r\n        },\r\n        \"PreOrderReturnTrf\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":3,\r\n            \"ctrlMapping\":{\r\n                \"1\":0\r\n            }\r\n        },\r\n        \"CustomerReturnTrf\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":3,\r\n            \"ctrlMapping\":{\r\n                \"1\":0\r\n            }\r\n        },\r\n        \"SyncReviseReport\": {\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"100\": 0,\r\n								\"6\":0\r\n            }\r\n        }\r\n    }\r\n}', 'system', '2023-05-11 15:12:36', 'system', '2023-05-15 08:20:58', '2023-09-27 11:41:33');
INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (13, NULL, NULL, '10016', 'AFL', 2, 'DeliveryReportMode', '1', 'system', '2023-09-27 14:35:20', 'system', '2023-09-27 14:35:23', '2023-09-27 14:35:54');

INSERT INTO  `tb_cfg_system_api`(`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (15, 10002, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{}', '{}', 'SGSMart回传能力配置', 'Walley', '2023-09-07 16:23:12', 'Walley', '2023-09-07 16:23:12', '2023-09-07 16:23:12');
INSERT INTO  `tb_cfg_system_api`(`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (16, 10016, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{\r\n    \"action\":\"SyncOrderStatus\",\r\n    \"dataStatus\":\"ToOrder\",\r\n    \"bu\":\"$.extra.productLineCode\",\r\n    \"objectNumber\":\"$.trfList[0].trfNo\",\r\n    \"refSystemId\":10016,\r\n    \"msgId\":\"$.extra.randomSequence\",\r\n    \"body\":{\r\n        \"WTDH\":\"$.trfList[0].trfNo\",\r\n        \"STATUS\":\"接样\",\r\n        \"Time\":\"#toDate($.extra.syncInfo.order.serviceStartDate)\",\r\n        \"REMARK\":null\r\n    }\r\n}', '{}', 'AFL 状态同步接口（ToOrder）', 'YANG', '2023-09-08 13:42:12', 'YANG', '2023-09-08 13:44:20', '2023-09-11 10:50:17');
INSERT INTO  `tb_cfg_system_api`(`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (17, 10016, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{\r\n    \"action\":\"SyncOrderStatus\",\r\n    \"dataStatus\":\"Confirmed\",\r\n    \"bu\":\"$.extra.productLineCode\",\r\n    \"objectNumber\":\"$.trfList[0].trfNo\",\r\n    \"refSystemId\":10016,\r\n    \"msgId\":\"$.extra.randomSequence\",\r\n    \"body\":{\r\n        \"WTDH\":\"$.trfList[0].trfNo\",\r\n        \"STATUS\":\"受理\",\r\n        \"Time\":\"#toDate($.extra.syncInfo.order.serviceConfirmDate)\",\r\n        \"REMARK\":null\r\n    }\r\n}', '{}', 'AFL 状态同步接口（Confirmed）', 'YANG', '2023-09-08 13:42:12', 'YANG', '2023-09-08 13:44:20', '2023-09-11 10:50:18');
INSERT INTO  `tb_cfg_system_api`(`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (18, 10016, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{\r\n    \"action\":\"SyncOrderStatus\",\r\n    \"dataStatus\":\"Testing\",\r\n    \"bu\":\"$.extra.productLineCode\",\r\n    \"objectNumber\":\"$.trfList[0].trfNo\",\r\n    \"refSystemId\":10016,\r\n    \"msgId\":\"$.extra.randomSequence\",\r\n    \"body\":{\r\n        \"WTDH\":\"$.trfList[0].trfNo\",\r\n        \"STATUS\":\"检测中\",\r\n        \"Time\":\"#toDate($.extra.syncInfo.order.testingStartDate)\",\r\n        \"REMARK\":null\r\n    }\r\n}', '{}', 'AFL 状态同步接口（Testing）', 'YANG', '2023-09-08 13:42:12', 'YANG', '2023-09-08 13:44:20', '2023-09-11 10:50:18');
INSERT INTO  `tb_cfg_system_api`(`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (19, 10016, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{\r\n    \"action\":\"SyncOrderStatus\",\r\n    \"dataStatus\":\"Completed\",\r\n    \"bu\":\"$.extra.productLineCode\",\r\n    \"objectNumber\":\"$.trfList[0].trfNo\",\r\n    \"refSystemId\":10016,\r\n    \"msgId\":\"$.extra.randomSequence\",\r\n    \"body\":{\r\n        \"WTDH\":\"$.trfList[0].trfNo\",\r\n        \"STATUS\":\"报告出具\",\r\n        \"Time\":\"#toDate($.extra.syncInfo.reportList[0].softCopyDeliveryDate)\",\r\n        \"REMARK\":null\r\n    }\r\n}', '{}', 'AFL 状态同步接口（Completed）', 'YANG', '2023-09-08 13:42:12', 'YANG', '2023-09-08 13:44:20', '2023-09-11 10:50:19');
INSERT INTO  `tb_cfg_system_api`(`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (20, 10016, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{
  "_reports":[
    {
		"_reportNo": "$.reportList[*].reportNo",
      "data": [
        {
				"_reportNo": "$.reportList[*].reportNo",
          "_orderNo": "$.reportList[reportNo=@._reportNo].orderNo",
					"_testSampleInstanceId":"$.reportList[*].reportMatrixList[*].testSampleInstanceId",
					"_testLineInstanceId":"$.reportList[*].reportMatrixList[*].testLineInstanceId",
          "G_ID": "$.testSampleList[testSampleInstanceId=@._testSampleInstanceId][0].external.testSampleId",
          "WTDH": "$.trfList[0].trfNo",
          "SFSC": "$.testSampleList[testSampleInstanceId=@._testSampleInstanceId][0].testSampleNo",
          "SC": "$.testSampleList[testSampleInstanceId=@._testSampleInstanceId][0].external.testSampleNo",
          "PDFTIME": "#toDate($.reportList[*].softCopyDeliveryDate,''yyyy-MM-dd'')",
          "VER": "#yiliReportDeliveryCounter($.extra.refSystemId,$.trfList[0].trfNo)",
          "YPBMWD": null,
          "PDFFILES": [
            {
              "PDFNAME": "$.reportList[reportNo = @_reportNo].reportFileList[*].fileName",
              "PDFSTRING": null,
              "PDFURI": "$.reportList[reportNo = @_reportNo].reportFileList[*].cloudId"
            }
          ],
          "PADATA": [
            {
              "G_ID": "$.reportList[reportNo = @_reportNo].reportMatrixList[*].testMatrixId",
              "SC": null,
              "PA": "$.orderList[0].testItemMappingList[testLineInstanceId =@_testLineInstanceId].external.testItemId",
              "PANAME": "$.orderList[0].testItemMappingList[testLineInstanceId =@_testLineInstanceId].external.testItemName",
              "UNIT": "$.testResultList[testMatrixId = @.G_ID].testResult.resultUnit[0]",
              "VALUE_S": "#listToStr($.testResultList[testMatrixId = @.G_ID].testResult.resultValue)",
              "CONCLUSION": "$.reportList[reportNo =@_reportNo][0].reportMatrixList[testMatrixId = @.G_ID][0].conclusion.customerConclusion",
              "METHOD": "$.testLineList[testLineInstanceId = @_testLineInstanceId][0].citation.languageList[0].citationFullName",
              "METHODNAME": "$.testLineList[testLineInstanceId = @_testLineInstanceId][0].citation.languageList[0].citationFullName",
              "LODORLOQ": "$.testResultList[testMatrixId = @.G_ID].methodLimit.limitType[0]",
              "LIMITVALUE": "$.testResultList[testMatrixId = @.G_ID].methodLimit.limitValueFullName[0]",
              "TESTTIME": "#toDate($.orderList[0].testingStartDate)",
              "TESTENDTIME": "#toDate($.orderList[0].testingEndDate)",
              "EQNAME": null,
              "SYTJ": null,
              "STATUS": null,
              "BZYQ": "$.testResultList[testMatrixId = @.G_ID].reportLimit.limitValueFullName[0]",
              "HighLimit": null,
              "LowLimit": null
            }
          ]
        }
      ]
    }
  ] ,
  "_buildReportsNode": "#buildVirtualNode(''_reports'',@._reports)",
  "action": "SyncReport",
  "dataStatus": "Completed",
  "bu": "$.extra.productLineCode",
  "objectNumber": "$.extra.trfNo",
  "refSystemId": "$.extra.refSystemId",
  "msgId": "$.extra.randomSequence",
  "body": {
    "reports": "$._reports[0].data"
  }
}');


INSERT INTO `tb_dict_value`(`id`, `system_id`, `dict_type`, `dict_value`, `dict_label`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (21, 40, 'RefSystemId', '10016', 'YILI系统', 1, 'system', '2023-09-07 14:38:31', 'system', '2023-09-07 14:38:37', '2023-09-07 14:38:42');
INSERT INTO `tb_dict_value`(`id`, `system_id`, `dict_type`, `dict_value`, `dict_label`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (22, 40, 'SystemId', '8', 'AFL系统', 1, 'system', '2023-05-04 15:35:32', 'system', '2023-05-04 15:35:48', '2023-05-25 18:55:22');



INSERT INTO `tb_cfg_event_subscribe`(`id`, `system_id`, `event_code`, `api_id`, `priority`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (42, 10016, 10, 16, 0, 24, 1, 'messageNotifyHandler', '', 'YANG', '2023-09-08 14:21:19', 'YANG', '2023-09-08 14:21:29', '2023-09-27 12:25:40');
INSERT INTO `tb_cfg_event_subscribe`(`id`, `system_id`, `event_code`, `api_id`, `priority`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (43, 10016, 30, 17, 0, 24, 1, 'messageNotifyHandler', '', 'YANG', '2023-09-08 14:21:19', 'YANG', '2023-09-08 14:21:29', '2023-09-27 12:25:43');
INSERT INTO `tb_cfg_event_subscribe`(`id`, `system_id`, `event_code`, `api_id`, `priority`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (44, 10016, 40, 18, 0, 24, 1, 'messageNotifyHandler', '', 'YANG', '2023-09-08 14:21:19', 'YANG', '2023-09-08 14:21:29', '2023-09-27 12:25:45');
INSERT INTO `tb_cfg_event_subscribe`(`id`, `system_id`, `event_code`, `api_id`, `priority`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (45, 10016, 60, 19, 0, 24, 2023, 'messageNotifyHandler', '', 'YANG', '2023-09-08 14:21:19', 'YANG', '2023-09-08 14:21:29', '2023-10-07 16:12:07');
INSERT INTO `tb_cfg_event_subscribe`(`id`, `system_id`, `event_code`, `api_id`, `priority`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (46, 10016, 60, 20, 1, 24, 2023, 'messageNotifyHandler', '', 'YANG', '2023-09-08 14:21:19', 'YANG', '2023-09-08 14:21:29', '2023-10-12 12:06:06');




-- 优衣库配置
INSERT INTO `tb_cfg_event_subscribe`(`id`, `system_id`, `event_code`, `api_id`, `priority`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (47, 10017, 60, 21, 0, 0, 8167, 'messageNotifyHandler', '', 'Lester', '2023-10-26 11:20:04', 'Lester', '2023-10-26 11:20:13', '2023-10-31 15:19:45');

INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (16, '', '', '10017', 'SL', 2, 'TrfStatusControl', '{
     "trfOrderRelationshipRule": 2,
     "statusRule": 0,
     "statusFlow": [
         1,
         3,
         4,
         5,
         6,
         7,
         100
     ],
     "statusMapping": {
         "1": 1,
         "3": 3,
         "4": 4,
         "5": 5,
         "6": 6,
         "7": 7,
         "100": 100
     },
     "actionStatusMapping": {
         "Import": {
             "orderRefRule": 0,
             "orderOpType": 1,
             "ctrlMapping": {}
         },
         "SyncToOrder": {
             "orderRefRule": 1,
             "orderOpType": 1,
             "ctrlMapping": {
                 "3": 0
             }
         },
         "SyncConfirmed": {
             "orderRefRule": 1,
             "orderOpType": 7,
             "ctrlMapping": {
                 "4": 0
             }
         },
         "SyncTesting": {
             "orderRefRule": 1,
             "orderOpType": 1,
             "ctrlMapping": {
                 "5": 0
             }
         },
         "SyncCompleted": {
             "orderRefRule": 1,
             "orderOpType": 7,
             "ctrlMapping": {
                 "6": 0
             }
         },
         "SyncClosed": {
             "orderRefRule": 1,
             "orderOpType": 7,
             "ctrlMapping": {
                 "7": 0
             }
         },
         "SyncPending": {
             "orderRefRule": 1,
             "orderOpType": 7,
             "ctrlMapping": {
                 "1": 1,
                 "3": 3,
                 "4": 4,
                 "5": 5
             }
         },
         "SyncUnPending": {
             "orderRefRule": 1,
             "orderOpType": 7,
             "ctrlMapping": {
                 "1": 1,
                 "3": 3,
                 "4": 4,
                 "5": 5
             }
         },
         "SyncUnBind": {
             "orderRefRule": 1,
             "orderOpType": 2,
             "ctrlMapping": {
                 "3": 3,
                 "5": 5,
                 "4": 4
             }
         },
         "SyncReviseReport": {
             "orderOpType": 7,
             "ctrlMapping": {
                 "100": 0,
 		"6":0
             }
         },
         "PreOrderReturnTrf":{
             "orderRefRule":1,
             "orderOpType":3,
             "ctrlMapping":{
                 "1":0
             }
         },
         "CustomerReturnTrf":{
             "orderRefRule":1,
             "orderOpType":3,
             "ctrlMapping":{
                 "1":0
             }
         }
     }
 }');
INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (17, NULL, NULL, '10017', 'SL', 2, 'DeliveryReportMode', '1', 'system', '2023-09-27 14:35:20', 'system', '2023-09-27 14:35:23', '2023-10-27 20:11:29');
INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (18, '', '', '10017', 'SL', 2, 'DataSend', '{\"mode\":1,\"cronExpression\":\"0 0 19 * * ? *\",\"identityId\":10017}', 'system', '2023-10-27 15:12:36', 'system', '2023-10-27 15:12:36', '2023-11-02 20:59:06');

INSERT INTO `tb_cfg_system_api`(`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (21, 10017, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{
  "body":{
		"_timeStr":"#dateTimeNameForMart(yyyyMMddHHmmssSSS)",
    "zipFileName":"#dateTimeNameForMart(AIF1161X01_20100091_,@._timeStr,.zip)",
    "report":{
		"_timeStrTwo":"#dateTimeNameForMart()",
      "header": {
        "fileName": "#dateTimeNameForMart(Quality_Result_Header_,@_timeStrTwo,.csv)",
        "data":[
          {
            "reportNo": "$.extra.syncInfo.allReportList[*].reportNo",
            "testInstitutionCode": "#mapping($.extra.syncInfo.allReportList[*].lab.labCode,''HK SL'':''sgs001''&''SH SL'':''sgs002''&''QD SL'':''sgs003''&''GZ SL'':''sgs004''&''CZ SL'':''sgs005'')",
            "testRequestFlag": "1",
            "brandCode": "010",
            "previousReportNo":"$.orderList[0].productList[0].productAttrList[labelCode=''PreviousReportNo''][0].value",
            "reportType": "$.orderList[0].productList[0].productAttrList[labelCode=''SpecialCustomerAttribute4''][0].value",
            "season": "$.orderList[0].productList[0].productAttrList[labelCode=''Season''][0].value",
            "phase": "$.orderList[0].productList[0].productAttrList[labelCode=''SpecialProductAttribute4''][0].value",
            "managementFactoryCode": "$.orderList[0].productList[0].productAttrList[labelCode=''SpecialProductAttribute5''][0].value",
            "testRequestNumber": "$.extra.trfNo",
            "receiptDate": "$.orderList[0].productList[0].productAttrList[labelCode=''SampleReceivedDate''][0].value",
            "issuranceDate": "#toDate($.extra.syncInfo.allReportList[*].softCopyDeliveryDate)",
            "comment": null,
            "URL": "$.extra.syncInfo.allReportList[*].reportFileList[0].cloudId",
            "reportFileName": "$.extra.syncInfo.allReportList[*].reportFileList[0].fileName",
            "updateType": "#uNIQLOUpdateTypeFn($.extra.syncInfo.allReportList[*].reportNo,$.extra.syncInfo.allReportList[*].lab.labCode,$.orderList[0].systemId,$.extra.syncInfo.allReportList[*].originalReportNo)"
          }
        ]
      },
      "detail": {
        "fileName": "#dateTimeNameForMart(Quality_Result_Detail_,@_timeStrTwo,.csv)",
        "data": [{
					"_sampleInstanceId":"$.reportConclusionList[conclusionLevelId=604].objectId",
					"_testLineInstanceId":"$.reportConclusionList[conclusionLevelId=604].testLineInstanceId",
					"_reportId":"$.reportConclusionList[conclusionLevelId=604].reportId",
					"_report":"$.reportList[reportId=@._reportId][0]",
					"_reportNo": "#getReportByTestMatrixIdFn(@._report)",
          "reportNo": "#getReportByTestMatrixIdFn(@._report)",
          "testInstitutionCode": "#mapping($.reportList[reportNo=@.reportNo][0].lab.labCode,''HK SL'':''sgs001''&''SH SL'':''sgs002''&''QD SL'':''sgs003''&''GZ SL'':''sgs004''&''CZ SL'':''sgs005'')",
          "testType": "#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._sampleInstanceId,SpecialCustomerAttribute2,2)",
          "testTargetCode": "#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._sampleInstanceId,SpecialProductAttribute2,2)",
          "colorCode":"#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._sampleInstanceId,ProductColor,2)",
          "colorName": "#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._sampleInstanceId,SpecialCustomerAttribute1,2)",
          "testItemKeyCode": "#uNIQLOTestLineMappingFn($.testLineList[testLineInstanceId=@._testLineInstanceId][0],1)",
          "testShareSampleCode": "#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._sampleInstanceId,SpecialProductAttribute3,2)",
          "supplierRawMaterialCode": "#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._sampleInstanceId,SpecialCustomerAttribute3,2)",
          "lotNo": "#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._sampleInstanceId,RefCode4,2)",
          "remark1": null,
          "remark2": null,
          "result": "#uNIQLOResultMappingFn($.reportConclusionList[conclusionLevelId=604].conclusionCode)",
          "comment": "$.reportConclusionList[conclusionLevelId=604].conclusionRemark",
          "updateType": "#uNIQLOUpdateTypeFn($.reportList[reportId=@._reportId][0].reportNo,$.reportList[reportId=@._reportId][0].lab.labCode,$.orderList[0].systemId,$.reportList[reportId=@._reportId][0].originalReportNo)"
        }
      ]},
      "subDetail": {
        "fileName":	"#dateTimeNameForMart(Quality_Result_SubDetail_,@_timeStrTwo,.csv)",
        "data":[
          {
					"_testMatrixId":"$.testResultList[*].testMatrixId",

					"_testLineInstanceId":"#getReportByTestMatrixIdFn($.reportList,@._testMatrixId,3)",
					"_testSampleInstanceId":"#getReportByTestMatrixIdFn($.reportList,@._testMatrixId,2)",
					"_positionInstanceId":"$.testResultList[*].testResult.testResultFullNameRel.positionInstanceId",
					"_analyteInstanceId":"$.testResultList[*].testResult.testResultFullNameRel.analyteInstanceId",
					"_testMatrix":"#getReportByTestMatrixIdFn($.reportList,@._testMatrixId,4)",
					"_testLine":"$.testLineList[testLineInstanceId=@._testLineInstanceId][0]",
            "reportNo": "#getReportByTestMatrixIdFn($.reportList,@._testMatrixId,1)",
            "testInstitutionCode": "#mapping($.reportList[reportNo=@.reportNo][0].lab.labCode,''HK SL'':''sgs001''&''SH SL'':''sgs002''&''QD SL'':''sgs003''&''GZ SL'':''sgs004''&''CZ SL'':''sgs005'')",
            "testType": "#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._testSampleInstanceId,SpecialCustomerAttribute2,1)",
            "testTargetCode": "#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._testSampleInstanceId,SpecialProductAttribute2,1)",
            "colorCode":"#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._testSampleInstanceId,ProductColor,1)",
            "colorName": "#uNIQLODffGridFn($.testSampleList,$.orderList.sampleList,@._testSampleInstanceId,SpecialCustomerAttribute1,1)",
            "testItemKeyCode": "#uNIQLOTestLineMappingFn($.testLineList[testLineInstanceId=@._testLineInstanceId][0],1)",
            "testMethodCode": "#uNIQLOTestLineMappingFn($.testLineList[testLineInstanceId=@._testLineInstanceId][0],2)",
            "jugdementDevisionCode": "#uNIQLOTestLineMappingFn($.testLineList[testLineInstanceId=@._testLineInstanceId][0],3,SL,@._analyteInstanceId)",
            "testObjectNo": "$.testSampleList[testSampleInstanceId=@._testSampleInstanceId][0].testSampleNo",
            "testActualColorName": "$.testSampleList[testSampleInstanceId=@._testSampleInstanceId][0].materialAttr.materialColor",
            "testCondition1": "#uNIQLOGetValueFormListFn(@._testMatrix,@._positionInstanceId,positionId)",
            "testCondition2": null,
            "testCondition3": null,
            "testCondition4": "#uNIQLOGetValueFormListFn(@._testLine,@._analyteInstanceId,analyteId)",
            "testCondition5": null,
            "testCondition6": null,
            "unit": "#uNIQLOUnitMappingFn($.testResultList[*],SL)",
            "testValue": "$.testResultList[*].testResult.resultValue",
            "result": null,
            "comment": null
          }
        ]
      }
    }
  },
	"action": "SyncReport",
  "dataStatus": "Completed",
  "bu": "$.extra.productLineCode",
  "objectNumber": "$.extra.trfNo",
  "refSystemId": "$.extra.refSystemId",
  "msgId": "$.extra.randomSequence"
}');


INSERT INTO `tb_dict_value`(`id`, `system_id`, `dict_type`, `dict_value`, `dict_label`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (23, 40, 'RefSystemId', '10017', '优衣库系统', 1, 'system', '2023-10-26 11:22:10', 'system', '2023-10-26 11:22:16', '2023-10-26 11:22:20');

ALTER TABLE `tb_trf_report`
    ADD COLUMN `origin_report_no` varchar(50) NULL COMMENT 'originReportNo' AFTER `report_no`;

INSERT INTO tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (19, NULL, NULL, '10017', 'SL', 2, 'OriginReportDataMode', '1', 'system', '2023-11-08 19:25:19', 'system', '2023-11-08 19:25:25', '2023-11-08 19:25:25');


UPDATE `tb_cfg_info` SET  `config_value` = '{\r\n    \"trfOrderRelationshipRule\":4,\r\n    \"statusRule\":0,\r\n    \"statusFlow\":[\r\n        1,\r\n        3,\r\n        4,\r\n        5,\r\n        6,\r\n        7,\r\n        100\r\n    ],\r\n    \"statusMapping\":{\r\n        \"1\":1,\r\n        \"3\":3,\r\n        \"4\":4,\r\n        \"5\":5,\r\n        \"6\":6,\r\n        \"7\":7,\r\n        \"9\":9,\r\n        \"100\":100\r\n    },\r\n    \"actionStatusMapping\":{\r\n        \"Import\":{\r\n            \"orderRefRule\":0,\r\n            \"orderOpType\":1,\r\n            \"ctrlMapping\":{\r\n\r\n            }\r\n        },\r\n        \"SyncToOrder\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":1,\r\n            \"ctrlMapping\":{\r\n                \"3\":0\r\n            }\r\n        },\r\n        \"SyncConfirmed\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"4\":0\r\n            }\r\n        },\r\n        \"SyncTesting\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":1,\r\n            \"ctrlMapping\":{\r\n                \"5\":0\r\n            }\r\n        },\r\n        \"SyncCompleted\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"6\":0\r\n            }\r\n        },\r\n        \"SyncClosed\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"7\":0\r\n            }\r\n        },\r\n        \"SyncUnBind\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"3\":0,\r\n                \"4\":0,\r\n                \"5\":0\r\n            }\r\n        },\r\n        \"PreOrderReturnTrf\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":3,\r\n            \"ctrlMapping\":{\r\n                \"1\":0\r\n            }\r\n        },\r\n        \"CustomerReturnTrf\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":3,\r\n            \"ctrlMapping\":{\r\n                \"1\":0\r\n            }\r\n        },\r\n        \"SyncReviseReport\":{\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"100\":0\r\n            }\r\n        },\r\n        \"SyncPending\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"1\":1,\r\n                \"3\":3,\r\n                \"4\":4,\r\n                \"5\":5\r\n            }\r\n        },\r\n        \"SyncUnPending\":{\r\n            \"orderRefRule\":1,\r\n            \"orderOpType\":7,\r\n            \"ctrlMapping\":{\r\n                \"1\":1,\r\n                \"3\":3,\r\n                \"4\":4,\r\n                \"5\":5\r\n            }\r\n        }\r\n    }\r\n}' WHERE `id` = 6;
