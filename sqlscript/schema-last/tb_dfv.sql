/*
 Navicat Premium Data Transfer

 Source Server         : SODA-order-UAT
 Source Server Type    : MySQL
 Source Server Version : 50647
 Source Host           : sgsdbcne2mysqlarchmasteruat.mysql.database.chinacloudapi.cn:3306
 Source Schema         : preorder

 Target Server Type    : MySQL
 Target Server Version : 50647
 File Encoding         : 65001

 Date: 26/05/2023 18:50:57
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tb_dfv_field_constraint
-- ----------------------------

CREATE TABLE `tb_dfv_field_constraint`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `system_id` int(11) NOT NULL COMMENT '归属系统id',
  `function_id` bigint(20) NOT NULL COMMENT '功能id',
  `field_id` bigint(20) NOT NULL COMMENT '属性id',
  `field_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '约束类型',
  `constraint_value1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '约束条件值1',
  `constraint_value2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '约束条件值2',
  `sort_num` int(11) NOT NULL COMMENT '排序',
  `field_id_ref` bigint(20) NOT NULL COMMENT '关联属性：例如A属性的值要大于B属性值的约束时，需要指定B属性的属性id',
  `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NOT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NOT NULL COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uidx_func_field_constraint_type`(`function_id`, `field_id`, `type`) USING BTREE,
  INDEX `idx_created_date`(`created_date`) USING BTREE,
  INDEX `idx_func_id`(`system_id`, `function_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'DFV-功能-入参属性规则' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_dfv_func_field
-- ----------------------------

CREATE TABLE `tb_dfv_func_field`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `system_id` int(11) NOT NULL COMMENT '归属系统id',
  `function_id` bigint(20) NOT NULL COMMENT '所属功能id',
  `full_field_path` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性全路径，与field_path不同，当对象tree某个节点出现集合类型时，full_field_path会描述整个路径，而field path只从集合的item开始，例如\r\ntrf.orderList.orderNo，fullFieldPath为trf.orderList[].orderNo，而fieldPath为orderNo',
  `field_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '属性路径，由英文逗号链接的对象树父子关系，例如trf.order.lab.labCode',
  `field_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '属性code',
  `field_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '属性类型：\r\ncollection，string，integer，long，digits，object, boolean,  date',
  `label_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '属性名称',
  `sort_num` int(11) NOT NULL COMMENT '排序',
  `parent_id` bigint(20) NOT NULL COMMENT '父属性id，0为无父属性，其他表示父属性id',
  `active_indicator` int(1) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NOT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NOT NULL COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `uidx_func_field`(`system_id`, `function_id`, `field_code`) USING BTREE,
  INDEX `idx_created_date`(`created_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 634 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'DFV-功能-入参属性' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_dfv_func_field
-- ----------------------------
INSERT INTO `tb_dfv_func_field` VALUES (1, 40, 1, 'action', 'action', 'action', 'string', 'Action', 1, 0, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (2, 40, 1, 'header', 'header', 'header', 'object', 'Header', 1, 0, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (3, 40, 1, 'invoiceList', 'invoiceList', 'invoiceList', 'collection', 'InvoiceList', 1, 0, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (4, 40, 1, 'order', 'order', 'order', 'object', 'Order', 1, 0, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (5, 40, 1, 'others', 'others', 'others', 'object', 'Others', 1, 0, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (6, 40, 1, 'quotationList', 'quotationList', 'quotationList', 'collection', 'QuotationList', 1, 0, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (7, 40, 1, 'reportList', 'reportList', 'reportList', 'collection', 'ReportList', 1, 0, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (8, 40, 1, 'testLineList', 'testLineList', 'testLineList', 'collection', 'TestLineList', 1, 0, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (9, 40, 1, 'testResultList', 'testResultList', 'testResultList', 'collection', 'TestResultList', 1, 0, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (10, 40, 1, 'testSampleList', 'testSampleList', 'testSampleList', 'collection', 'TestSampleList', 1, 0, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (11, 40, 1, 'header.refSystemId', 'header.refSystemId', 'refSystemId', 'integer', 'RefSystemId', 1, 2, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (12, 40, 1, 'header.trfList', 'header.trfList', 'trfList', 'collection', 'TrfList', 1, 2, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (13, 40, 1, 'header.trfNo', 'header.trfNo', 'trfNo', 'string', 'TrfNo', 1, 2, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (14, 40, 1, 'header.trfList.trfNo', 'trfNo', 'trfNo', 'string', 'TrfNo', 1, 12, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (15, 40, 1, 'order.attachmentList', 'order.attachmentList', 'attachmentList', 'collection', 'AttachmentList', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (16, 40, 1, 'order.contactPersonList', 'order.contactPersonList', 'contactPersonList', 'collection', 'ContactPersonList', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (17, 40, 1, 'order.createBy', 'order.createBy', 'createBy', 'string', 'CreateBy', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (18, 40, 1, 'order.createDate', 'order.createDate', 'createDate', 'date', 'CreateDate', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (19, 40, 1, 'order.customerList', 'order.customerList', 'customerList', 'collection', 'CustomerList', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (20, 40, 1, 'order.cuttingExpectDueDate', 'order.cuttingExpectDueDate', 'cuttingExpectDueDate', 'date', 'CuttingExpectDueDate', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (21, 40, 1, 'order.flags', 'order.flags', 'flags', 'object', 'Flags', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (22, 40, 1, 'order.groupId', 'order.groupId', 'groupId', 'integer', 'GroupId', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (23, 40, 1, 'order.idbLab', 'order.idbLab', 'idbLab', 'string', 'IdbLab', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (24, 40, 1, 'order.jobExpectDueDate', 'order.jobExpectDueDate', 'jobExpectDueDate', 'date', 'JobExpectDueDate', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (25, 40, 1, 'order.operationMode', 'order.operationMode', 'operationMode', 'integer', 'OperationMode', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (26, 40, 1, 'order.operationType', 'order.operationType', 'operationType', 'integer', 'OperationType', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (27, 40, 1, 'order.orderExpectDueDate', 'order.orderExpectDueDate', 'orderExpectDueDate', 'date', 'OrderExpectDueDate', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (28, 40, 1, 'order.orderId', 'order.orderId', 'orderId', 'string', 'OrderId', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (29, 40, 1, 'order.orderNo', 'order.orderNo', 'orderNo', 'string', 'OrderNo', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (30, 40, 1, 'order.orderStatus', 'order.orderStatus', 'orderStatus', 'integer', 'OrderStatus', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (31, 40, 1, 'order.orderType', 'order.orderType', 'orderType', 'string', 'OrderType', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (32, 40, 1, 'order.originalOrderNo', 'order.originalOrderNo', 'originalOrderNo', 'string', 'OriginalOrderNo', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (33, 40, 1, 'order.others', 'order.others', 'others', 'object', 'Others', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (34, 40, 1, 'order.payment', 'order.payment', 'payment', 'object', 'Payment', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (35, 40, 1, 'order.product', 'order.product', 'product', 'object', 'Product', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (36, 40, 1, 'order.productCategory', 'order.productCategory', 'productCategory', 'string', 'ProductCategory', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (37, 40, 1, 'order.productSubCategory', 'order.productSubCategory', 'productSubCategory', 'string', 'ProductSubCategory', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (38, 40, 1, 'order.reportExpectDueDate', 'order.reportExpectDueDate', 'reportExpectDueDate', 'date', 'ReportExpectDueDate', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (39, 40, 1, 'order.sampleList', 'order.sampleList', 'sampleList', 'collection', 'SampleList', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (40, 40, 1, 'order.serviceConfirmDate', 'order.serviceConfirmDate', 'serviceConfirmDate', 'date', 'ServiceConfirmDate', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (41, 40, 1, 'order.serviceRequirement', 'order.serviceRequirement', 'serviceRequirement', 'object', 'ServiceRequirement', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (42, 40, 1, 'order.serviceStartDate', 'order.serviceStartDate', 'serviceStartDate', 'date', 'ServiceStartDate', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (43, 40, 1, 'order.serviceType', 'order.serviceType', 'serviceType', 'integer', 'ServiceType', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (44, 40, 1, 'order.softCopyDeliveryDate', 'order.softCopyDeliveryDate', 'softCopyDeliveryDate', 'date', 'SoftCopyDeliveryDate', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (45, 40, 1, 'order.subcontractExpectDueDate', 'order.subcontractExpectDueDate', 'subcontractExpectDueDate', 'date', 'SubcontractExpectDueDate', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (46, 40, 1, 'order.systemId', 'order.systemId', 'systemId', 'integer', 'SystemId', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (47, 40, 1, 'order.tat', 'order.tat', 'tat', 'integer', 'Tat', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (48, 40, 1, 'order.testingEndDate', 'order.testingEndDate', 'testingEndDate', 'date', 'TestingEndDate', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (49, 40, 1, 'order.testingStartDate', 'order.testingStartDate', 'testingStartDate', 'date', 'TestingStartDate', 1, 4, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (50, 40, 1, 'order.payment.currency', 'order.payment.currency', 'currency', 'string', 'Currency', 1, 34, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (51, 40, 1, 'order.payment.mainCurrencyTotalAmount', 'order.payment.mainCurrencyTotalAmount', 'mainCurrencyTotalAmount', 'string', 'MainCurrencyTotalAmount', 1, 34, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (52, 40, 1, 'order.payment.paymentStatus', 'order.payment.paymentStatus', 'paymentStatus', 'integer', 'PaymentStatus', 1, 34, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (53, 40, 1, 'order.payment.totalAmount', 'order.payment.totalAmount', 'totalAmount', 'string', 'TotalAmount', 1, 34, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (54, 40, 1, 'order.contactPersonList.bossContactId', 'bossContactId', 'bossContactId', 'long', 'BossContactId', 1, 16, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 19:26:42');
INSERT INTO `tb_dfv_func_field` VALUES (55, 40, 1, 'order.contactPersonList.bossSiteUseId', 'bossSiteUseId', 'bossSiteUseId', 'long', 'BossSiteUseId', 1, 16, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 19:26:56');
INSERT INTO `tb_dfv_func_field` VALUES (56, 40, 1, 'order.contactPersonList.contactAddressId', 'contactAddressId', 'contactAddressId', 'string', 'ContactAddressId', 1, 16, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 19:26:57');
INSERT INTO `tb_dfv_func_field` VALUES (57, 40, 1, 'order.contactPersonList.contactEmail', 'contactEmail', 'contactEmail', 'string', 'ContactEmail', 1, 16, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 19:26:57');
INSERT INTO `tb_dfv_func_field` VALUES (58, 40, 1, 'order.contactPersonList.contactFax', 'contactFax', 'contactFax', 'string', 'ContactFax', 1, 16, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 19:26:57');
INSERT INTO `tb_dfv_func_field` VALUES (59, 40, 1, 'order.contactPersonList.contactId', 'contactId', 'contactId', 'string', 'ContactId', 1, 16, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 19:26:57');
INSERT INTO `tb_dfv_func_field` VALUES (60, 40, 1, 'order.contactPersonList.contactName', 'contactName', 'contactName', 'string', 'ContactName', 1, 16, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 19:26:57');
INSERT INTO `tb_dfv_func_field` VALUES (61, 40, 1, 'order.contactPersonList.contactPhone', 'contactPhone', 'contactPhone', 'string', 'ContactPhone', 1, 16, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 19:26:57');
INSERT INTO `tb_dfv_func_field` VALUES (62, 40, 1, 'order.contactPersonList.contactRegionAccount', 'contactRegionAccount', 'contactRegionAccount', 'string', 'ContactRegionAccount', 1, 16, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 19:26:57');
INSERT INTO `tb_dfv_func_field` VALUES (63, 40, 1, 'order.contactPersonList.contactTelephone', 'contactTelephone', 'contactTelephone', 'string', 'ContactTelephone', 1, 16, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 19:26:57');
INSERT INTO `tb_dfv_func_field` VALUES (64, 40, 1, 'order.contactPersonList.contactUsage', 'contactUsage', 'contactUsage', 'integer', 'ContactUsage', 1, 16, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 19:26:57');
INSERT INTO `tb_dfv_func_field` VALUES (65, 40, 1, 'order.contactPersonList.responsibleTeamCode', 'responsibleTeamCode', 'responsibleTeamCode', 'string', 'ResponsibleTeamCode', 1, 16, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 19:26:57');
INSERT INTO `tb_dfv_func_field` VALUES (66, 40, 1, 'order.flags.selfTestFlag', 'order.flags.selfTestFlag', 'selfTestFlag', 'integer', 'SelfTestFlag', 1, 21, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 19:24:59');
INSERT INTO `tb_dfv_func_field` VALUES (67, 40, 1, 'order.flags.toDMFlag', 'order.flags.toDMFlag', 'toDMFlag', 'integer', 'ToDMFlag', 1, 21, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 19:25:01');
INSERT INTO `tb_dfv_func_field` VALUES (72, 40, 1, 'order.others.orderRemark', 'order.others.orderRemark', 'orderRemark', 'string', 'OrderRemark', 1, 33, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 19:24:31');
INSERT INTO `tb_dfv_func_field` VALUES (73, 40, 1, 'order.others.pending', 'order.others.pending', 'pending', 'object', 'Pending', 1, 33, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 19:24:32');
INSERT INTO `tb_dfv_func_field` VALUES (74, 40, 1, 'order.others.pending.pendingFlag', 'order.others.pending.pendingFlag', 'pendingFlag', 'integer', 'PendingFlag', 1, 73, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 19:24:07');
INSERT INTO `tb_dfv_func_field` VALUES (75, 40, 1, 'order.others.pending.pendingRemark', 'order.others.pending.pendingRemark', 'pendingRemark', 'string', 'PendingRemark', 1, 73, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 19:24:08');
INSERT INTO `tb_dfv_func_field` VALUES (76, 40, 1, 'order.others.pending.pendingType', 'order.others.pending.pendingType', 'pendingType', 'string', 'PendingType', 1, 73, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 19:24:10');
INSERT INTO `tb_dfv_func_field` VALUES (77, 40, 1, 'order.customerList.blackFlag', 'blackFlag', 'blackFlag', 'integer', 'BlackFlag', 1, 19, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (78, 40, 1, 'order.customerList.bossNo', 'bossNo', 'bossNo', 'long', 'BossNo', 1, 19, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (79, 40, 1, 'order.customerList.customerAddress', 'customerAddress', 'customerAddress', 'string', 'CustomerAddress', 1, 19, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (80, 40, 1, 'order.customerList.customerContactList', 'customerContactList', 'customerContactList', 'collection', 'CustomerContactList', 1, 19, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (81, 40, 1, 'order.customerList.customerGroupCode', 'customerGroupCode', 'customerGroupCode', 'string', 'CustomerGroupCode', 1, 19, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (82, 40, 1, 'order.customerList.customerId', 'customerId', 'customerId', 'string', 'CustomerId', 1, 19, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (83, 40, 1, 'order.customerList.customerInstanceId', 'customerInstanceId', 'customerInstanceId', 'string', 'CustomerInstanceId', 1, 19, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (84, 40, 1, 'order.customerList.customerName', 'customerName', 'customerName', 'string', 'CustomerName', 1, 19, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (85, 40, 1, 'order.customerList.customerUsage', 'customerUsage', 'customerUsage', 'integer', 'CustomerUsage', 1, 19, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (86, 40, 1, 'order.customerList.languageList', 'languageList', 'languageList', 'collection', 'LanguageList', 1, 19, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (87, 40, 1, 'order.customerList.paymentTerm', 'paymentTerm', 'paymentTerm', 'string', 'PaymentTerm', 1, 19, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (88, 40, 1, 'order.customerList.customerContactList.bossContactId', 'bossContactId', 'bossContactId', 'long', 'BossContactId', 1, 80, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (89, 40, 1, 'order.customerList.customerContactList.bossSiteUseId', 'bossSiteUseId', 'bossSiteUseId', 'long', 'BossSiteUseId', 1, 80, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (90, 40, 1, 'order.customerList.customerContactList.contactAddressId', 'contactAddressId', 'contactAddressId', 'string', 'ContactAddressId', 1, 80, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (91, 40, 1, 'order.customerList.customerContactList.contactEmail', 'contactEmail', 'contactEmail', 'string', 'ContactEmail', 1, 80, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (92, 40, 1, 'order.customerList.customerContactList.contactFax', 'contactFax', 'contactFax', 'string', 'ContactFax', 1, 80, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (93, 40, 1, 'order.customerList.customerContactList.contactId', 'contactId', 'contactId', 'string', 'ContactId', 1, 80, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (94, 40, 1, 'order.customerList.customerContactList.contactName', 'contactName', 'contactName', 'string', 'ContactName', 1, 80, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (95, 40, 1, 'order.customerList.customerContactList.contactPhone', 'contactPhone', 'contactPhone', 'string', 'ContactPhone', 1, 80, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (96, 40, 1, 'order.customerList.customerContactList.contactRegionAccount', 'contactRegionAccount', 'contactRegionAccount', 'string', 'ContactRegionAccount', 1, 80, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (97, 40, 1, 'order.customerList.customerContactList.contactTelephone', 'contactTelephone', 'contactTelephone', 'string', 'ContactTelephone', 1, 80, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (98, 40, 1, 'order.customerList.customerContactList.contactUsage', 'contactUsage', 'contactUsage', 'integer', 'ContactUsage', 1, 80, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (99, 40, 1, 'order.customerList.customerContactList.responsibleTeamCode', 'responsibleTeamCode', 'responsibleTeamCode', 'string', 'ResponsibleTeamCode', 1, 80, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (100, 40, 1, 'order.customerList.languageList.customerAddress', 'customerAddress', 'customerAddress', 'string', 'CustomerAddress', 1, 86, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (101, 40, 1, 'order.customerList.languageList.customerName', 'customerName', 'customerName', 'string', 'CustomerName', 1, 86, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (102, 40, 1, 'order.customerList.languageList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 86, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (103, 40, 1, 'order.product.productAttrList', 'order.product.productAttrList', 'productAttrList', 'collection', 'ProductAttrList', 1, 35, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (104, 40, 1, 'order.product.templateId', 'order.product.templateId', 'templateId', 'string', 'TemplateId', 1, 35, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (105, 40, 1, 'order.product.productAttrList.attrSeq', 'attrSeq', 'attrSeq', 'integer', 'AttrSeq', 1, 103, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (106, 40, 1, 'order.product.productAttrList.customerLabel', 'customerLabel', 'customerLabel', 'string', 'CustomerLabel', 1, 103, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (107, 40, 1, 'order.product.productAttrList.dataType', 'dataType', 'dataType', 'string', 'DataType', 1, 103, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (108, 40, 1, 'order.product.productAttrList.fieldCode', 'fieldCode', 'fieldCode', 'string', 'FieldCode', 1, 103, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (109, 40, 1, 'order.product.productAttrList.labelCode', 'labelCode', 'labelCode', 'string', 'LabelCode', 1, 103, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (110, 40, 1, 'order.product.productAttrList.labelName', 'labelName', 'labelName', 'string', 'LabelName', 1, 103, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (111, 40, 1, 'order.product.productAttrList.labelValue', 'labelValue', 'labelValue', 'string', 'LabelValue', 1, 103, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (112, 40, 1, 'order.product.productAttrList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 103, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (113, 40, 1, 'order.product.productAttrList.languageList', 'languageList', 'languageList', 'collection', 'LanguageList', 1, 103, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (114, 40, 1, 'order.product.productAttrList.productInstanceId', 'productInstanceId', 'productInstanceId', 'string', 'ProductInstanceId', 1, 103, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (125, 40, 1, 'order.product.productAttrList.languageList.customerLabel', 'customerLabel', 'customerLabel', 'string', 'CustomerLabel', 1, 113, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (126, 40, 1, 'order.product.productAttrList.languageList.labelName', 'labelName', 'labelName', 'string', 'LabelName', 1, 113, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (127, 40, 1, 'order.product.productAttrList.languageList.labelValue', 'labelValue', 'labelValue', 'string', 'LabelValue', 1, 113, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (128, 40, 1, 'order.product.productAttrList.languageList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 113, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (129, 40, 1, 'order.sampleList.conclusion', 'conclusion', 'conclusion', 'object', 'Conclusion', 1, 39, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (130, 40, 1, 'order.sampleList.sampleAttrList', 'sampleAttrList', 'sampleAttrList', 'collection', 'SampleAttrList', 1, 39, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (131, 40, 1, 'order.sampleList.sampleInstanceId', 'sampleInstanceId', 'sampleInstanceId', 'string', 'SampleInstanceId', 1, 39, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (132, 40, 1, 'order.sampleList.templateId', 'templateId', 'templateId', 'string', 'TemplateId', 1, 39, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (133, 40, 1, 'order.sampleList.sampleAttrList.attrSeq', 'attrSeq', 'attrSeq', 'integer', 'AttrSeq', 1, 130, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (134, 40, 1, 'order.sampleList.sampleAttrList.customerLabel', 'customerLabel', 'customerLabel', 'string', 'CustomerLabel', 1, 130, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (135, 40, 1, 'order.sampleList.sampleAttrList.dataType', 'dataType', 'dataType', 'string', 'DataType', 1, 130, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (136, 40, 1, 'order.sampleList.sampleAttrList.fieldCode', 'fieldCode', 'fieldCode', 'string', 'FieldCode', 1, 130, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (137, 40, 1, 'order.sampleList.sampleAttrList.labelCode', 'labelCode', 'labelCode', 'string', 'LabelCode', 1, 130, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (138, 40, 1, 'order.sampleList.sampleAttrList.labelName', 'labelName', 'labelName', 'string', 'LabelName', 1, 130, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (139, 40, 1, 'order.sampleList.sampleAttrList.labelValue', 'labelValue', 'labelValue', 'string', 'LabelValue', 1, 130, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (140, 40, 1, 'order.sampleList.sampleAttrList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 130, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (141, 40, 1, 'order.sampleList.sampleAttrList.languageList', 'languageList', 'languageList', 'collection', 'LanguageList', 1, 130, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (142, 40, 1, 'order.sampleList.sampleAttrList.productInstanceId', 'productInstanceId', 'productInstanceId', 'string', 'ProductInstanceId', 1, 130, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (143, 40, 1, 'order.sampleList.sampleAttrList.languageList.customerLabel', 'customerLabel', 'customerLabel', 'string', 'CustomerLabel', 1, 141, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (144, 40, 1, 'order.sampleList.sampleAttrList.languageList.labelName', 'labelName', 'labelName', 'string', 'LabelName', 1, 141, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (145, 40, 1, 'order.sampleList.sampleAttrList.languageList.labelValue', 'labelValue', 'labelValue', 'string', 'LabelValue', 1, 141, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (146, 40, 1, 'order.sampleList.sampleAttrList.languageList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 141, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (147, 40, 1, 'order.serviceRequirement.invoice', 'order.serviceRequirement.invoice', 'invoice', 'object', 'Invoice', 1, 41, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (148, 40, 1, 'order.serviceRequirement.otherRequestRemark', 'order.serviceRequirement.otherRequestRemark', 'otherRequestRemark', 'string', 'OtherRequestRemark', 1, 41, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (149, 40, 1, 'order.serviceRequirement.report', 'order.serviceRequirement.report', 'report', 'object', 'Report', 1, 41, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (150, 40, 1, 'order.serviceRequirement.sample', 'order.serviceRequirement.sample', 'sample', 'object', 'Sample', 1, 41, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (151, 40, 1, 'order.serviceRequirement.report.accreditation', 'order.serviceRequirement.report.accreditation', 'accreditation', 'string', 'Accreditation', 1, 149, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (152, 40, 1, 'order.serviceRequirement.report.hardcopy', 'order.serviceRequirement.report.hardcopy', 'hardcopy', 'object', 'Hardcopy', 1, 149, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (153, 40, 1, 'order.serviceRequirement.report.languageList', 'order.serviceRequirement.report.languageList', 'languageList', 'collection', 'LanguageList', 1, 149, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (154, 40, 1, 'order.serviceRequirement.report.needConclusion', 'order.serviceRequirement.report.needConclusion', 'needConclusion', 'integer', 'NeedConclusion', 1, 149, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (155, 40, 1, 'order.serviceRequirement.report.needDraft', 'order.serviceRequirement.report.needDraft', 'needDraft', 'integer', 'NeedDraft', 1, 149, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (156, 40, 1, 'order.serviceRequirement.report.needPhoto', 'order.serviceRequirement.report.needPhoto', 'needPhoto', 'integer', 'NeedPhoto', 1, 149, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (157, 40, 1, 'order.serviceRequirement.report.reportAddress', 'order.serviceRequirement.report.reportAddress', 'reportAddress', 'string', 'ReportAddress', 1, 149, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (158, 40, 1, 'order.serviceRequirement.report.reportHeader', 'order.serviceRequirement.report.reportHeader', 'reportHeader', 'string', 'ReportHeader', 1, 149, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (159, 40, 1, 'order.serviceRequirement.report.reportLanguage', 'order.serviceRequirement.report.reportLanguage', 'reportLanguage', 'integer', 'ReportLanguage', 1, 149, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (160, 40, 1, 'order.serviceRequirement.report.softcopy', 'order.serviceRequirement.report.softcopy', 'softcopy', 'object', 'Softcopy', 1, 149, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (161, 40, 1, 'order.serviceRequirement.report.languageList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 153, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (162, 40, 1, 'order.serviceRequirement.report.languageList.reportAddress', 'reportAddress', 'reportAddress', 'string', 'ReportAddress', 1, 153, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (163, 40, 1, 'order.serviceRequirement.report.languageList.reportHeader', 'reportHeader', 'reportHeader', 'string', 'ReportHeader', 1, 153, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (164, 40, 1, 'order.serviceRequirement.report.softcopy.deliveryCc', 'order.serviceRequirement.report.softcopy.deliveryCc', 'deliveryCc', 'string', 'DeliveryCc', 1, 160, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (165, 40, 1, 'order.serviceRequirement.report.softcopy.deliveryOthers', 'order.serviceRequirement.report.softcopy.deliveryOthers', 'deliveryOthers', 'string', 'DeliveryOthers', 1, 160, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (166, 40, 1, 'order.serviceRequirement.report.softcopy.deliveryTo', 'order.serviceRequirement.report.softcopy.deliveryTo', 'deliveryTo', 'string', 'DeliveryTo', 1, 160, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (167, 40, 1, 'order.serviceRequirement.report.softcopy.deliveryWay', 'order.serviceRequirement.report.softcopy.deliveryWay', 'deliveryWay', 'string', 'DeliveryWay', 1, 160, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (168, 40, 1, 'order.serviceRequirement.report.softcopy.required', 'order.serviceRequirement.report.softcopy.required', 'required', 'integer', 'Required', 1, 160, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (169, 40, 1, 'order.serviceRequirement.report.hardcopy.deliveryCc', 'order.serviceRequirement.report.hardcopy.deliveryCc', 'deliveryCc', 'string', 'DeliveryCc', 1, 152, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (170, 40, 1, 'order.serviceRequirement.report.hardcopy.deliveryOthers', 'order.serviceRequirement.report.hardcopy.deliveryOthers', 'deliveryOthers', 'string', 'DeliveryOthers', 1, 152, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (171, 40, 1, 'order.serviceRequirement.report.hardcopy.deliveryTo', 'order.serviceRequirement.report.hardcopy.deliveryTo', 'deliveryTo', 'string', 'DeliveryTo', 1, 152, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (172, 40, 1, 'order.serviceRequirement.report.hardcopy.deliveryWay', 'order.serviceRequirement.report.hardcopy.deliveryWay', 'deliveryWay', 'string', 'DeliveryWay', 1, 152, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (173, 40, 1, 'order.serviceRequirement.report.hardcopy.required', 'order.serviceRequirement.report.hardcopy.required', 'required', 'integer', 'Required', 1, 152, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (174, 40, 1, 'order.serviceRequirement.invoice.invoiceDelivery', 'order.serviceRequirement.invoice.invoiceDelivery', 'invoiceDelivery', 'object', 'InvoiceDelivery', 1, 147, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (175, 40, 1, 'order.serviceRequirement.invoice.invoiceType', 'order.serviceRequirement.invoice.invoiceType', 'invoiceType', 'integer', 'InvoiceType', 1, 147, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (176, 40, 1, 'order.serviceRequirement.invoice.invoiceDelivery.deliveryCc', 'order.serviceRequirement.invoice.invoiceDelivery.deliveryCc', 'deliveryCc', 'string', 'DeliveryCc', 1, 174, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (177, 40, 1, 'order.serviceRequirement.invoice.invoiceDelivery.deliveryOthers', 'order.serviceRequirement.invoice.invoiceDelivery.deliveryOthers', 'deliveryOthers', 'string', 'DeliveryOthers', 1, 174, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (178, 40, 1, 'order.serviceRequirement.invoice.invoiceDelivery.deliveryTo', 'order.serviceRequirement.invoice.invoiceDelivery.deliveryTo', 'deliveryTo', 'string', 'DeliveryTo', 1, 174, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (179, 40, 1, 'order.serviceRequirement.invoice.invoiceDelivery.deliveryWay', 'order.serviceRequirement.invoice.invoiceDelivery.deliveryWay', 'deliveryWay', 'string', 'DeliveryWay', 1, 174, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (180, 40, 1, 'order.serviceRequirement.invoice.invoiceDelivery.required', 'order.serviceRequirement.invoice.invoiceDelivery.required', 'required', 'integer', 'Required', 1, 174, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (181, 40, 1, 'order.serviceRequirement.sample.returnResidueSample', 'order.serviceRequirement.sample.returnResidueSample', 'returnResidueSample', 'object', 'ReturnResidueSample', 1, 150, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (182, 40, 1, 'order.serviceRequirement.sample.returnTestSample', 'order.serviceRequirement.sample.returnTestSample', 'returnTestSample', 'object', 'ReturnTestSample', 1, 150, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (183, 40, 1, 'order.serviceRequirement.sample.sampleSaveDuration', 'order.serviceRequirement.sample.sampleSaveDuration', 'sampleSaveDuration', 'integer', 'SampleSaveDuration', 1, 150, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (184, 40, 1, 'order.serviceRequirement.sample.returnTestSample.deliveryCc', 'order.serviceRequirement.sample.returnTestSample.deliveryCc', 'deliveryCc', 'string', 'DeliveryCc', 1, 182, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (185, 40, 1, 'order.serviceRequirement.sample.returnTestSample.deliveryOthers', 'order.serviceRequirement.sample.returnTestSample.deliveryOthers', 'deliveryOthers', 'string', 'DeliveryOthers', 1, 182, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (186, 40, 1, 'order.serviceRequirement.sample.returnTestSample.deliveryTo', 'order.serviceRequirement.sample.returnTestSample.deliveryTo', 'deliveryTo', 'string', 'DeliveryTo', 1, 182, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (187, 40, 1, 'order.serviceRequirement.sample.returnTestSample.deliveryWay', 'order.serviceRequirement.sample.returnTestSample.deliveryWay', 'deliveryWay', 'string', 'DeliveryWay', 1, 182, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (188, 40, 1, 'order.serviceRequirement.sample.returnTestSample.required', 'order.serviceRequirement.sample.returnTestSample.required', 'required', 'integer', 'Required', 1, 182, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (189, 40, 1, 'order.serviceRequirement.sample.returnResidueSample.deliveryCc', 'order.serviceRequirement.sample.returnResidueSample.deliveryCc', 'deliveryCc', 'string', 'DeliveryCc', 1, 181, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (190, 40, 1, 'order.serviceRequirement.sample.returnResidueSample.deliveryOthers', 'order.serviceRequirement.sample.returnResidueSample.deliveryOthers', 'deliveryOthers', 'string', 'DeliveryOthers', 1, 181, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (191, 40, 1, 'order.serviceRequirement.sample.returnResidueSample.deliveryTo', 'order.serviceRequirement.sample.returnResidueSample.deliveryTo', 'deliveryTo', 'string', 'DeliveryTo', 1, 181, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (192, 40, 1, 'order.serviceRequirement.sample.returnResidueSample.deliveryWay', 'order.serviceRequirement.sample.returnResidueSample.deliveryWay', 'deliveryWay', 'string', 'DeliveryWay', 1, 181, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (193, 40, 1, 'order.serviceRequirement.sample.returnResidueSample.required', 'order.serviceRequirement.sample.returnResidueSample.required', 'required', 'integer', 'Required', 1, 181, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (194, 40, 1, 'order.attachmentList.cloudId', 'cloudId', 'cloudId', 'string', 'CloudId', 1, 15, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (195, 40, 1, 'order.attachmentList.fileName', 'fileName', 'fileName', 'string', 'FileName', 1, 15, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (196, 40, 1, 'order.attachmentList.filePath', 'filePath', 'filePath', 'string', 'FilePath', 1, 15, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (197, 40, 1, 'order.attachmentList.fileSize', 'fileSize', 'fileSize', 'long', 'FileSize', 1, 15, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (198, 40, 1, 'order.attachmentList.fileType', 'fileType', 'fileType', 'integer', 'FileType', 1, 15, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (199, 40, 1, 'order.attachmentList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 15, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (200, 40, 1, 'testSampleList.conclusion', 'conclusion', 'conclusion', 'object', 'Conclusion', 1, 10, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (201, 40, 1, 'testSampleList.externalSampleNo', 'externalSampleNo', 'externalSampleNo', 'string', 'ExternalSampleNo', 1, 10, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (202, 40, 1, 'testSampleList.materialAttrList', 'materialAttrList', 'materialAttrList', 'collection', 'MaterialAttrList', 1, 10, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (203, 40, 1, 'testSampleList.parentTestSampleId', 'parentTestSampleId', 'parentTestSampleId', 'string', 'ParentTestSampleId', 1, 10, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (204, 40, 1, 'testSampleList.testSampleGroupList', 'testSampleGroupList', 'testSampleGroupList', 'collection', 'TestSampleGroupList', 1, 10, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (205, 40, 1, 'testSampleList.testSampleInstanceId', 'testSampleInstanceId', 'testSampleInstanceId', 'string', 'TestSampleInstanceId', 1, 10, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (206, 40, 1, 'testSampleList.testSampleNo', 'testSampleNo', 'testSampleNo', 'string', 'TestSampleNo', 1, 10, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (207, 40, 1, 'testSampleList.testSamplePhotoList', 'testSamplePhotoList', 'testSamplePhotoList', 'collection', 'TestSamplePhotoList', 1, 10, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (208, 40, 1, 'testSampleList.testSampleSeq', 'testSampleSeq', 'testSampleSeq', 'integer', 'TestSampleSeq', 1, 10, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (209, 40, 1, 'testSampleList.testSampleType', 'testSampleType', 'testSampleType', 'integer', 'TestSampleType', 1, 10, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (210, 40, 1, 'testSampleList.testSampleGroupList.mainSampleFlag', 'mainSampleFlag', 'mainSampleFlag', 'integer', 'MainSampleFlag', 1, 204, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (211, 40, 1, 'testSampleList.testSampleGroupList.testSampleInstanceId', 'testSampleInstanceId', 'testSampleInstanceId', 'string', 'TestSampleInstanceId', 1, 204, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (212, 40, 1, 'testSampleList.materialAttrList.extFields', 'extFields', 'extFields', 'object', 'ExtFields', 1, 202, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (213, 40, 1, 'testSampleList.materialAttrList.materialApplicableFlag', 'materialApplicableFlag', 'materialApplicableFlag', 'integer', 'MaterialApplicableFlag', 1, 202, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (214, 40, 1, 'testSampleList.materialAttrList.materialCategory', 'materialCategory', 'materialCategory', 'string', 'MaterialCategory', 1, 202, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (215, 40, 1, 'testSampleList.materialAttrList.materialColor', 'materialColor', 'materialColor', 'string', 'MaterialColor', 1, 202, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (216, 40, 1, 'testSampleList.materialAttrList.materialComposition', 'materialComposition', 'materialComposition', 'string', 'MaterialComposition', 1, 202, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (217, 40, 1, 'testSampleList.materialAttrList.materialDescription', 'materialDescription', 'materialDescription', 'string', 'MaterialDescription', 1, 202, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (218, 40, 1, 'testSampleList.materialAttrList.materialEndUse', 'materialEndUse', 'materialEndUse', 'string', 'MaterialEndUse', 1, 202, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (219, 40, 1, 'testSampleList.materialAttrList.materialItem', 'materialItem', 'materialItem', 'string', 'MaterialItem', 1, 202, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (220, 40, 1, 'testSampleList.materialAttrList.materialName', 'materialName', 'materialName', 'string', 'MaterialName', 1, 202, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (221, 40, 1, 'testSampleList.materialAttrList.materialOtherSampleInfo', 'materialOtherSampleInfo', 'materialOtherSampleInfo', 'string', 'MaterialOtherSampleInfo', 1, 202, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (222, 40, 1, 'testSampleList.materialAttrList.materialSampleRemark', 'materialSampleRemark', 'materialSampleRemark', 'string', 'MaterialSampleRemark', 1, 202, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (223, 40, 1, 'testSampleList.materialAttrList.materialSku', 'materialSku', 'materialSku', 'string', 'MaterialSku', 1, 202, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (224, 40, 1, 'testSampleList.materialAttrList.materialTexture', 'materialTexture', 'materialTexture', 'string', 'MaterialTexture', 1, 202, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (225, 40, 1, 'testSampleList.materialAttrList.extFields.materialCategory', 'extFields.materialCategory', 'materialCategory', 'string', 'MaterialCategory', 1, 212, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (226, 40, 1, 'testSampleList.materialAttrList.extFields.materialItem', 'extFields.materialItem', 'materialItem', 'string', 'MaterialItem', 1, 212, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (227, 40, 1, 'testSampleList.materialAttrList.extFields.materialSku', 'extFields.materialSku', 'materialSku', 'string', 'MaterialSku', 1, 212, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (228, 40, 1, 'testSampleList.conclusion.conclusionCode', 'conclusion.conclusionCode', 'conclusionCode', 'string', 'ConclusionCode', 1, 200, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (229, 40, 1, 'testSampleList.conclusion.conclusionId', 'conclusion.conclusionId', 'conclusionId', 'string', 'ConclusionId', 1, 200, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (230, 40, 1, 'testSampleList.conclusion.conclusionRemark', 'conclusion.conclusionRemark', 'conclusionRemark', 'string', 'ConclusionRemark', 1, 200, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (231, 40, 1, 'testSampleList.conclusion.customerConclusion', 'conclusion.customerConclusion', 'customerConclusion', 'string', 'CustomerConclusion', 1, 200, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (232, 40, 1, 'testSampleList.conclusion.customerConclusionId', 'conclusion.customerConclusionId', 'customerConclusionId', 'string', 'CustomerConclusionId', 1, 200, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (233, 40, 1, 'testSampleList.conclusion.reviewConclusion', 'conclusion.reviewConclusion', 'reviewConclusion', 'string', 'ReviewConclusion', 1, 200, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (234, 40, 1, 'testSampleList.testSamplePhotoList.cloudId', 'cloudId', 'cloudId', 'string', 'CloudId', 1, 207, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (235, 40, 1, 'testSampleList.testSamplePhotoList.fileName', 'fileName', 'fileName', 'string', 'FileName', 1, 207, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (236, 40, 1, 'testSampleList.testSamplePhotoList.filePath', 'filePath', 'filePath', 'string', 'FilePath', 1, 207, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (237, 40, 1, 'testSampleList.testSamplePhotoList.fileSize', 'fileSize', 'fileSize', 'long', 'FileSize', 1, 207, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (238, 40, 1, 'testSampleList.testSamplePhotoList.fileType', 'fileType', 'fileType', 'integer', 'FileType', 1, 207, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (239, 40, 1, 'testSampleList.testSamplePhotoList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 207, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (240, 40, 1, 'testLineList.analyteList', 'analyteList', 'analyteList', 'collection', 'AnalyteList', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (241, 40, 1, 'testLineList.citation', 'citation', 'citation', 'object', 'Citation', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (242, 40, 1, 'testLineList.citationFullName', 'citationFullName', 'citationFullName', 'string', 'CitationFullName', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (243, 40, 1, 'testLineList.citationId', 'citationId', 'citationId', 'integer', 'CitationId', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (244, 40, 1, 'testLineList.citationType', 'citationType', 'citationType', 'integer', 'CitationType', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (245, 40, 1, 'testLineList.conclusion', 'conclusion', 'conclusion', 'object', 'Conclusion', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (246, 40, 1, 'testLineList.evaluationAlias', 'evaluationAlias', 'evaluationAlias', 'string', 'EvaluationAlias', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (247, 40, 1, 'testLineList.evaluationName', 'evaluationName', 'evaluationName', 'string', 'EvaluationName', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (248, 40, 1, 'testLineList.externalInfo', 'externalInfo', 'externalInfo', 'object', 'ExternalInfo', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (249, 40, 1, 'testLineList.labSectionBaseId', 'labSectionBaseId', 'labSectionBaseId', 'integer', 'LabSectionBaseId', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (250, 40, 1, 'testLineList.labTeam', 'labTeam', 'labTeam', 'string', 'LabTeam', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (251, 40, 1, 'testLineList.languageList', 'languageList', 'languageList', 'collection', 'LanguageList', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (252, 40, 1, 'testLineList.ppName', 'ppName', 'ppName', 'string', 'PpName', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (253, 40, 1, 'testLineList.ppNo', 'ppNo', 'ppNo', 'integer', 'PpNo', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (254, 40, 1, 'testLineList.ppTestLineRelList', 'ppTestLineRelList', 'ppTestLineRelList', 'collection', 'PpTestLineRelList', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (255, 40, 1, 'testLineList.productLineAbbr', 'productLineAbbr', 'productLineAbbr', 'string', 'ProductLineAbbr', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (256, 40, 1, 'testLineList.testItemNo', 'testItemNo', 'testItemNo', 'string', 'TestItemNo', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (257, 40, 1, 'testLineList.testLineBaseId', 'testLineBaseId', 'testLineBaseId', 'integer', 'TestLineBaseId', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (258, 40, 1, 'testLineList.testLineId', 'testLineId', 'testLineId', 'integer', 'TestLineId', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (259, 40, 1, 'testLineList.testLineInstanceId', 'testLineInstanceId', 'testLineInstanceId', 'string', 'TestLineInstanceId', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (260, 40, 1, 'testLineList.testLineRemark', 'testLineRemark', 'testLineRemark', 'string', 'TestLineRemark', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (261, 40, 1, 'testLineList.testLineSeq', 'testLineSeq', 'testLineSeq', 'integer', 'TestLineSeq', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (262, 40, 1, 'testLineList.testLineStatus', 'testLineStatus', 'testLineStatus', 'integer', 'TestLineStatus', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (263, 40, 1, 'testLineList.testLineType', 'testLineType', 'testLineType', 'integer', 'TestLineType', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (264, 40, 1, 'testLineList.testLineVersionId', 'testLineVersionId', 'testLineVersionId', 'integer', 'TestLineVersionId', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (265, 40, 1, 'testLineList.wi', 'wi', 'wi', 'object', 'Wi', 1, 8, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (266, 40, 1, 'testLineList.languageList.citationFullName', 'citationFullName', 'citationFullName', 'string', 'CitationFullName', 1, 251, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (267, 40, 1, 'testLineList.languageList.evaluationAlias', 'evaluationAlias', 'evaluationAlias', 'string', 'EvaluationAlias', 1, 251, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (268, 40, 1, 'testLineList.languageList.evaluationName', 'evaluationName', 'evaluationName', 'string', 'EvaluationName', 1, 251, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (269, 40, 1, 'testLineList.languageList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 251, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (270, 40, 1, 'testLineList.languageList.ppName', 'ppName', 'ppName', 'string', 'PpName', 1, 251, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (271, 40, 1, 'testLineList.citation.citationFullName', 'citation.citationFullName', 'citationFullName', 'string', 'CitationFullName', 1, 241, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (272, 40, 1, 'testLineList.citation.citationId', 'citation.citationId', 'citationId', 'integer', 'CitationId', 1, 241, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (273, 40, 1, 'testLineList.citation.citationName', 'citation.citationName', 'citationName', 'string', 'CitationName', 1, 241, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (274, 40, 1, 'testLineList.citation.citationSectionId', 'citation.citationSectionId', 'citationSectionId', 'integer', 'CitationSectionId', 1, 241, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (275, 40, 1, 'testLineList.citation.citationSectionName', 'citation.citationSectionName', 'citationSectionName', 'string', 'CitationSectionName', 1, 241, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (276, 40, 1, 'testLineList.citation.citationType', 'citation.citationType', 'citationType', 'integer', 'CitationType', 1, 241, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (277, 40, 1, 'testLineList.citation.citationVersionId', 'citation.citationVersionId', 'citationVersionId', 'integer', 'CitationVersionId', 1, 241, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (278, 40, 1, 'testLineList.citation.languageList', 'citation.languageList', 'languageList', 'collection', 'LanguageList', 1, 241, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (279, 40, 1, 'testLineList.wi.wiForCs', 'wi.wiForCs', 'wiForCs', 'string', 'WiForCs', 1, 265, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (280, 40, 1, 'testLineList.wi.wiForSample', 'wi.wiForSample', 'wiForSample', 'string', 'WiForSample', 1, 265, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (281, 40, 1, 'testLineList.wi.wiForTest', 'wi.wiForTest', 'wiForTest', 'string', 'WiForTest', 1, 265, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (282, 40, 1, 'testLineList.analyteList.analyteBaseId', 'analyteBaseId', 'analyteBaseId', 'integer', 'AnalyteBaseId', 1, 240, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (283, 40, 1, 'testLineList.analyteList.analyteId', 'analyteId', 'analyteId', 'integer', 'AnalyteId', 1, 240, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (284, 40, 1, 'testLineList.analyteList.analyteInstanceId', 'analyteInstanceId', 'analyteInstanceId', 'string', 'AnalyteInstanceId', 1, 240, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (285, 40, 1, 'testLineList.analyteList.analyteLimitVersionId', 'analyteLimitVersionId', 'analyteLimitVersionId', 'integer', 'AnalyteLimitVersionId', 1, 240, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (286, 40, 1, 'testLineList.analyteList.analyteName', 'analyteName', 'analyteName', 'string', 'AnalyteName', 1, 240, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (287, 40, 1, 'testLineList.analyteList.analyteSeq', 'analyteSeq', 'analyteSeq', 'integer', 'AnalyteSeq', 1, 240, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (288, 40, 1, 'testLineList.analyteList.casNo', 'casNo', 'casNo', 'string', 'CasNo', 1, 240, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (289, 40, 1, 'testLineList.analyteList.languageList', 'languageList', 'languageList', 'collection', 'LanguageList', 1, 240, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (290, 40, 1, 'testLineList.analyteList.reportUnit', 'reportUnit', 'reportUnit', 'string', 'ReportUnit', 1, 240, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (291, 40, 1, 'testLineList.analyteList.unitBaseId', 'unitBaseId', 'unitBaseId', 'long', 'UnitBaseId', 1, 240, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (292, 40, 1, 'testLineList.analyteList.languageList.analyteName', 'analyteName', 'analyteName', 'string', 'AnalyteName', 1, 289, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (293, 40, 1, 'testLineList.analyteList.languageList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 289, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (294, 40, 1, 'testLineList.analyteList.languageList.reportUnit', 'reportUnit', 'reportUnit', 'string', 'ReportUnit', 1, 289, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (295, 40, 1, 'testLineList.ppTestLineRelList.aid', 'aid', 'aid', 'integer', 'Aid', 1, 254, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (296, 40, 1, 'testLineList.ppTestLineRelList.citation', 'citation', 'citation', 'object', 'Citation', 1, 254, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (297, 40, 1, 'testLineList.ppTestLineRelList.languageList', 'languageList', 'languageList', 'collection', 'LanguageList', 1, 254, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (298, 40, 1, 'testLineList.ppTestLineRelList.ppArtifactRelId', 'ppArtifactRelId', 'ppArtifactRelId', 'long', 'PpArtifactRelId', 1, 254, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (299, 40, 1, 'testLineList.ppTestLineRelList.ppBaseId', 'ppBaseId', 'ppBaseId', 'long', 'PpBaseId', 1, 254, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (300, 40, 1, 'testLineList.ppTestLineRelList.ppName', 'ppName', 'ppName', 'string', 'PpName', 1, 254, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (301, 40, 1, 'testLineList.ppTestLineRelList.ppNo', 'ppNo', 'ppNo', 'integer', 'PpNo', 1, 254, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (302, 40, 1, 'testLineList.ppTestLineRelList.ppNotes', 'ppNotes', 'ppNotes', 'string', 'PpNotes', 1, 254, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (303, 40, 1, 'testLineList.ppTestLineRelList.ppTlRelId', 'ppTlRelId', 'ppTlRelId', 'string', 'PpTlRelId', 1, 254, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (304, 40, 1, 'testLineList.ppTestLineRelList.ppVersionId', 'ppVersionId', 'ppVersionId', 'integer', 'PpVersionId', 1, 254, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (305, 40, 1, 'testLineList.ppTestLineRelList.rootPPBaseId', 'rootPPBaseId', 'rootPPBaseId', 'long', 'RootPPBaseId', 1, 254, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (306, 40, 1, 'testLineList.ppTestLineRelList.sectionId', 'sectionId', 'sectionId', 'integer', 'SectionId', 1, 254, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (307, 40, 1, 'testLineList.ppTestLineRelList.sectionLevel', 'sectionLevel', 'sectionLevel', 'integer', 'SectionLevel', 1, 254, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (308, 40, 1, 'testLineList.ppTestLineRelList.sectionName', 'sectionName', 'sectionName', 'string', 'SectionName', 1, 254, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (309, 40, 1, 'testLineList.ppTestLineRelList.languageList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 297, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (310, 40, 1, 'testLineList.ppTestLineRelList.languageList.ppName', 'ppName', 'ppName', 'string', 'PpName', 1, 297, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (311, 40, 1, 'testLineList.ppTestLineRelList.languageList.ppNotes', 'ppNotes', 'ppNotes', 'string', 'PpNotes', 1, 297, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (312, 40, 1, 'testLineList.ppTestLineRelList.languageList.sectionName', 'sectionName', 'sectionName', 'string', 'SectionName', 1, 297, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (313, 40, 1, 'testLineList.ppTestLineRelList.citation.citationFullName', 'citation.citationFullName', 'citationFullName', 'string', 'CitationFullName', 1, 296, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (314, 40, 1, 'testLineList.ppTestLineRelList.citation.citationId', 'citation.citationId', 'citationId', 'integer', 'CitationId', 1, 296, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (315, 40, 1, 'testLineList.ppTestLineRelList.citation.citationName', 'citation.citationName', 'citationName', 'string', 'CitationName', 1, 296, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (316, 40, 1, 'testLineList.ppTestLineRelList.citation.citationSectionId', 'citation.citationSectionId', 'citationSectionId', 'integer', 'CitationSectionId', 1, 296, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (317, 40, 1, 'testLineList.ppTestLineRelList.citation.citationSectionName', 'citation.citationSectionName', 'citationSectionName', 'string', 'CitationSectionName', 1, 296, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (318, 40, 1, 'testLineList.ppTestLineRelList.citation.citationType', 'citation.citationType', 'citationType', 'integer', 'CitationType', 1, 296, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (319, 40, 1, 'testLineList.ppTestLineRelList.citation.citationVersionId', 'citation.citationVersionId', 'citationVersionId', 'integer', 'CitationVersionId', 1, 296, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (320, 40, 1, 'testLineList.ppTestLineRelList.citation.languageList', 'citation.languageList', 'languageList', 'collection', 'LanguageList', 1, 296, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (321, 40, 1, 'testLineList.ppTestLineRelList.citation.languageList.citationFullName', 'citationFullName', 'citationFullName', 'string', 'CitationFullName', 1, 320, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (322, 40, 1, 'testLineList.ppTestLineRelList.citation.languageList.citationName', 'citationName', 'citationName', 'string', 'CitationName', 1, 320, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (323, 40, 1, 'testLineList.ppTestLineRelList.citation.languageList.citationSectionName', 'citationSectionName', 'citationSectionName', 'string', 'CitationSectionName', 1, 320, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (324, 40, 1, 'testLineList.ppTestLineRelList.citation.languageList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 320, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (325, 40, 1, 'testLineList.conclusion.conclusionCode', 'conclusion.conclusionCode', 'conclusionCode', 'string', 'ConclusionCode', 1, 246, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (326, 40, 1, 'testLineList.conclusion.conclusionId', 'conclusion.conclusionId', 'conclusionId', 'string', 'ConclusionId', 1, 246, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (327, 40, 1, 'testLineList.conclusion.conclusionRemark', 'conclusion.conclusionRemark', 'conclusionRemark', 'string', 'ConclusionRemark', 1, 246, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (328, 40, 1, 'testLineList.conclusion.customerConclusion', 'conclusion.customerConclusion', 'customerConclusion', 'string', 'CustomerConclusion', 1, 246, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (329, 40, 1, 'testLineList.conclusion.customerConclusionId', 'conclusion.customerConclusionId', 'customerConclusionId', 'string', 'CustomerConclusionId', 1, 246, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (330, 40, 1, 'testLineList.conclusion.reviewConclusion', 'conclusion.reviewConclusion', 'reviewConclusion', 'string', 'ReviewConclusion', 1, 246, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (331, 40, 1, 'others.pending', 'others.pending', 'pending', 'object', 'Pending', 1, 5, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (332, 40, 1, 'others.trfRemark', 'others.trfRemark', 'trfRemark', 'string', 'TrfRemark', 1, 5, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (333, 40, 1, 'others.pendingFlag', 'others.pendingFlag', 'pendingFlag', 'integer', 'PendingFlag', 1, 331, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (334, 40, 1, 'others.pendingRemark', 'others.pendingRemark', 'pendingRemark', 'string', 'PendingRemark', 1, 331, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (335, 40, 1, 'others.pendingType', 'others.pendingType', 'pendingType', 'string', 'PendingType', 1, 331, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (336, 40, 1, 'testResultList.languageList', 'languageList', 'languageList', 'collection', 'LanguageList', 1, 9, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (337, 40, 1, 'testResultList.limitValueFullName', 'limitValueFullName', 'limitValueFullName', 'string', 'LimitValueFullName', 1, 9, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (338, 40, 1, 'testResultList.limitValueFullNameRel', 'limitValueFullNameRel', 'limitValueFullNameRel', 'object', 'LimitValueFullNameRel', 1, 9, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (339, 40, 1, 'testResultList.subReportNo', 'subReportNo', 'subReportNo', 'string', 'SubReportNo', 1, 9, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (340, 40, 1, 'testResultList.testMatrixId', 'testMatrixId', 'testMatrixId', 'string', 'TestMatrixId', 1, 9, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (341, 40, 1, 'testResultList.testResult', 'testResult', 'testResult', 'object', 'TestResult', 1, 9, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (342, 40, 1, 'testResultList.testResultFullName', 'testResultFullName', 'testResultFullName', 'string', 'TestResultFullName', 1, 9, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (343, 40, 1, 'testResultList.testResultFullNameRel', 'testResultFullNameRel', 'testResultFullNameRel', 'object', 'TestResultFullNameRel', 1, 9, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (344, 40, 1, 'testResultList.testResultSeq', 'testResultSeq', 'testResultSeq', 'integer', 'TestResultSeq', 1, 9, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (345, 40, 1, 'testResultList.testResultFullNameRel.analyteInstanceId', 'testResultFullNameRel.analyteInstanceId', 'analyteInstanceId', 'string', 'AnalyteInstanceId', 1, 343, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (346, 40, 1, 'testResultList.testResultFullNameRel.conditionInstanceId', 'testResultFullNameRel.conditionInstanceId', 'conditionInstanceId', 'string', 'ConditionInstanceId', 1, 343, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (347, 40, 1, 'testResultList.testResultFullNameRel.parentConditionInstanceId', 'testResultFullNameRel.parentConditionInstanceId', 'parentConditionInstanceId', 'string', 'ParentConditionInstanceId', 1, 343, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (348, 40, 1, 'testResultList.testResultFullNameRel.positionInstanceId', 'testResultFullNameRel.positionInstanceId', 'positionInstanceId', 'string', 'PositionInstanceId', 1, 343, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (349, 40, 1, 'testResultList.testResultFullNameRel.procedureConditionInstanceId', 'testResultFullNameRel.procedureConditionInstanceId', 'procedureConditionInstanceId', 'string', 'ProcedureConditionInstanceId', 1, 343, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (350, 40, 1, 'testResultList.testResultFullNameRel.specimenInstanceId', 'testResultFullNameRel.specimenInstanceId', 'specimenInstanceId', 'string', 'SpecimenInstanceId', 1, 343, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (351, 40, 1, 'testResultList.testResultFullNameRel.upSpecimenInstanceId', 'testResultFullNameRel.upSpecimenInstanceId', 'upSpecimenInstanceId', 'string', 'UpSpecimenInstanceId', 1, 343, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (352, 40, 1, 'testResultList.testResult.failFlag', 'testResult.failFlag', 'failFlag', 'integer', 'FailFlag', 1, 341, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (353, 40, 1, 'testResultList.testResult.resultUnit', 'testResult.resultUnit', 'resultUnit', 'string', 'ResultUnit', 1, 341, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (354, 40, 1, 'testResultList.testResult.resultValue', 'testResult.resultValue', 'resultValue', 'string', 'ResultValue', 1, 341, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (355, 40, 1, 'testResultList.testResult.resultValueRemark', 'testResult.resultValueRemark', 'resultValueRemark', 'string', 'ResultValueRemark', 1, 341, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (356, 40, 1, 'testResultList.limitValueFullNameRel.limitUnit', 'limitValueFullNameRel.limitUnit', 'limitUnit', 'string', 'LimitUnit', 1, 338, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (357, 40, 1, 'testResultList.limitValueFullNameRel.limitValue1', 'limitValueFullNameRel.limitValue1', 'limitValue1', 'string', 'LimitValue1', 1, 338, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (358, 40, 1, 'testResultList.limitValueFullNameRel.limitValue2', 'limitValueFullNameRel.limitValue2', 'limitValue2', 'string', 'LimitValue2', 1, 338, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (359, 40, 1, 'testResultList.limitValueFullNameRel.manualRequirement', 'limitValueFullNameRel.manualRequirement', 'manualRequirement', 'integer', 'ManualRequirement', 1, 338, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (360, 40, 1, 'testResultList.limitValueFullNameRel.operatorName', 'limitValueFullNameRel.operatorName', 'operatorName', 'string', 'OperatorName', 1, 338, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (361, 40, 1, 'testResultList.limitValueFullNameRel.reportDescription', 'limitValueFullNameRel.reportDescription', 'reportDescription', 'string', 'ReportDescription', 1, 338, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (362, 40, 1, 'testResultList.limitValueFullNameRel.talBaseId', 'limitValueFullNameRel.talBaseId', 'talBaseId', 'long', 'TalBaseId', 1, 338, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (363, 40, 1, 'testResultList.languageList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 336, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (364, 40, 1, 'testResultList.languageList.limitValueFullName', 'limitValueFullName', 'limitValueFullName', 'string', 'LimitValueFullName', 1, 336, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (365, 40, 1, 'testResultList.languageList.testResultFullName', 'testResultFullName', 'testResultFullName', 'string', 'TestResultFullName', 1, 336, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (366, 40, 1, 'quotationList.adjustmentAmount', 'adjustmentAmount', 'adjustmentAmount', 'string', 'AdjustmentAmount', 1, 6, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (367, 40, 1, 'quotationList.currency', 'currency', 'currency', 'string', 'Currency', 1, 6, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (368, 40, 1, 'quotationList.discount', 'discount', 'discount', 'string', 'Discount', 1, 6, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (369, 40, 1, 'quotationList.finalAmount', 'finalAmount', 'finalAmount', 'string', 'FinalAmount', 1, 6, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (370, 40, 1, 'quotationList.freeQuotationFlag', 'freeQuotationFlag', 'freeQuotationFlag', 'integer', 'FreeQuotationFlag', 1, 6, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (371, 40, 1, 'quotationList.netAmount', 'netAmount', 'netAmount', 'string', 'NetAmount', 1, 6, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (372, 40, 1, 'quotationList.payer', 'payer', 'payer', 'object', 'Payer', 1, 6, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (373, 40, 1, 'quotationList.quotationFileList', 'quotationFileList', 'quotationFileList', 'collection', 'QuotationFileList', 1, 6, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (374, 40, 1, 'quotationList.quotationNo', 'quotationNo', 'quotationNo', 'string', 'QuotationNo', 1, 6, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (375, 40, 1, 'quotationList.quotationStatus', 'quotationStatus', 'quotationStatus', 'integer', 'QuotationStatus', 1, 6, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (376, 40, 1, 'quotationList.quotationVersionId', 'quotationVersionId', 'quotationVersionId', 'long', 'QuotationVersionId', 1, 6, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (377, 40, 1, 'quotationList.serviceItemList', 'serviceItemList', 'serviceItemList', 'collection', 'ServiceItemList', 1, 6, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (378, 40, 1, 'quotationList.totalAmount', 'totalAmount', 'totalAmount', 'string', 'TotalAmount', 1, 6, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (379, 40, 1, 'quotationList.vatAmount', 'vatAmount', 'vatAmount', 'string', 'VatAmount', 1, 6, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (380, 40, 1, 'quotationList.payer.blackFlag', 'payer.blackFlag', 'blackFlag', 'integer', 'BlackFlag', 1, 372, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (381, 40, 1, 'quotationList.payer.bossNo', 'payer.bossNo', 'bossNo', 'long', 'BossNo', 1, 372, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (382, 40, 1, 'quotationList.payer.customerAddress', 'payer.customerAddress', 'customerAddress', 'string', 'CustomerAddress', 1, 372, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (383, 40, 1, 'quotationList.payer.customerContactList', 'payer.customerContactList', 'customerContactList', 'collection', 'CustomerContactList', 1, 372, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (384, 40, 1, 'quotationList.payer.customerGroupCode', 'payer.customerGroupCode', 'customerGroupCode', 'string', 'CustomerGroupCode', 1, 372, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (385, 40, 1, 'quotationList.payer.customerId', 'payer.customerId', 'customerId', 'string', 'CustomerId', 1, 372, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (386, 40, 1, 'quotationList.payer.customerInstanceId', 'payer.customerInstanceId', 'customerInstanceId', 'string', 'CustomerInstanceId', 1, 372, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (387, 40, 1, 'quotationList.payer.customerName', 'payer.customerName', 'customerName', 'string', 'CustomerName', 1, 372, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (388, 40, 1, 'quotationList.payer.customerUsage', 'payer.customerUsage', 'customerUsage', 'integer', 'CustomerUsage', 1, 372, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (389, 40, 1, 'quotationList.payer.languageList', 'payer.languageList', 'languageList', 'collection', 'LanguageList', 1, 372, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (390, 40, 1, 'quotationList.payer.paymentTerm', 'payer.paymentTerm', 'paymentTerm', 'string', 'PaymentTerm', 1, 372, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (391, 40, 1, 'quotationList.payer.customerContactList.bossContactId', 'bossContactId', 'bossContactId', 'long', 'BossContactId', 1, 383, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (392, 40, 1, 'quotationList.payer.customerContactList.bossSiteUseId', 'bossSiteUseId', 'bossSiteUseId', 'long', 'BossSiteUseId', 1, 383, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (393, 40, 1, 'quotationList.payer.customerContactList.contactAddressId', 'contactAddressId', 'contactAddressId', 'string', 'ContactAddressId', 1, 383, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (394, 40, 1, 'quotationList.payer.customerContactList.contactEmail', 'contactEmail', 'contactEmail', 'string', 'ContactEmail', 1, 383, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (395, 40, 1, 'quotationList.payer.customerContactList.contactFax', 'contactFax', 'contactFax', 'string', 'ContactFax', 1, 383, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (396, 40, 1, 'quotationList.payer.customerContactList.contactId', 'contactId', 'contactId', 'string', 'ContactId', 1, 383, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (397, 40, 1, 'quotationList.payer.customerContactList.contactName', 'contactName', 'contactName', 'string', 'ContactName', 1, 383, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (398, 40, 1, 'quotationList.payer.customerContactList.contactPhone', 'contactPhone', 'contactPhone', 'string', 'ContactPhone', 1, 383, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (399, 40, 1, 'quotationList.payer.customerContactList.contactRegionAccount', 'contactRegionAccount', 'contactRegionAccount', 'string', 'ContactRegionAccount', 1, 383, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (400, 40, 1, 'quotationList.payer.customerContactList.contactTelephone', 'contactTelephone', 'contactTelephone', 'string', 'ContactTelephone', 1, 383, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (401, 40, 1, 'quotationList.payer.customerContactList.contactUsage', 'contactUsage', 'contactUsage', 'integer', 'ContactUsage', 1, 383, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (402, 40, 1, 'quotationList.payer.customerContactList.responsibleTeamCode', 'responsibleTeamCode', 'responsibleTeamCode', 'string', 'ResponsibleTeamCode', 1, 383, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (403, 40, 1, 'quotationList.payer.languageList.customerAddress', 'customerAddress', 'customerAddress', 'string', 'CustomerAddress', 1, 389, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (404, 40, 1, 'quotationList.payer.languageList.customerName', 'customerName', 'customerName', 'string', 'CustomerName', 1, 389, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (405, 40, 1, 'quotationList.payer.languageList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 389, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (406, 40, 1, 'quotationList.quotationFileList.cloudId', 'cloudId', 'cloudId', 'string', 'CloudId', 1, 373, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (407, 40, 1, 'quotationList.quotationFileList.fileName', 'fileName', 'fileName', 'string', 'FileName', 1, 373, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (408, 40, 1, 'quotationList.quotationFileList.filePath', 'filePath', 'filePath', 'string', 'FilePath', 1, 373, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (409, 40, 1, 'quotationList.quotationFileList.fileSize', 'fileSize', 'fileSize', 'long', 'FileSize', 1, 373, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (410, 40, 1, 'quotationList.quotationFileList.fileType', 'fileType', 'fileType', 'integer', 'FileType', 1, 373, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (411, 40, 1, 'quotationList.quotationFileList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 373, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (412, 40, 1, 'quotationList.serviceItemList.citationId', 'citationId', 'citationId', 'integer', 'CitationId', 1, 377, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (413, 40, 1, 'quotationList.serviceItemList.citationName', 'citationName', 'citationName', 'string', 'CitationName', 1, 377, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (414, 40, 1, 'quotationList.serviceItemList.citationType', 'citationType', 'citationType', 'integer', 'CitationType', 1, 377, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (415, 40, 1, 'quotationList.serviceItemList.citationVersionId', 'citationVersionId', 'citationVersionId', 'integer', 'CitationVersionId', 1, 377, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (416, 40, 1, 'quotationList.serviceItemList.evaluationAlias', 'evaluationAlias', 'evaluationAlias', 'string', 'EvaluationAlias', 1, 377, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (417, 40, 1, 'quotationList.serviceItemList.externalInfo', 'externalInfo', 'externalInfo', 'object', 'ExternalInfo', 1, 377, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (418, 40, 1, 'quotationList.serviceItemList.languageList', 'languageList', 'languageList', 'collection', 'LanguageList', 1, 377, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (419, 40, 1, 'quotationList.serviceItemList.ppNo', 'ppNo', 'ppNo', 'integer', 'PpNo', 1, 377, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (420, 40, 1, 'quotationList.serviceItemList.ppVersionId', 'ppVersionId', 'ppVersionId', 'integer', 'PpVersionId', 1, 377, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (421, 40, 1, 'quotationList.serviceItemList.quantity', 'quantity', 'quantity', 'integer', 'Quantity', 1, 377, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (422, 40, 1, 'quotationList.serviceItemList.serviceItemDiscount', 'serviceItemDiscount', 'serviceItemDiscount', 'string', 'ServiceItemDiscount', 1, 377, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (423, 40, 1, 'quotationList.serviceItemList.serviceItemListUnitPrice', 'serviceItemListUnitPrice', 'serviceItemListUnitPrice', 'string', 'ServiceItemListUnitPrice', 1, 377, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (424, 40, 1, 'quotationList.serviceItemList.serviceItemName', 'serviceItemName', 'serviceItemName', 'string', 'ServiceItemName', 1, 377, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (425, 40, 1, 'quotationList.serviceItemList.serviceItemNetAmount', 'serviceItemNetAmount', 'serviceItemNetAmount', 'string', 'ServiceItemNetAmount', 1, 377, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (426, 40, 1, 'quotationList.serviceItemList.serviceItemSalesUnitPrice', 'serviceItemSalesUnitPrice', 'serviceItemSalesUnitPrice', 'string', 'ServiceItemSalesUnitPrice', 1, 377, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (427, 40, 1, 'quotationList.serviceItemList.serviceItemTotalAmount', 'serviceItemTotalAmount', 'serviceItemTotalAmount', 'string', 'ServiceItemTotalAmount', 1, 377, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (428, 40, 1, 'quotationList.serviceItemList.serviceItemVatAmount', 'serviceItemVatAmount', 'serviceItemVatAmount', 'string', 'ServiceItemVatAmount', 1, 377, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (429, 40, 1, 'quotationList.serviceItemList.testLineId', 'testLineId', 'testLineId', 'integer', 'TestLineId', 1, 377, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (430, 40, 1, 'quotationList.serviceItemList.testLineVersionId', 'testLineVersionId', 'testLineVersionId', 'integer', 'TestLineVersionId', 1, 377, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (431, 40, 1, 'quotationList.serviceItemList.languageList.citationName', 'citationName', 'citationName', 'string', 'CitationName', 1, 418, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (432, 40, 1, 'quotationList.serviceItemList.languageList.evaluationAlias', 'evaluationAlias', 'evaluationAlias', 'string', 'EvaluationAlias', 1, 418, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (433, 40, 1, 'quotationList.serviceItemList.languageList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 418, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (434, 40, 1, 'quotationList.serviceItemList.languageList.serviceItemName', 'serviceItemName', 'serviceItemName', 'string', 'ServiceItemName', 1, 418, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (435, 40, 1, 'quotationList.serviceItemList.externalInfo.checkType', 'externalInfo.checkType', 'checkType', 'integer', 'CheckType', 1, 417, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (436, 40, 1, 'quotationList.serviceItemList.externalInfo.languageList', 'externalInfo.languageList', 'languageList', 'collection', 'LanguageList', 1, 417, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (437, 40, 1, 'quotationList.serviceItemList.externalInfo.testCitationId', 'externalInfo.testCitationId', 'testCitationId', 'string', 'TestCitationId', 1, 417, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (438, 40, 1, 'quotationList.serviceItemList.externalInfo.testCitationName', 'externalInfo.testCitationName', 'testCitationName', 'string', 'TestCitationName', 1, 417, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (439, 40, 1, 'quotationList.serviceItemList.externalInfo.testItemId', 'externalInfo.testItemId', 'testItemId', 'string', 'TestItemId', 1, 417, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (440, 40, 1, 'quotationList.serviceItemList.externalInfo.testItemName', 'externalInfo.testItemName', 'testItemName', 'string', 'TestItemName', 1, 417, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (441, 40, 1, 'invoiceList.currency', 'currency', 'currency', 'string', 'Currency', 1, 3, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (442, 40, 1, 'invoiceList.invoiceFileList', 'invoiceFileList', 'invoiceFileList', 'collection', 'InvoiceFileList', 1, 3, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (443, 40, 1, 'invoiceList.invoiceNo', 'invoiceNo', 'invoiceNo', 'string', 'InvoiceNo', 1, 3, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (444, 40, 1, 'invoiceList.invoiceStatus', 'invoiceStatus', 'invoiceStatus', 'integer', 'InvoiceStatus', 1, 3, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (445, 40, 1, 'invoiceList.netAmount', 'netAmount', 'netAmount', 'string', 'NetAmount', 1, 3, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (446, 40, 1, 'invoiceList.quotationNoList', 'quotationNoList', 'quotationNoList', 'collection', 'QuotationNoList', 1, 3, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (447, 40, 1, 'invoiceList.totalAmount', 'totalAmount', 'totalAmount', 'string', 'TotalAmount', 1, 3, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (448, 40, 1, 'invoiceList.vatAmount', 'vatAmount', 'vatAmount', 'string', 'VatAmount', 1, 3, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (449, 40, 1, 'invoiceList.invoiceFileList.cloudId', 'cloudId', 'cloudId', 'string', 'CloudId', 1, 442, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (450, 40, 1, 'invoiceList.invoiceFileList.fileName', 'fileName', 'fileName', 'string', 'FileName', 1, 442, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (451, 40, 1, 'invoiceList.invoiceFileList.filePath', 'filePath', 'filePath', 'string', 'FilePath', 1, 442, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (452, 40, 1, 'invoiceList.invoiceFileList.fileSize', 'fileSize', 'fileSize', 'long', 'FileSize', 1, 442, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (453, 40, 1, 'invoiceList.invoiceFileList.fileType', 'fileType', 'fileType', 'integer', 'FileType', 1, 442, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (454, 40, 1, 'invoiceList.invoiceFileList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 442, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (455, 40, 1, 'reportList.approveBy', 'approveBy', 'approveBy', 'string', 'ApproveBy', 1, 7, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (456, 40, 1, 'reportList.approveDate', 'approveDate', 'approveDate', 'date', 'ApproveDate', 1, 7, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (457, 40, 1, 'reportList.certificateName', 'certificateName', 'certificateName', 'string', 'CertificateName', 1, 7, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (458, 40, 1, 'reportList.conclusion', 'conclusion', 'conclusion', 'object', 'Conclusion', 1, 7, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (459, 40, 1, 'reportList.conditionGroupList', 'conditionGroupList', 'conditionGroupList', 'collection', 'ConditionGroupList', 1, 7, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (460, 40, 1, 'reportList.createBy', 'createBy', 'createBy', 'string', 'CreateBy', 1, 7, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (461, 40, 1, 'reportList.createDate', 'createDate', 'createDate', 'date', 'CreateDate', 1, 7, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (462, 40, 1, 'reportList.lab', 'lab', 'lab', 'object', 'Lab', 1, 7, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (463, 40, 1, 'reportList.originalReportNo', 'originalReportNo', 'originalReportNo', 'string', 'OriginalReportNo', 1, 7, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (464, 40, 1, 'reportList.relationship', 'relationship', 'relationship', 'object', 'Relationship', 1, 7, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (465, 40, 1, 'reportList.reportConclusionList', 'reportConclusionList', 'reportConclusionList', 'collection', 'ReportConclusionList', 1, 7, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (466, 40, 1, 'reportList.reportDueDate', 'reportDueDate', 'reportDueDate', 'string', 'ReportDueDate', 1, 7, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (467, 40, 1, 'reportList.reportFileList', 'reportFileList', 'reportFileList', 'collection', 'ReportFileList', 1, 7, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (468, 40, 1, 'reportList.reportId', 'reportId', 'reportId', 'string', 'ReportId', 1, 7, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (469, 40, 1, 'reportList.reportMatrixList', 'reportMatrixList', 'reportMatrixList', 'collection', 'ReportMatrixList', 1, 7, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (470, 40, 1, 'reportList.reportNo', 'reportNo', 'reportNo', 'string', 'ReportNo', 1, 7, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (471, 40, 1, 'reportList.reportStatus', 'reportStatus', 'reportStatus', 'integer', 'ReportStatus', 1, 7, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (472, 40, 1, 'reportList.softCopyDeliveryDate', 'softCopyDeliveryDate', 'softCopyDeliveryDate', 'date', 'SoftCopyDeliveryDate', 1, 7, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (473, 40, 1, 'reportList.subReportList', 'subReportList', 'subReportList', 'collection', 'SubReportList', 1, 7, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (474, 40, 1, 'reportList.lab.buCode', 'lab.buCode', 'buCode', 'string', 'BuCode', 1, 462, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (475, 40, 1, 'reportList.lab.buId', 'lab.buId', 'buId', 'integer', 'BuId', 1, 462, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (476, 40, 1, 'reportList.lab.countryId', 'lab.countryId', 'countryId', 'integer', 'CountryId', 1, 462, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (477, 40, 1, 'reportList.lab.labAddress', 'lab.labAddress', 'labAddress', 'string', 'LabAddress', 1, 462, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (478, 40, 1, 'reportList.lab.labCode', 'lab.labCode', 'labCode', 'string', 'LabCode', 1, 462, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (479, 40, 1, 'reportList.lab.labContact', 'lab.labContact', 'labContact', 'object', 'LabContact', 1, 462, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (480, 40, 1, 'reportList.lab.labId', 'lab.labId', 'labId', 'long', 'LabId', 1, 462, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (481, 40, 1, 'reportList.lab.labName', 'lab.labName', 'labName', 'string', 'LabName', 1, 462, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (482, 40, 1, 'reportList.lab.labType', 'lab.labType', 'labType', 'integer', 'LabType', 1, 462, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (483, 40, 1, 'reportList.lab.languageList', 'lab.languageList', 'languageList', 'collection', 'LanguageList', 1, 462, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (484, 40, 1, 'reportList.lab.locationCode', 'lab.locationCode', 'locationCode', 'string', 'LocationCode', 1, 462, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (485, 40, 1, 'reportList.lab.locationId', 'lab.locationId', 'locationId', 'integer', 'LocationId', 1, 462, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (486, 40, 1, 'reportList.lab.otherCode', 'lab.otherCode', 'otherCode', 'string', 'OtherCode', 1, 462, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (487, 40, 1, 'reportList.conclusion.conclusionCode', 'conclusion.conclusionCode', 'conclusionCode', 'string', 'ConclusionCode', 1, 458, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (488, 40, 1, 'reportList.conclusion.conclusionId', 'conclusion.conclusionId', 'conclusionId', 'string', 'ConclusionId', 1, 458, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (489, 40, 1, 'reportList.conclusion.conclusionRemark', 'conclusion.conclusionRemark', 'conclusionRemark', 'string', 'ConclusionRemark', 1, 458, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (490, 40, 1, 'reportList.conclusion.customerConclusion', 'conclusion.customerConclusion', 'customerConclusion', 'string', 'CustomerConclusion', 1, 458, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (491, 40, 1, 'reportList.conclusion.customerConclusionId', 'conclusion.customerConclusionId', 'customerConclusionId', 'string', 'CustomerConclusionId', 1, 458, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (492, 40, 1, 'reportList.conclusion.reviewConclusion', 'conclusion.reviewConclusion', 'reviewConclusion', 'string', 'ReviewConclusion', 1, 458, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (493, 40, 1, 'reportList.relationship.parent', 'relationship.parent', 'parent', 'object', 'Parent', 1, 464, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (494, 40, 1, 'reportList.relationship.subReportList', 'relationship.subReportList', 'subReportList', 'collection', 'SubReportList', 1, 464, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (495, 40, 1, 'reportList.reportMatrixList.conclusion', 'conclusion', 'conclusion', 'object', 'Conclusion', 1, 469, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (496, 40, 1, 'reportList.reportMatrixList.conditionList', 'conditionList', 'conditionList', 'collection', 'ConditionList', 1, 469, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (497, 40, 1, 'reportList.reportMatrixList.positionList', 'positionList', 'positionList', 'collection', 'PositionList', 1, 469, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (498, 40, 1, 'reportList.reportMatrixList.specimenList', 'specimenList', 'specimenList', 'collection', 'SpecimenList', 1, 469, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (499, 40, 1, 'reportList.reportMatrixList.testConditionGroupId', 'testConditionGroupId', 'testConditionGroupId', 'string', 'TestConditionGroupId', 1, 469, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (500, 40, 1, 'reportList.reportMatrixList.testLineInstanceId', 'testLineInstanceId', 'testLineInstanceId', 'string', 'TestLineInstanceId', 1, 469, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (501, 40, 1, 'reportList.reportMatrixList.testMatrixId', 'testMatrixId', 'testMatrixId', 'string', 'TestMatrixId', 1, 469, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (502, 40, 1, 'reportList.reportMatrixList.testSampleInstanceId', 'testSampleInstanceId', 'testSampleInstanceId', 'string', 'TestSampleInstanceId', 1, 469, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (503, 40, 1, 'reportList.relationship.parent.orderNo', 'relationship.parent.orderNo', 'orderNo', 'string', 'OrderNo', 1, 493, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (504, 40, 1, 'reportList.relationship.parent.systemId', 'relationship.parent.systemId', 'systemId', 'integer', 'SystemId', 1, 493, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (505, 40, 1, 'reportList.relationship.subReportList.externalId', 'externalId', 'externalId', 'string', 'ExternalId', 1, 494, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (506, 40, 1, 'reportList.relationship.subReportList.externalNo', 'externalNo', 'externalNo', 'string', 'ExternalNo', 1, 494, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (507, 40, 1, 'reportList.relationship.subReportList.objectNo', 'objectNo', 'objectNo', 'string', 'ObjectNo', 1, 494, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (508, 40, 1, 'reportList.relationship.subReportList.sourceType', 'sourceType', 'sourceType', 'string', 'SourceType', 1, 494, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (509, 40, 1, 'reportList.relationship.subReportList.subReportNo', 'subReportNo', 'subReportNo', 'string', 'SubReportNo', 1, 494, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (510, 40, 1, 'reportList.reportMatrixList.conditionList.conditionDesc', 'conditionDesc', 'conditionDesc', 'string', 'ConditionDesc', 1, 496, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (511, 40, 1, 'reportList.reportMatrixList.conditionList.conditionId', 'conditionId', 'conditionId', 'integer', 'ConditionId', 1, 496, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (512, 40, 1, 'reportList.reportMatrixList.conditionList.conditionInstanceId', 'conditionInstanceId', 'conditionInstanceId', 'string', 'ConditionInstanceId', 1, 496, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (513, 40, 1, 'reportList.reportMatrixList.conditionList.conditionName', 'conditionName', 'conditionName', 'string', 'ConditionName', 1, 496, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (514, 40, 1, 'reportList.reportMatrixList.conditionList.conditionSeq', 'conditionSeq', 'conditionSeq', 'integer', 'ConditionSeq', 1, 496, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (515, 40, 1, 'reportList.reportMatrixList.conditionList.conditionType', 'conditionType', 'conditionType', 'integer', 'ConditionType', 1, 496, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (516, 40, 1, 'reportList.reportMatrixList.conditionList.conditionTypeId', 'conditionTypeId', 'conditionTypeId', 'integer', 'ConditionTypeId', 1, 496, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (517, 40, 1, 'reportList.reportMatrixList.conditionList.conditionTypeName', 'conditionTypeName', 'conditionTypeName', 'string', 'ConditionTypeName', 1, 496, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (518, 40, 1, 'reportList.reportMatrixList.conditionList.languageList', 'languageList', 'languageList', 'collection', 'LanguageList', 1, 496, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (528, 40, 1, 'reportList.reportMatrixList.specimenList.languageList', 'languageList', 'languageList', 'collection', 'LanguageList', 1, 498, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (529, 40, 1, 'reportList.reportMatrixList.specimenList.specimenDescription', 'specimenDescription', 'specimenDescription', 'string', 'SpecimenDescription', 1, 498, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (530, 40, 1, 'reportList.reportMatrixList.specimenList.specimenInstanceId', 'specimenInstanceId', 'specimenInstanceId', 'string', 'SpecimenInstanceId', 1, 498, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (531, 40, 1, 'reportList.reportMatrixList.specimenList.specimenNo', 'specimenNo', 'specimenNo', 'integer', 'SpecimenNo', 1, 498, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (532, 40, 1, 'reportList.reportMatrixList.specimenList.specimenType', 'specimenType', 'specimenType', 'integer', 'SpecimenType', 1, 498, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (533, 40, 1, 'reportList.reportMatrixList.specimenList.languageList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 528, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (534, 40, 1, 'reportList.reportMatrixList.specimenList.languageList.specimenDescription', 'specimenDescription', 'specimenDescription', 'string', 'SpecimenDescription', 1, 528, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (535, 40, 1, 'reportList.reportMatrixList.positionList.languageList', 'languageList', 'languageList', 'collection', 'LanguageList', 1, 497, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (536, 40, 1, 'reportList.reportMatrixList.positionList.positionDescription', 'positionDescription', 'positionDescription', 'string', 'PositionDescription', 1, 497, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (537, 40, 1, 'reportList.reportMatrixList.positionList.positionInstanceId', 'positionInstanceId', 'positionInstanceId', 'string', 'PositionInstanceId', 1, 497, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (538, 40, 1, 'reportList.reportMatrixList.positionList.positionName', 'positionName', 'positionName', 'string', 'PositionName', 1, 497, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (539, 40, 1, 'reportList.reportMatrixList.positionList.positionSeq', 'positionSeq', 'positionSeq', 'integer', 'PositionSeq', 1, 497, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (540, 40, 1, 'reportList.reportMatrixList.positionList.usageTypeId', 'usageTypeId', 'usageTypeId', 'integer', 'UsageTypeId', 1, 497, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (541, 40, 1, 'reportList.reportMatrixList.positionList.usageTypeName', 'usageTypeName', 'usageTypeName', 'string', 'UsageTypeName', 1, 497, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (542, 40, 1, 'reportList.reportMatrixList.positionList.languageList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 535, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (543, 40, 1, 'reportList.reportMatrixList.positionList.languageList.positionDescription', 'positionDescription', 'positionDescription', 'string', 'PositionDescription', 1, 535, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (544, 40, 1, 'reportList.reportMatrixList.positionList.languageList.positionName', 'positionName', 'positionName', 'string', 'PositionName', 1, 535, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (545, 40, 1, 'reportList.reportMatrixList.conclusion.conclusionCode', 'conclusion.conclusionCode', 'conclusionCode', 'string', 'ConclusionCode', 1, 495, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (546, 40, 1, 'reportList.reportMatrixList.conclusion.conclusionId', 'conclusion.conclusionId', 'conclusionId', 'string', 'ConclusionId', 1, 495, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (547, 40, 1, 'reportList.reportMatrixList.conclusion.conclusionRemark', 'conclusion.conclusionRemark', 'conclusionRemark', 'string', 'ConclusionRemark', 1, 495, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (548, 40, 1, 'reportList.reportMatrixList.conclusion.customerConclusion', 'conclusion.customerConclusion', 'customerConclusion', 'string', 'CustomerConclusion', 1, 495, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (549, 40, 1, 'reportList.reportMatrixList.conclusion.customerConclusionId', 'conclusion.customerConclusionId', 'customerConclusionId', 'string', 'CustomerConclusionId', 1, 495, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (550, 40, 1, 'reportList.reportMatrixList.conclusion.reviewConclusion', 'conclusion.reviewConclusion', 'reviewConclusion', 'string', 'ReviewConclusion', 1, 495, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (551, 40, 1, 'reportList.reportMatrixList.conditionList.languageList.conditionDesc', 'conditionDesc', 'conditionDesc', 'string', 'ConditionDesc', 1, 518, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (552, 40, 1, 'reportList.reportMatrixList.conditionList.languageList.conditionName', 'conditionName', 'conditionName', 'string', 'ConditionName', 1, 518, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (553, 40, 1, 'reportList.reportMatrixList.conditionList.languageList.conditionTypeName', 'conditionTypeName', 'conditionTypeName', 'string', 'ConditionTypeName', 1, 518, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (554, 40, 1, 'reportList.reportMatrixList.conditionList.languageList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 518, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (555, 40, 1, 'reportList.reportConclusionList.conclusionCode', 'conclusionCode', 'conclusionCode', 'string', 'ConclusionCode', 1, 465, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (556, 40, 1, 'reportList.reportConclusionList.conclusionId', 'conclusionId', 'conclusionId', 'string', 'ConclusionId', 1, 465, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (557, 40, 1, 'reportList.reportConclusionList.conclusionInstanceId', 'conclusionInstanceId', 'conclusionInstanceId', 'string', 'ConclusionInstanceId', 1, 465, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (558, 40, 1, 'reportList.reportConclusionList.conclusionLevelId', 'conclusionLevelId', 'conclusionLevelId', 'integer', 'ConclusionLevelId', 1, 465, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (559, 40, 1, 'reportList.reportConclusionList.conclusionRemark', 'conclusionRemark', 'conclusionRemark', 'string', 'ConclusionRemark', 1, 465, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (560, 40, 1, 'reportList.reportConclusionList.customerConclusion', 'customerConclusion', 'customerConclusion', 'string', 'CustomerConclusion', 1, 465, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (561, 40, 1, 'reportList.reportConclusionList.customerConclusionId', 'customerConclusionId', 'customerConclusionId', 'string', 'CustomerConclusionId', 1, 465, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (562, 40, 1, 'reportList.reportConclusionList.objectId', 'objectId', 'objectId', 'string', 'ObjectId', 1, 465, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (563, 40, 1, 'reportList.reportConclusionList.ppArtifactRelId', 'ppArtifactRelId', 'ppArtifactRelId', 'string', 'PpArtifactRelId', 1, 465, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (564, 40, 1, 'reportList.reportConclusionList.ppSampleRelId', 'ppSampleRelId', 'ppSampleRelId', 'string', 'PpSampleRelId', 1, 465, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (565, 40, 1, 'reportList.reportConclusionList.sampleInstanceId', 'sampleInstanceId', 'sampleInstanceId', 'string', 'SampleInstanceId', 1, 465, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (566, 40, 1, 'reportList.reportConclusionList.sectionId', 'sectionId', 'sectionId', 'integer', 'SectionId', 1, 465, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (567, 40, 1, 'reportList.reportConclusionList.testLineInstanceId', 'testLineInstanceId', 'testLineInstanceId', 'string', 'TestLineInstanceId', 1, 465, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (568, 40, 1, 'reportList.reportFileList.cloudId', 'cloudId', 'cloudId', 'string', 'CloudId', 1, 467, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (569, 40, 1, 'reportList.reportFileList.fileName', 'fileName', 'fileName', 'string', 'FileName', 1, 467, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (570, 40, 1, 'reportList.reportFileList.filePath', 'filePath', 'filePath', 'string', 'FilePath', 1, 467, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (571, 40, 1, 'reportList.reportFileList.fileSize', 'fileSize', 'fileSize', 'long', 'FileSize', 1, 467, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (572, 40, 1, 'reportList.reportFileList.fileType', 'fileType', 'fileType', 'integer', 'FileType', 1, 467, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (573, 40, 1, 'reportList.reportFileList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 467, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (574, 40, 1, 'reportList.subReportList.subReportFileList', 'subReportFileList', 'subReportFileList', 'collection', 'SubReportFileList', 1, 473, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (575, 40, 1, 'reportList.subReportList.subReportId', 'subReportId', 'subReportId', 'string', 'SubReportId', 1, 473, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (576, 40, 1, 'reportList.subReportList.subReportNo', 'subReportNo', 'subReportNo', 'string', 'SubReportNo', 1, 473, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (577, 40, 1, 'reportList.subReportList.subReportFileList.cloudId', 'cloudId', 'cloudId', 'string', 'CloudId', 1, 574, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (578, 40, 1, 'reportList.subReportList.subReportFileList.fileName', 'fileName', 'fileName', 'string', 'FileName', 1, 574, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (579, 40, 1, 'reportList.subReportList.subReportFileList.filePath', 'filePath', 'filePath', 'string', 'FilePath', 1, 574, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (580, 40, 1, 'reportList.subReportList.subReportFileList.fileSize', 'fileSize', 'fileSize', 'long', 'FileSize', 1, 574, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (581, 40, 1, 'reportList.subReportList.subReportFileList.fileType', 'fileType', 'fileType', 'integer', 'FileType', 1, 574, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (582, 40, 1, 'reportList.subReportList.subReportFileList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 574, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (583, 40, 1, 'reportList.conditionGroupList.combinedConditionDescription', 'combinedConditionDescription', 'combinedConditionDescription', 'string', 'CombinedConditionDescription', 1, 459, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (584, 40, 1, 'reportList.conditionGroupList.conditionGroupId', 'conditionGroupId', 'conditionGroupId', 'string', 'ConditionGroupId', 1, 459, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (585, 40, 1, 'reportList.conditionGroupList.languageList', 'languageList', 'languageList', 'collection', 'LanguageList', 1, 459, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (586, 40, 1, 'reportList.conditionGroupList.ppConditionGroupList', 'ppConditionGroupList', 'ppConditionGroupList', 'collection', 'PpConditionGroupList', 1, 459, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (587, 40, 1, 'reportList.conditionGroupList.requirement', 'requirement', 'requirement', 'string', 'Requirement', 1, 459, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (588, 40, 1, 'reportList.conditionGroupList.ppConditionGroupList.conditionGroupId', 'conditionGroupId', 'conditionGroupId', 'string', 'ConditionGroupId', 1, 586, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (589, 40, 1, 'reportList.conditionGroupList.ppConditionGroupList.groupFootNotes', 'groupFootNotes', 'groupFootNotes', 'string', 'GroupFootNotes', 1, 586, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (590, 40, 1, 'reportList.conditionGroupList.ppConditionGroupList.id', 'id', 'id', 'string', 'Id', 1, 586, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (591, 40, 1, 'reportList.conditionGroupList.ppConditionGroupList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 586, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (592, 40, 1, 'reportList.conditionGroupList.ppConditionGroupList.ppName', 'ppName', 'ppName', 'string', 'PpName', 1, 586, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (593, 40, 1, 'reportList.conditionGroupList.ppConditionGroupList.ppNo', 'ppNo', 'ppNo', 'integer', 'PpNo', 1, 586, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (594, 40, 1, 'reportList.conditionGroupList.ppConditionGroupList.ppTestLineRelId', 'ppTestLineRelId', 'ppTestLineRelId', 'string', 'PpTestLineRelId', 1, 586, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (595, 40, 1, 'reportList.conditionGroupList.ppConditionGroupList.sampleNos', 'sampleNos', 'sampleNos', 'string', 'SampleNos', 1, 586, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (596, 40, 1, 'reportList.conditionGroupList.languageList.combinedConditionDescription', 'combinedConditionDescription', 'combinedConditionDescription', 'string', 'CombinedConditionDescription', 1, 585, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (597, 40, 1, 'reportList.conditionGroupList.languageList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 585, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (598, 40, 1, 'reportList.reportMatrixList.conclusionCode', 'conclusionCode', 'conclusionCode', 'object', 'ConclusionCode', 1, 469, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (599, 40, 1, 'reportList.reportMatrixList.testMatrixFileList', 'testMatrixFileList', 'testMatrixFileList', 'collection', 'TestMatrixFileList', 1, 469, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-24 14:58:44');
INSERT INTO `tb_dfv_func_field` VALUES (606, 40, 1, 'order.sampleList.conclusion.conclusionCode', 'conclusion.conclusionCode', 'conclusionCode', 'string', 'ConclusionCode', 1, 129, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (607, 40, 1, 'order.sampleList.conclusion.conclusionId', 'conclusion.conclusionId', 'conclusionId', 'string', 'ConclusionId', 1, 129, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (608, 40, 1, 'order.sampleList.conclusion.conclusionRemark', 'conclusion.conclusionRemark', 'conclusionRemark', 'string', 'ConclusionRemark', 1, 129, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (609, 40, 1, 'order.sampleList.conclusion.customerConclusion', 'conclusion.customerConclusion', 'customerConclusion', 'string', 'CustomerConclusion', 1, 129, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (610, 40, 1, 'order.sampleList.conclusion.customerConclusionId', 'conclusion.customerConclusionId', 'customerConclusionId', 'string', 'CustomerConclusionId', 1, 129, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (611, 40, 1, 'order.sampleList.conclusion.reviewConclusion', 'conclusion.reviewConclusion', 'reviewConclusion', 'string', 'ReviewConclusion', 1, 129, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (612, 40, 1, 'reportList.reportMatrixList.testMatrixFileList.cloudId', 'cloudId', 'cloudId', 'string', 'CloudId', 1, 599, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (613, 40, 1, 'reportList.reportMatrixList.testMatrixFileList.fileName', 'fileName', 'fileName', 'string', 'FileName', 1, 599, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (614, 40, 1, 'reportList.reportMatrixList.testMatrixFileList.filePath', 'filePath', 'filePath', 'string', 'FilePath', 1, 599, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (615, 40, 1, 'reportList.reportMatrixList.testMatrixFileList.fileSize', 'fileSize', 'fileSize', 'long', 'FileSize', 1, 599, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (616, 40, 1, 'reportList.reportMatrixList.testMatrixFileList.fileType', 'fileType', 'fileType', 'integer', 'FileType', 1, 599, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (617, 40, 1, 'reportList.reportMatrixList.testMatrixFileList.languageId', 'languageId', 'languageId', 'integer', 'LanguageId', 1, 599, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (624, 40, 1, 'testLineList.externalInfo.checkType', 'externalInfo.checkType', 'checkType', 'integer', 'CheckType', 1, 248, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (625, 40, 1, 'testLineList.externalInfo.languageList', 'externalInfo.languageList', 'languageList', 'collection', 'LanguageList', 1, 248, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (626, 40, 1, 'testLineList.externalInfo.testCitationId', 'externalInfo.testCitationId', 'testCitationId', 'string', 'TestCitationId', 1, 248, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (627, 40, 1, 'testLineList.externalInfo.testCitationName', 'externalInfo.testCitationName', 'testCitationName', 'string', 'TestCitationName', 1, 248, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (628, 40, 1, 'testLineList.externalInfo.testItemId', 'externalInfo.testItemId', 'testItemId', 'string', 'TestItemId', 1, 248, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (629, 40, 1, 'testLineList.externalInfo.testItemName', 'externalInfo.testItemName', 'testItemName', 'string', 'TestItemName', 1, 248, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-15 17:10:28');
INSERT INTO `tb_dfv_func_field` VALUES (630, 40, 2, 'trfNo', 'trfNo', 'trfNo', 'string', 'TrfNo', 1, 0, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-25 18:59:21');
INSERT INTO `tb_dfv_func_field` VALUES (631, 40, 2, 'refSystemId', 'refSystemId', 'refSystemId', 'integer', 'RefSystemId', 1, 0, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-25 18:59:23');
INSERT INTO `tb_dfv_func_field` VALUES (632, 40, 2, 'systemId', 'systemId', 'systemId', 'integer', 'SystemId', 1, 0, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-25 18:59:23');
INSERT INTO `tb_dfv_func_field` VALUES (633, 40, 1, 'systemId', 'systemId', 'systemId', 'integer', 'SystemId', 1, 0, 1, 'system', '2023-05-15 17:10:28', 'system', '2023-05-15 17:10:28', '2023-05-25 18:59:23');

-- ----------------------------
-- Table structure for tb_dfv_function
-- ----------------------------

CREATE TABLE `tb_dfv_function`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `system_id` int(11) NOT NULL COMMENT '归属系统id',
  `function_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '功能代码',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '功能描述',
  `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uidx_sys_func`(`system_id`, `function_code`) USING BTREE,
  INDEX `idx_created_date`(`created_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'DFV-功能' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_dfv_function
-- ----------------------------
INSERT INTO `tb_dfv_function` VALUES (1, 40, 'sci.syncTrf', 'SCI同步结构', 1, 'system', '2023-05-15 16:57:55', 'system', '2023-05-15 16:57:55', '2023-05-15 16:57:55');
INSERT INTO `tb_dfv_function` VALUES (2, 40, 'sci.importTrf', 'SCI Import', 1, 'system', '2023-05-15 16:57:55', 'system', '2023-05-15 16:57:55', '2023-05-25 18:39:42');

-- ----------------------------
-- Table structure for tb_dfv_template
-- ----------------------------

CREATE TABLE `tb_dfv_template`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `system_id` int(11) NOT NULL COMMENT '归属系统id',
  `template_code` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板代码',
  `function_id` bigint(20) NOT NULL COMMENT '归属功能id',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板功能描述',
  `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NOT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NOT NULL COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_sys_tpl`(`system_id`, `template_code`) USING BTREE,
  INDEX `idx_created_date`(`created_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'DFV-验证实例-模板' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_dfv_template
-- ----------------------------
INSERT INTO `tb_dfv_template` VALUES (1, 40, 'sci-syncTrf-SyncConfirmed-7-39', 1, 'Sync Trf接口：SyncConfirmed action，希音客户，土耳其订单系统场景', 1, 'system', '2023-05-16 17:38:09', 'system', '2023-05-16 17:38:14', '2023-05-16 17:38:20');
INSERT INTO `tb_dfv_template` VALUES (2, 40, 'sci-syncTrf-SyncTesting-7-39', 1, 'Sync Trf接口：SyncTesting action，希音客户，土耳其订单系统场景', 1, 'system', '2023-05-16 17:38:09', 'system', '2023-05-16 17:38:14', '2023-05-16 17:40:37');
INSERT INTO `tb_dfv_template` VALUES (3, 40, 'sci-syncTrf-SyncCompleted-7-39', 1, 'Sync Trf接口：SyncCompleted action，希音客户，土耳其订单系统场景', 1, 'system', '2023-05-16 17:38:09', 'system', '2023-05-16 17:38:14', '2023-05-16 17:40:37');
INSERT INTO `tb_dfv_template` VALUES (4, 40, 'sci-syncTrf-SyncClosed-7-39', 1, 'Sync Trf接口：SyncClosed action，希音客户，土耳其订单系统场景', 1, 'system', '2023-05-16 17:38:09', 'system', '2023-05-16 17:38:14', '2023-05-16 17:41:23');
INSERT INTO `tb_dfv_template` VALUES (5, 40, 'sci-syncTrf', 1, 'sync接口默认规则', 1, 'system', '2023-05-16 17:38:09', 'system', '2023-05-16 17:38:14', '2023-05-26 10:43:09');
INSERT INTO `tb_dfv_template` VALUES (6, 40, 'sci-import', 2, 'import接口默认规则', 1, 'system', '2023-05-16 17:38:09', 'system', '2023-05-16 17:38:14', '2023-05-26 10:43:11');

-- ----------------------------
-- Table structure for tb_dfv_template_field
-- ----------------------------

CREATE TABLE `tb_dfv_template_field`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `template_id` bigint(20) NOT NULL COMMENT '模板id',
  `field_id` bigint(20) NOT NULL COMMENT '属性id',
  `field_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `sort_num` int(11) NOT NULL COMMENT '排序',
  `parent_id` bigint(20) NOT NULL COMMENT '父属性id，0为无父属性，其他表示父属性id',
  `active_indicator` int(1) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NOT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NOT NULL COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_created_date`(`created_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 160 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'DFV-验证实例-模板属性' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_dfv_template_field
-- ----------------------------
INSERT INTO `tb_dfv_template_field` VALUES (1, 1, 1, 'action', 1, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (2, 1, 2, 'header', 2, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (3, 1, 12, 'header.trfList', 1, 2, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (4, 1, 14, 'header.trfList.trfNo', 0, 3, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (5, 1, 11, 'header.refSystemId', 2, 2, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (6, 1, 4, 'order', 3, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (7, 1, 28, 'order.orderId', 1, 6, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (8, 1, 29, 'order.orderNo', 2, 6, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (9, 1, 42, 'order.serviceStartDate', 3, 6, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (10, 1, 7, 'reportList', 4, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (11, 1, 468, 'reportList.reportId', 1, 10, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (12, 1, 470, 'reportList.reportNo', 2, 10, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (13, 1, 466, 'reportList.reportDueDate', 3, 10, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (14, 2, 1, 'action', 1, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (15, 2, 2, 'header', 2, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (16, 2, 12, 'header.trfList', 1, 15, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (17, 2, 14, 'header.trfList.trfNo', 0, 16, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (18, 2, 11, 'header.refSystemId', 2, 15, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (19, 2, 4, 'order', 3, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (20, 2, 28, 'order.orderId', 1, 19, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (21, 2, 29, 'order.orderNo', 2, 19, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (22, 2, 42, 'order.serviceStartDate', 3, 19, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (23, 2, 49, 'order.testingStartDate', 4, 19, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (24, 2, 7, 'reportList', 4, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (25, 2, 468, 'reportList.reportId', 1, 24, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (26, 2, 470, 'reportList.reportNo', 2, 24, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (27, 2, 466, 'reportList.reportDueDate', 3, 24, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (29, 3, 1, 'action', 1, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (30, 3, 2, 'header', 2, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (31, 3, 12, 'header.trfList', 1, 30, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (32, 3, 14, 'header.trfList.trfNo', 0, 31, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (33, 3, 11, 'header.refSystemId', 2, 30, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (34, 3, 4, 'order', 3, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (35, 3, 28, 'order.orderId', 1, 34, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (36, 3, 29, 'order.orderNo', 2, 34, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (37, 3, 42, 'order.serviceStartDate', 3, 34, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (38, 3, 49, 'order.testingStartDate', 4, 34, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-24 13:05:17');
INSERT INTO `tb_dfv_template_field` VALUES (39, 3, 48, 'order.testingEndDate', 5, 34, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (40, 3, 39, 'order.sampleList', 6, 34, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (41, 3, 131, 'order.sampleList.sampleInstanceId', 1, 40, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (42, 3, 130, 'order.sampleList.sampleAttrList', 2, 40, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (43, 3, 129, 'order.sampleList.conclusion', 3, 40, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (44, 3, 137, 'order.sampleList.sampleAttrList.labelCode', 1, 42, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (45, 3, 138, 'order.sampleList.sampleAttrList.labelName', 2, 42, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (46, 3, 139, 'order.sampleList.sampleAttrList.labelValue', 3, 42, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (47, 3, 133, 'order.sampleList.sampleAttrList.attrSeq', 4, 42, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (48, 3, 134, 'order.sampleList.sampleAttrList.customerLabel', 5, 42, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (49, 3, 606, 'order.sampleList.conclusion.conclusionCode', 1, 43, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (50, 3, 609, 'order.sampleList.conclusion.customerConclusion', 2, 43, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (52, 3, 7, 'reportList', 4, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (53, 3, 468, 'reportList.reportId', 1, 52, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (54, 3, 470, 'reportList.reportNo', 2, 52, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (55, 3, 466, 'reportList.reportDueDate', 3, 52, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (56, 3, 469, 'reportList.reportMatrixList', 4, 52, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (57, 3, 467, 'reportList.reportFileList', 5, 52, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (58, 3, 501, 'reportList.reportMatrixList.testMatrixId', 1, 56, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (59, 3, 502, 'reportList.reportMatrixList.testSampleInstanceId', 2, 56, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (60, 3, 500, 'reportList.reportMatrixList.testLineInstanceId', 3, 56, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (61, 3, 495, 'reportList.reportMatrixList.conclusion', 4, 56, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (62, 3, 599, 'reportList.reportMatrixList.testMatrixFileList', 5, 56, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (63, 3, 545, 'reportList.reportMatrixList.conclusion.conclusionCode', 1, 61, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-24 13:05:17');
INSERT INTO `tb_dfv_template_field` VALUES (64, 3, 548, 'reportList.reportMatrixList.conclusion.customerConclusion', 2, 61, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-24 13:05:17');
INSERT INTO `tb_dfv_template_field` VALUES (66, 3, 613, 'reportList.reportMatrixList.testMatrixFileList.fileName', 1, 62, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (67, 3, 616, 'reportList.reportMatrixList.testMatrixFileList.fileType', 2, 62, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (68, 3, 614, 'reportList.reportMatrixList.testMatrixFileList.filePath', 3, 62, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (69, 3, 573, 'reportList.reportFileList.languageId', 1, 57, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (70, 3, 569, 'reportList.reportFileList.fileName', 2, 57, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (71, 3, 572, 'reportList.reportFileList.fileType', 3, 57, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (72, 3, 570, 'reportList.reportFileList.filePath', 4, 57, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (73, 3, 10, 'testSampleList', 5, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (74, 3, 205, 'testSampleList.testSampleInstanceId', 1, 73, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (75, 3, 204, 'testSampleList.testSampleGroupList', 2, 73, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (76, 3, 209, 'testSampleList.testSampleType', 3, 73, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (77, 3, 208, 'testSampleList.testSampleSeq', 4, 73, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (78, 3, 202, 'testSampleList.materialAttrList', 5, 73, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (79, 3, 211, 'testSampleList.testSampleGroupList.testSampleInstanceId', 1, 75, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-24 13:05:17');
INSERT INTO `tb_dfv_template_field` VALUES (80, 3, 210, 'testSampleList.testSampleGroupList.mainSampleFlag', 2, 75, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-24 13:05:17');
INSERT INTO `tb_dfv_template_field` VALUES (81, 3, 217, 'testSampleList.materialAttrList.materialDescription', 1, 78, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (82, 3, 218, 'testSampleList.materialAttrList.materialEndUse', 2, 78, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (83, 3, 215, 'testSampleList.materialAttrList.materialColor', 3, 78, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (84, 3, 224, 'testSampleList.materialAttrList.materialTexture', 4, 78, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (85, 3, 222, 'testSampleList.materialAttrList.materialSampleRemark', 5, 78, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (86, 3, 212, 'testSampleList.materialAttrList.extFields', 6, 78, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (87, 3, 225, 'testSampleList.materialAttrList.extFields.materialCategory', 1, 86, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (88, 3, 227, 'testSampleList.materialAttrList.extFields.materialSku', 2, 86, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (89, 3, 226, 'testSampleList.materialAttrList.extFields.materialItem', 3, 86, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (90, 3, 8, 'testLineList', 6, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (91, 3, 259, 'testLineList.testLineInstanceId', 1, 90, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (92, 3, 258, 'testLineList.testLineId', 2, 90, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (93, 3, 246, 'testLineList.evaluationAlias', 3, 90, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (94, 3, 261, 'testLineList.testLineSeq', 4, 90, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (95, 3, 243, 'testLineList.citationId', 5, 90, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (96, 3, 244, 'testLineList.citationType', 6, 90, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (97, 3, 242, 'testLineList.citationFullName', 7, 90, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (98, 3, 253, 'testLineList.ppNo', 8, 90, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (99, 3, 252, 'testLineList.ppName', 9, 90, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (100, 3, 248, 'testLineList.externalInfo', 10, 90, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (101, 3, 628, 'testLineList.externalInfo.testItemId', 1, 100, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (102, 3, 629, 'testLineList.externalInfo.testItemName', 2, 100, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (103, 3, 626, 'testLineList.externalInfo.testCitationId', 3, 100, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (104, 3, 627, 'testLineList.externalInfo.testCitationName', 4, 100, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (105, 3, 624, 'testLineList.externalInfo.checkType', 5, 100, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (106, 3, 9, 'testResultList', 0, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (107, 3, 340, 'testResultList.testMatrixId', 1, 106, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (108, 3, 342, 'testResultList.testResultFullName', 2, 106, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (109, 3, 344, 'testResultList.testResultSeq', 3, 106, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (110, 3, 341, 'testResultList.testResult', 4, 106, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (111, 3, 354, 'testResultList.testResult.resultValue', 1, 110, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (112, 3, 353, 'testResultList.testResult.resultUnit', 2, 110, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (113, 4, 1, 'action', 1, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (114, 4, 2, 'header', 2, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (115, 4, 12, 'header.trfList', 1, 114, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (116, 4, 14, 'header.trfList.trfNo', 0, 115, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (117, 4, 11, 'header.refSystemId', 2, 114, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (118, 4, 4, 'order', 3, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (119, 4, 28, 'order.orderId', 1, 118, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (120, 4, 29, 'order.orderNo', 2, 118, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (121, 4, 42, 'order.serviceStartDate', 3, 118, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (122, 4, 49, 'order.testingStartDate', 4, 118, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-24 13:05:17');
INSERT INTO `tb_dfv_template_field` VALUES (123, 4, 48, 'order.testingEndDate', 5, 118, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-16 19:52:28');
INSERT INTO `tb_dfv_template_field` VALUES (124, 4, 6, 'quotationList', 4, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-23 16:47:47');
INSERT INTO `tb_dfv_template_field` VALUES (125, 4, 3, 'invoiceList', 5, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-23 16:50:06');
INSERT INTO `tb_dfv_template_field` VALUES (126, 4, 374, 'quotationList.quotationNo', 1, 124, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-23 16:48:17');
INSERT INTO `tb_dfv_template_field` VALUES (127, 4, 377, 'quotationList.serviceItemList', 2, 124, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-23 16:48:27');
INSERT INTO `tb_dfv_template_field` VALUES (128, 4, 424, 'quotationList.serviceItemList.serviceItemName', 1, 127, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-23 16:49:01');
INSERT INTO `tb_dfv_template_field` VALUES (129, 4, 419, 'quotationList.serviceItemList.ppNo', 2, 127, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-23 16:49:10');
INSERT INTO `tb_dfv_template_field` VALUES (130, 4, 429, 'quotationList.serviceItemList.testLineId', 3, 127, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-23 16:49:12');
INSERT INTO `tb_dfv_template_field` VALUES (131, 4, 412, 'quotationList.serviceItemList.citationId', 4, 127, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-23 16:49:25');
INSERT INTO `tb_dfv_template_field` VALUES (132, 4, 414, 'quotationList.serviceItemList.citationType', 5, 127, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-23 16:49:26');
INSERT INTO `tb_dfv_template_field` VALUES (133, 4, 413, 'quotationList.serviceItemList.citationName', 6, 127, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-23 16:49:33');
INSERT INTO `tb_dfv_template_field` VALUES (134, 4, 426, 'quotationList.serviceItemList.serviceItemSalesUnitPrice', 7, 127, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-23 16:49:44');
INSERT INTO `tb_dfv_template_field` VALUES (135, 4, 421, 'quotationList.serviceItemList.quantity', 8, 127, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-23 16:49:51');
INSERT INTO `tb_dfv_template_field` VALUES (136, 4, 443, 'invoiceList.invoiceNo', 1, 125, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-23 16:50:12');
INSERT INTO `tb_dfv_template_field` VALUES (137, 4, 446, 'invoiceList.quotationNoList', 2, 125, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-23 16:50:21');
INSERT INTO `tb_dfv_template_field` VALUES (138, 4, 441, 'invoiceList.currency', 3, 125, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-23 16:50:28');
INSERT INTO `tb_dfv_template_field` VALUES (139, 4, 447, 'invoiceList.totalAmount', 4, 125, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-23 16:50:34');
INSERT INTO `tb_dfv_template_field` VALUES (140, 4, 442, 'invoiceList.invoiceFileList', 5, 125, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-23 16:50:44');
INSERT INTO `tb_dfv_template_field` VALUES (141, 4, 450, 'invoiceList.invoiceFileList.fileName', 1, 140, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-23 16:50:49');
INSERT INTO `tb_dfv_template_field` VALUES (142, 4, 451, 'invoiceList.invoiceFileList.filePath', 2, 140, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-23 16:50:56');
INSERT INTO `tb_dfv_template_field` VALUES (143, 6, 630, 'trfNo', 0, 0, 1, 'system', '2023-05-25 18:44:34', 'system', '2023-05-25 18:44:34', '2023-05-26 10:43:33');
INSERT INTO `tb_dfv_template_field` VALUES (144, 6, 631, 'refSystemId', 1, 0, 1, 'system', '2023-05-25 18:44:34', 'system', '2023-05-25 18:44:34', '2023-05-26 10:43:35');
INSERT INTO `tb_dfv_template_field` VALUES (145, 6, 632, 'systemId', 3, 0, 1, 'system', '2023-05-25 18:44:34', 'system', '2023-05-25 18:44:34', '2023-05-26 10:43:36');
INSERT INTO `tb_dfv_template_field` VALUES (151, 5, 633, 'systemId', 1, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-26 16:56:54');
INSERT INTO `tb_dfv_template_field` VALUES (152, 5, 2, 'header', 2, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-26 16:56:56');
INSERT INTO `tb_dfv_template_field` VALUES (153, 5, 12, 'header.trfList', 0, 152, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-26 11:25:57');
INSERT INTO `tb_dfv_template_field` VALUES (154, 5, 14, 'header.trfList.trfNo', 0, 153, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-26 11:26:02');
INSERT INTO `tb_dfv_template_field` VALUES (155, 5, 11, 'header.refSystemId', 1, 152, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-26 11:26:07');
INSERT INTO `tb_dfv_template_field` VALUES (156, 5, 4, 'order', 3, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-26 16:56:58');
INSERT INTO `tb_dfv_template_field` VALUES (157, 5, 28, 'order.orderId', 0, 157, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-26 11:26:16');
INSERT INTO `tb_dfv_template_field` VALUES (158, 5, 29, 'order.orderNo', 1, 157, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-26 11:26:19');
INSERT INTO `tb_dfv_template_field` VALUES (159, 5, 1, 'action', 0, 0, 1, 'system', '2023-05-16 17:44:12', 'system', '2023-05-16 17:44:12', '2023-05-26 16:56:52');

-- ----------------------------
-- Table structure for tb_dfv_template_field_constraint
-- ----------------------------

CREATE TABLE `tb_dfv_template_field_constraint`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `template_id` bigint(20) NOT NULL COMMENT '模板id',
  `template_field_id` bigint(20) NOT NULL COMMENT '模板属性id',
  `field_id` bigint(20) NOT NULL COMMENT '属性id',
  `field_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '约束类型',
  `constraint_value1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '约束条件值1',
  `constraint_value2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '约束条件值2',
  `field_id_ref` bigint(20) NOT NULL COMMENT '关联属性：例如A属性的值要大于B属性值的约束时，需要指定B属性的属性id',
  `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `sort_num` int(11) NOT NULL COMMENT '排序',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NOT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NOT NULL COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_date`(`created_date`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 204 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'DFV-验证实例-模板属性规则' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_dfv_template_field_constraint
-- ----------------------------
INSERT INTO `tb_dfv_template_field_constraint` VALUES (10, 1, 9, 42, 'order.serviceStartDate', 'NotNull', NULL, NULL, 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (11, 1, 10, 7, 'reportList', 'NotEmpty', NULL, NULL, 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (12, 1, 11, 468, 'reportList.reportId', 'NotEmpty', NULL, NULL, 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (13, 1, 12, 470, 'reportList.reportNo', 'NotEmpty', NULL, NULL, 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (14, 1, 13, 466, 'reportList.reportDueDate', 'NotNull', NULL, NULL, 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (24, 2, 22, 42, 'order.serviceStartDate', 'NotNull', NULL, NULL, 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (25, 2, 23, 49, 'order.testingStartDate', 'NotNull', NULL, NULL, 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (30, 2, 24, 7, 'reportList', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (31, 2, 25, 468, 'reportList.reportId', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (32, 2, 26, 470, 'reportList.reportNo', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (33, 2, 27, 466, 'reportList.reportDueDate', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (43, 3, 37, 42, 'order.serviceStartDate', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (44, 3, 38, 49, 'order.testingStartDate', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-24 13:05:17');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (45, 3, 39, 48, 'order.testingEndDate', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (46, 3, 40, 39, 'order.sampleList', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (47, 3, 41, 131, 'order.sampleList.sampleInstanceId', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (48, 3, 42, 130, 'order.sampleList.sampleAttrList', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (49, 3, 44, 137, 'order.sampleList.sampleAttrList.labelCode', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (50, 3, 45, 138, 'order.sampleList.sampleAttrList.labelName', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (51, 3, 46, 139, 'order.sampleList.sampleAttrList.labelValue', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-25 22:33:02');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (52, 3, 46, 139, 'order.sampleList.sampleAttrList.labelValue', 'Size', '1', '64', 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (53, 3, 47, 133, 'order.sampleList.sampleAttrList.attrSeq', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (54, 3, 48, 134, 'order.sampleList.sampleAttrList.customerLabel', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (55, 3, 43, 129, 'order.sampleList.conclusion', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (56, 3, 49, 606, 'order.sampleList.conclusion.conclusionCode', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (57, 3, 50, 609, 'order.sampleList.conclusion.customerConclusion', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (59, 3, 52, 7, 'reportList', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (60, 3, 53, 468, 'reportList.reportId', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (61, 3, 54, 470, 'reportList.reportNo', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (62, 3, 55, 466, 'reportList.reportDueDate', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (63, 3, 56, 469, 'reportList.reportMatrixList', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (64, 3, 58, 501, 'reportList.reportMatrixList.testMatrixId', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (65, 3, 59, 502, 'reportList.reportMatrixList.testSampleInstanceId', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (66, 3, 60, 500, 'reportList.reportMatrixList.testLineInstanceId', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (67, 3, 61, 495, 'reportList.reportMatrixList.conclusion', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (68, 3, 63, 545, 'reportList.reportMatrixList.conclusion.conclusionCode', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-24 13:05:17');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (69, 3, 64, 548, 'reportList.reportMatrixList.conclusion.customerConclusion', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-24 13:05:17');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (71, 3, 62, 599, 'reportList.reportMatrixList.testMatrixFileList', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (72, 3, 66, 613, 'reportList.reportMatrixList.testMatrixFileList.fileName', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (73, 3, 67, 616, 'reportList.reportMatrixList.testMatrixFileList.fileType', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (74, 3, 68, 614, 'reportList.reportMatrixList.testMatrixFileList.filePath', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (75, 3, 68, 614, 'reportList.reportMatrixList.testMatrixFileList.filePath', 'Size', '1', '255', 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (76, 3, 57, 467, 'reportList.reportFileList', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (77, 3, 69, 573, 'reportList.reportFileList.languageId', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (78, 3, 70, 569, 'reportList.reportFileList.fileName', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (79, 3, 71, 572, 'reportList.reportFileList.fileType', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (80, 3, 72, 570, 'reportList.reportFileList.filePath', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (81, 3, 72, 570, 'reportList.reportFileList.filePath', 'Size', '1', '255', 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (82, 3, 73, 10, 'testSampleList', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (83, 3, 74, 205, 'testSampleList.testSampleInstanceId', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (84, 3, 75, 204, 'testSampleList.testSampleGroupList', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (85, 3, 79, 211, 'testSampleList.testSampleGroupList.testSampleInstanceId', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-24 13:05:17');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (86, 3, 80, 210, 'testSampleList.testSampleGroupList.mainSampleFlag', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-24 13:05:17');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (87, 3, 76, 209, 'testSampleList.testSampleType', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (88, 3, 77, 208, 'testSampleList.testSampleSeq', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (89, 3, 78, 202, 'testSampleList.materialAttrList', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (90, 3, 81, 217, 'testSampleList.materialAttrList.materialDescription', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (91, 3, 81, 217, 'testSampleList.materialAttrList.materialDescription', 'Size', '1', '100', 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (92, 3, 82, 218, 'testSampleList.materialAttrList.materialEndUse', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (93, 3, 82, 218, 'testSampleList.materialAttrList.materialEndUse', 'Size', '1', '32', 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (94, 3, 83, 215, 'testSampleList.materialAttrList.materialColor', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (95, 3, 83, 215, 'testSampleList.materialAttrList.materialColor', 'Size', '1', '32', 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (96, 3, 84, 224, 'testSampleList.materialAttrList.materialTexture', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (97, 3, 84, 224, 'testSampleList.materialAttrList.materialTexture', 'Size', '1', '32', 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (98, 3, 85, 222, 'testSampleList.materialAttrList.materialSampleRemark', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (99, 3, 85, 222, 'testSampleList.materialAttrList.materialSampleRemark', 'Size', '1', '255', 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (100, 3, 86, 212, 'testSampleList.materialAttrList.extFields', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (101, 3, 87, 225, 'testSampleList.materialAttrList.extFields.materialCategory', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (102, 3, 87, 225, 'testSampleList.materialAttrList.extFields.materialCategory', 'Size', '1', '64', 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (103, 3, 88, 227, 'testSampleList.materialAttrList.extFields.materialSku', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (104, 3, 88, 227, 'testSampleList.materialAttrList.extFields.materialSku', 'Size', '1', '100', 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (105, 3, 89, 226, 'testSampleList.materialAttrList.extFields.materialItem', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (106, 3, 89, 226, 'testSampleList.materialAttrList.extFields.materialItem', 'Size', '1', '30', 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (107, 3, 90, 8, 'testLineList', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (108, 3, 91, 259, 'testLineList.testLineInstanceId', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (109, 3, 92, 258, 'testLineList.testLineId', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (110, 3, 93, 246, 'testLineList.evaluationAlias', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (111, 3, 94, 261, 'testLineList.testLineSeq', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (112, 3, 95, 243, 'testLineList.citationId', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (113, 3, 96, 244, 'testLineList.citationType', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (114, 3, 97, 242, 'testLineList.citationFullName', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (115, 3, 98, 253, 'testLineList.ppNo', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (116, 3, 99, 252, 'testLineList.ppName', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (117, 3, 100, 248, 'testLineList.externalInfo', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (118, 3, 101, 628, 'testLineList.externalInfo.testItemId', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (119, 3, 102, 629, 'testLineList.externalInfo.testItemName', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (120, 3, 102, 629, 'testLineList.externalInfo.testItemName', 'Size', '1', '64', 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-23 22:13:36');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (121, 3, 103, 626, 'testLineList.externalInfo.testCitationId', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (122, 3, 104, 627, 'testLineList.externalInfo.testCitationName', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (123, 3, 105, 624, 'testLineList.externalInfo.checkType', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (124, 3, 106, 9, 'testResultList', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (125, 3, 107, 340, 'testResultList.testMatrixId', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (126, 3, 108, 342, 'testResultList.testResultFullName', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (127, 3, 109, 344, 'testResultList.testResultSeq', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (128, 3, 110, 341, 'testResultList.testResult', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (129, 3, 111, 354, 'testResultList.testResult.resultValue', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (130, 3, 111, 354, 'testResultList.testResult.resultValue', 'Size', '1', '32', 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-23 22:13:50');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (131, 3, 112, 353, 'testResultList.testResult.resultUnit', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (132, 3, 112, 353, 'testResultList.testResult.resultUnit', 'Size', '1', '20', 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-23 22:13:51');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (142, 4, 121, 42, 'order.serviceStartDate', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (143, 4, 122, 49, 'order.testingStartDate', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-24 13:05:17');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (144, 4, 124, 6, 'quotationList', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (145, 4, 126, 374, 'quotationList.quotationNo', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (146, 4, 127, 377, 'quotationList.serviceItemList', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (147, 4, 128, 424, 'quotationList.serviceItemList.serviceItemName', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (148, 4, 128, 424, 'quotationList.serviceItemList.serviceItemName', 'Size', '1', '64', 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-23 22:13:57');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (149, 4, 129, 419, 'quotationList.serviceItemList.ppNo', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (150, 4, 130, 429, 'quotationList.serviceItemList.testLineId', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (151, 4, 131, 412, 'quotationList.serviceItemList.citationId', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (152, 4, 132, 414, 'quotationList.serviceItemList.citationType', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (153, 4, 133, 413, 'quotationList.serviceItemList.citationName', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (154, 4, 134, 426, 'quotationList.serviceItemList.serviceItemSalesUnitPrice', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (155, 4, 135, 421, 'quotationList.serviceItemList.quantity', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (156, 4, 125, 3, 'invoiceList', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (157, 4, 136, 443, 'invoiceList.invoiceNo', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (158, 4, 136, 443, 'invoiceList.invoiceNo', 'Size', '1', '64', 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-23 22:14:01');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (159, 4, 137, 446, 'invoiceList.quotationNoList', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (160, 4, 138, 441, 'invoiceList.currency', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (161, 4, 139, 447, 'invoiceList.totalAmount', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (162, 4, 140, 442, 'invoiceList.invoiceFileList', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (163, 4, 141, 450, 'invoiceList.invoiceFileList.fileName', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (164, 4, 142, 451, 'invoiceList.invoiceFileList.filePath', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-16 20:02:40');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (165, 4, 142, 451, 'invoiceList.invoiceFileList.filePath', 'Size', '1', '255', 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-23 22:14:03');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (184, 5, 151, 632, 'systemId', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-26 11:27:43');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (185, 5, 151, 632, 'systemId', 'Dict', 'SystemId', NULL, 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-26 11:27:43');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (186, 5, 152, 2, 'header', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-26 11:13:59');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (187, 5, 153, 12, 'header.trfList', 'NotEmpty', NULL, NULL, 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-26 11:13:59');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (188, 5, 154, 14, 'header.trfList.trfNo', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-26 11:13:59');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (189, 5, 154, 14, 'header.trfList.trfNo', 'Size', '1', '32', 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-26 11:13:59');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (190, 5, 155, 11, 'header.refSystemId', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-26 11:13:59');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (191, 5, 155, 11, 'header.refSystemId', 'Dict', 'RefSystemId', NULL, 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-26 11:13:59');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (192, 5, 156, 4, 'order', 'NotNull', NULL, NULL, 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-26 11:13:59');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (193, 5, 157, 28, 'order.orderId', 'NotEmpty', NULL, NULL, 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-26 11:13:59');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (194, 5, 158, 29, 'order.orderNo', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-26 11:31:53');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (195, 5, 158, 29, 'order.orderNo', 'Size', '1', '64', 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-26 11:32:18');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (196, 6, 145, 632, 'systemId', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-26 11:34:42');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (197, 6, 145, 632, 'systemId', 'Dict', 'SystemId', NULL, 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-26 11:35:13');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (198, 6, 144, 631, 'refSystemId', 'NotNull', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-26 11:37:05');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (199, 6, 144, 631, 'refSystemId', 'Dict', 'RefSystemId', NULL, 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-26 11:36:22');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (200, 6, 143, 630, 'trfNo', 'NotEmpty', NULL, NULL, 0, 1, 0, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-26 11:36:55');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (201, 6, 143, 630, 'trfNo', 'Size', '1', '64', 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-26 11:36:36');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (202, 5, 159, 1, 'action', 'NotNull', NULL, NULL, 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-26 16:58:01');
INSERT INTO `tb_dfv_template_field_constraint` VALUES (203, 5, 159, 1, 'action', 'Dict', 'SCI-SyncAction', NULL, 0, 1, 1, 'system', '2023-05-16 20:02:37', 'system', '2023-05-16 20:02:40', '2023-05-26 17:01:38');

SET FOREIGN_KEY_CHECKS = 1;
