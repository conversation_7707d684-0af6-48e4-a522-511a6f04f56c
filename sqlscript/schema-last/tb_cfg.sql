/*
 Navicat Premium Data Transfer

 Source Server         : SODA-order-UAT
 Source Server Type    : MySQL
 Source Server Version : 50647
 Source Host           : sgsdbcne2mysqlarchmasteruat.mysql.database.chinacloudapi.cn:3306
 Source Schema         : preorder

 Target Server Type    : MySQL
 Target Server Version : 50647
 File Encoding         : 65001

 Date: 26/05/2023 18:51:41
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tb_cfg_event_subscribe
-- ----------------------------

CREATE TABLE `tb_cfg_event_subscribe`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `system_id` int(11) NOT NULL COMMENT '系统标识',
  `event_code` int(11) NOT NULL COMMENT '事件code',
  `api_id` bigint(20) NOT NULL COMMENT '对应触发api的id',
  `notify_rule` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '事件通知规则 低位开始，第一位：多次发送标识，第二位：顺序发送标识，第三位：trf允许合并发送标识',
  `notify_data` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '事件触发时需要发生给客户的数据 0:不需要 ，低位开始，第一位：Invoice，第二位：quotation，第三位：。。。具体参见标准对象定义',
  `created_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NOT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_refSysId_eventCode_apiId`(`system_id`, `event_code`, `api_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_cfg_event_subscribe
-- ----------------------------
INSERT INTO `tb_cfg_event_subscribe` VALUES (1, 7, 30, 1, 24, 0, 'YANG', '2023-05-11 19:20:49', 'YANG', '2023-05-11 19:21:00', '2023-05-26 16:00:58');
INSERT INTO `tb_cfg_event_subscribe` VALUES (2, 7, 40, 1, 24, 0, 'YANG', '2023-05-11 19:20:49', 'YANG', '2023-05-11 19:21:00', '2023-05-26 16:00:59');
INSERT INTO `tb_cfg_event_subscribe` VALUES (3, 7, 60, 1, 24, 0, 'YANG', '2023-05-11 19:20:49', 'YANG', '2023-05-11 19:21:00', '2023-05-26 16:01:02');
INSERT INTO `tb_cfg_event_subscribe` VALUES (4, 7, 70, 1, 40, 0, 'YANG', '2023-05-25 17:06:20', 'YANG', '2023-05-25 17:06:26', '2023-05-26 16:01:20');

-- ----------------------------
-- Table structure for tb_cfg_info
-- ----------------------------

CREATE TABLE `tb_cfg_info`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `csutomer_group` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置方分组',
  `customer_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置方编号',
  `identity_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置方身份标识',
  `product_line` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'BU',
  `config_type` int(10) UNSIGNED NOT NULL COMMENT '配置参数类型 1.技术参数配置 2.业务参数配置',
  `config_key` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置key,如：ORDER_TRF_REL、TRF_CHANGE_MODE、TRF_UPDATE_LIMIT',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置值',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `created_date` datetime(0) NOT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_cfg_info
-- ----------------------------
INSERT INTO `tb_cfg_info` VALUES (1, 'CG0001085', '', '7', 'SL', 2, 'TrfStatusControl', '{\r\n    \"trfOrderRelationshipRule\": 2,\r\n    \"statusRule\": 4,\r\n    \"statusFlow\": [\r\n        1,\r\n        4,\r\n        5,\r\n        6,\r\n        7\r\n    ],\r\n    \"statusMapping\": {\r\n        \"4\": \"4\",\r\n        \"5\": \"5\",\r\n        \"6\": \"6\",\r\n        \"7\": \"7\"\r\n    }\r\n}', 'system', '2023-05-11 15:12:36', 'system', '2023-05-15 08:20:58', '2023-05-22 16:08:39');
INSERT INTO `tb_cfg_info` VALUES (2, 'CG0001085', '', '7', 'SL', 2, 'RdSource', 'SCI', 'system', '2023-05-11 15:12:36', 'system', '2023-05-15 08:20:58', '2023-05-15 08:39:29');

-- ----------------------------
-- Table structure for tb_cfg_support_system
-- ----------------------------

CREATE TABLE `tb_cfg_support_system`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `system_id` int(11) NOT NULL COMMENT '系统标识',
  `system_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户名称',
  `system_type` tinyint(4) NULL DEFAULT NULL COMMENT '系统类别  1.客户系统 2.执行系统',
  `system_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户编号',
  `system_group_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户分组编码',
  `system_desc` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对客户的描述',
  `created_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NOT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_refSystemId`(`system_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_cfg_system_api
-- ----------------------------

CREATE TABLE `tb_cfg_system_api`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `system_id` int(11) NOT NULL COMMENT '系统标识',
  `protocol_type` tinyint(4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '协议类型 1.http 2.queue',
  `request_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求地址',
  `request_method` tinyint(4) UNSIGNED NULL DEFAULT NULL COMMENT '请求方法 1.get 2.post ',
  `request_body_template` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求数据的模板',
  `response_body_template` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '返回数据的模板',
  `created_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NOT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_cfg_system_api
-- ----------------------------
INSERT INTO `tb_cfg_system_api` VALUES (1, 7, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{}', '{}', 'YANG', '2023-05-11 19:22:59', 'YANG', '2023-05-11 19:23:07', '2023-05-26 15:49:46');

SET FOREIGN_KEY_CHECKS = 1;
