/*
 Navicat Premium Data Transfer

 Source Server         : SODA-order-UAT
 Source Server Type    : MySQL
 Source Server Version : 50647
 Source Host           : sgsdbcne2mysqlarchmasteruat.mysql.database.chinacloudapi.cn:3306
 Source Schema         : preorder

 Target Server Type    : MySQL
 Target Server Version : 50647
 File Encoding         : 65001

 Date: 26/05/2023 18:59:31
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tb_task_execute_log
-- ----------------------------

CREATE TABLE `tb_task_execute_log`  (
  `id` bigint(20) NOT NULL,
  `task_id` bigint(20) NOT NULL COMMENT '任务id',
  `group_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分组key',
  `ext_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '外部id',
  `handler_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务处理器名称',
  `task_parameters` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '任务参数',
  `execute_result` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行结果',
  `execute_ip` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行节点ip',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `created_date` datetime(0) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_task_info
-- ----------------------------

CREATE TABLE `tb_task_info`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `task_id` bigint(20) NOT NULL COMMENT '任务id',
  `ext_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '外部业务id,可重复',
  `group_key` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '同一个extId下的分组key,可重复',
  `handler_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务处理器名称',
  `task_parameters` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '任务参数',
  `depend_task` bigint(20) NULL DEFAULT NULL COMMENT '前置taskId',
  `expired_time` bigint(20) UNSIGNED NULL DEFAULT 0 COMMENT '过期时间，时间戳 0表示不过期',
  `task_status` tinyint(3) UNSIGNED NULL DEFAULT 0 COMMENT '请求状态 0:初始化 1:执行中  2:成功 3:失败 4:取消 9:过期',
  `fail_count` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '请求失败次数',
  `max_fail` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '最大允许失败次数',
  `retry_count` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '重试次数',
  `max_retry` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '最大允许重试次数',
  `retry_time_millis` bigint(20) UNSIGNED NULL DEFAULT 180000 COMMENT '重试间隔时间,毫秒',
  `last_exec_time` bigint(20) UNSIGNED NULL DEFAULT 0 COMMENT '最后执行时间',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `created_date` datetime(0) NOT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_taskId`(`task_id`) USING BTREE,
  INDEX `idx_createdDate`(`created_date`) USING BTREE,
  INDEX `idx_extId`(`ext_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
