/*
 Navicat Premium Data Transfer

 Source Server         : SODA-order-UAT
 Source Server Type    : MySQL
 Source Server Version : 50647
 Source Host           : sgsdbcne2mysqlarchmasteruat.mysql.database.chinacloudapi.cn:3306
 Source Schema         : preorder

 Target Server Type    : MySQL
 Target Server Version : 50647
 File Encoding         : 65001

 Date: 26/05/2023 18:50:05
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tb_trf
-- ----------------------------

CREATE TABLE `tb_trf`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Trf 编号',
  `status` tinyint(4) NULL DEFAULT NULL COMMENT 'Trf 状态（1：待开单、2：报价中、3：已开单、4：待检测、5：检测中、6：已检测、7：已完成、9：已取消）',
  `service_type` tinyint(4) NULL DEFAULT NULL COMMENT '1：Regular、2：Express、3：Double express、4：Emergency',
  `product_category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '1：TX、2：FW',
  `self_test_flag` tinyint(4) NULL DEFAULT NULL,
  `source` int(11) NULL DEFAULT NULL COMMENT 'TRF来源：1 Online TRF，2 Order To TRF',
  `channel` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `system_id` int(11) NULL DEFAULT NULL COMMENT '调用方系统',
  `integration_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '样品Level，1 客户提供Component  2、SGS 拆样',
  `sample_level` int(11) NULL DEFAULT NULL,
  `ref_system_id` int(11) NULL DEFAULT NULL,
  `lab_id` int(11) NULL DEFAULT NULL COMMENT '实验室id',
  `lab_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `bu_id` int(11) NULL DEFAULT NULL,
  `bu_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `trf_remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `pending_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `pending_flag` tinyint(4) NULL DEFAULT 0 COMMENT '0: activate, 1: pending',
  `pending_remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Pending备注',
  `ext_fields` json NULL COMMENT '扩展信息json结构',
  `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_trfNo`(`trf_no`, `ref_system_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24170398875217921 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'trf' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_attachment
-- ----------------------------

CREATE TABLE `tb_trf_attachment`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_id` bigint(20) NULL DEFAULT NULL,
  `object_type` tinyint(4) NULL DEFAULT NULL,
  `object_id` bigint(20) NULL DEFAULT NULL,
  `file_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `file_type` int(4) NULL DEFAULT NULL,
  `file_size` bigint(20) NULL DEFAULT NULL,
  `language_id` int(11) NULL DEFAULT NULL,
  `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_object_id_type`(`object_id`, `object_type`) USING BTREE,
  INDEX `idx_trf_id`(`trf_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24170399332397057 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_care_label
-- ----------------------------

CREATE TABLE `tb_trf_care_label`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_id` bigint(20) NULL DEFAULT NULL,
  `test_sample_ids` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `care_instruction` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `radio_type` tinyint(4) NULL DEFAULT NULL,
  `select_country` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `care_label_seq` int(4) NULL DEFAULT NULL,
  `img_array` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `select_img_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `cloud_id` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `care_label_file_path` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `product_item_no` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_trf_id`(`trf_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_customer
-- ----------------------------

CREATE TABLE `tb_trf_customer`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_id` bigint(20) NULL DEFAULT NULL,
  `customer_instance_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `customer_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `customer_usage` tinyint(4) NULL DEFAULT NULL COMMENT '1、applicant\r\n2、payer\r\n3、buyer\r\n4、agent\r\n5、oem\r\n6、supplier\r\n7、manufacture',
  `boss_no` int(11) NULL DEFAULT NULL,
  `customer_group_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `customer_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `customer_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `payment_term` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `black_flag` tinyint(4) NULL DEFAULT NULL,
  `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_trf_id`(`trf_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24170398900383747 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '客户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_customer_contact
-- ----------------------------

CREATE TABLE `tb_trf_customer_contact`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_id` bigint(20) NULL DEFAULT NULL,
  `trf_customer_id` bigint(20) NULL DEFAULT NULL,
  `contact_address_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `boss_contact_id` bigint(20) NULL DEFAULT NULL,
  `boss_site_use_id` bigint(20) NULL DEFAULT NULL,
  `contact_usage` tinyint(4) NULL DEFAULT NULL,
  `contact_region_account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `contact_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `contact_email` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `contact_phone` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `contact_telephone` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `contact_fax` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `responsible_team_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_customer_id`(`trf_customer_id`) USING BTREE,
  INDEX `idx_boss_contact_id`(`boss_contact_id`) USING BTREE,
  INDEX `idx_boss_site_use_id`(`boss_site_use_id`) USING BTREE,
  INDEX `idx_trf_id`(`trf_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24170399068155906 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_customer_lang
-- ----------------------------

CREATE TABLE `tb_trf_customer_lang`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_id` bigint(20) NULL DEFAULT NULL,
  `trf_customer_id` bigint(20) NULL DEFAULT NULL,
  `language_id` tinyint(4) NULL DEFAULT NULL,
  `customer_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `customer_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_customer_id`(`trf_customer_id`) USING BTREE,
  INDEX `idx_trf_id`(`trf_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24170399097516035 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_invoice
-- ----------------------------

CREATE TABLE `tb_trf_invoice`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_id` bigint(20) NOT NULL,
  `system_id` int(11) NULL DEFAULT NULL COMMENT '调用系统id',
  `invoice_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `order_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `order_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_trf_id`(`trf_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_order
-- ----------------------------

CREATE TABLE `tb_trf_order`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_id` bigint(20) NULL DEFAULT NULL,
  `system_id` int(11) NULL DEFAULT NULL COMMENT '归属系统id',
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `order_status` int(11) NULL DEFAULT NULL,
  `bound_status` int(11) NOT NULL DEFAULT 0 COMMENT 'Trf绑定状态（1：已绑定、2：已解绑）',
  `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_trf_id`(`trf_id`) USING BTREE,
  INDEX `idx_order_no`(`system_id`, `order_no`, `bound_status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24170418104025089 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_product
-- ----------------------------

CREATE TABLE `tb_trf_product`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_id` bigint(20) NOT NULL,
  `template_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'dff form id',
  `object_type` tinyint(4) NOT NULL COMMENT '对象类型：1 Product,2 Sample',
  `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_trf_id`(`trf_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24170399370145794 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_product_attr
-- ----------------------------

CREATE TABLE `tb_trf_product_attr`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_id` bigint(20) NULL DEFAULT NULL,
  `trf_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `trf_product_id` bigint(20) NOT NULL,
  `product_instance_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `template_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `object_type` tinyint(4) NOT NULL COMMENT '对象类型：1 Product,2 Sample',
  `language_id` int(11) NULL DEFAULT NULL,
  `attr_seq` int(11) NULL DEFAULT NULL,
  `label_code` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `label_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `label_value` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `field_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `customer_label` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `data_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `active_indicator` int(1) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_trf_product_id`(`trf_product_id`) USING BTREE,
  INDEX `idx_trf_no`(`trf_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24170399470809100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_quotation
-- ----------------------------

CREATE TABLE `tb_trf_quotation`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_id` bigint(20) NULL DEFAULT NULL,
  `system_id` int(11) NULL DEFAULT NULL COMMENT '调用系统id',
  `quotation_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `order_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `order_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_trf_id`(`trf_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_report
-- ----------------------------

CREATE TABLE `tb_trf_report`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_id` bigint(20) NOT NULL,
  `system_id` int(11) NULL DEFAULT NULL COMMENT '调用系统id',
  `order_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `order_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `report_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `report_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_trf_id`(`trf_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_service_requirement
-- ----------------------------

CREATE TABLE `tb_trf_service_requirement`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_id` bigint(20) NULL DEFAULT NULL,
  `accreditation` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `need_conclusion` int(11) NULL DEFAULT NULL COMMENT 'Need Conclusion, 0-no; 1-yes',
  `need_draft` int(11) NULL DEFAULT NULL,
  `need_photo` tinyint(4) NULL DEFAULT NULL,
  `invoice_type` tinyint(4) NULL DEFAULT NULL,
  `sample_save_duration` int(11) NULL DEFAULT NULL,
  `report_language` int(11) NULL DEFAULT NULL,
  `report_header` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `report_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `other_request_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_trf_id`(`trf_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_service_requirement_delivery
-- ----------------------------

CREATE TABLE `tb_trf_service_requirement_delivery`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_id` bigint(20) NULL DEFAULT NULL,
  `trf_service_requirement_id` bigint(20) NULL DEFAULT NULL,
  `delivery_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `required` tinyint(4) NULL DEFAULT NULL,
  `delivery_to` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `delivery_others` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `delivery_cc` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `delivery_way` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_trf_id`(`trf_id`) USING BTREE,
  INDEX `idx_service_requirement_id`(`trf_service_requirement_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_service_requirement_lang
-- ----------------------------

CREATE TABLE `tb_trf_service_requirement_lang`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_id` bigint(20) NULL DEFAULT NULL,
  `trf_service_requirement_id` bigint(20) NULL DEFAULT NULL,
  `language_id` int(11) NULL DEFAULT NULL,
  `report_header` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `report_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_service_requirement_report_id`(`trf_service_requirement_id`) USING BTREE,
  INDEX `idx_trf_id`(`trf_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_test_item
-- ----------------------------

CREATE TABLE `tb_trf_test_item`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_id` bigint(20) NULL DEFAULT NULL,
  `test_line_id` int(11) NULL DEFAULT NULL,
  `evaluation_alias` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `evaluation_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `test_line_seq` int(11) NULL DEFAULT NULL,
  `pp_no` int(11) NULL DEFAULT NULL,
  `pp_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `citation_id` int(11) NULL DEFAULT NULL,
  `citation_type` int(11) NULL DEFAULT NULL,
  `citation_full_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `external_test_item_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `external_test_item_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `external_test_citation_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `external_test_citation_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `external_check_type` int(11) NULL DEFAULT NULL,
  `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_trf_id`(`trf_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24170399722467329 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_test_item_lang
-- ----------------------------

CREATE TABLE `tb_trf_test_item_lang`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_id` bigint(20) NULL DEFAULT NULL,
  `trf_test_item_id` bigint(20) NULL DEFAULT NULL,
  `language_id` int(11) NULL DEFAULT NULL,
  `evaluation_alias` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `evaluation_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `pp_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `citation_full_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `external_test_item_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `external_test_citation_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_trf_id`(`trf_id`) USING BTREE,
  INDEX `idx_trf_test_item_id`(`trf_test_item_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_test_sample
-- ----------------------------

CREATE TABLE `tb_trf_test_sample`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_id` bigint(20) NULL DEFAULT NULL,
  `test_sample_instance_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `parent_test_sample_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `test_sample_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `external_sample_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `test_sample_type` int(11) NULL DEFAULT NULL,
  `test_sample_seq` int(11) NULL DEFAULT NULL,
  `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_trf_id`(`trf_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24170399605026817 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_test_sample_file
-- ----------------------------

CREATE TABLE `tb_trf_test_sample_file`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_id` bigint(20) NULL DEFAULT NULL,
  `trf_test_sample_id` bigint(20) NULL DEFAULT NULL,
  `file_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `file_type` tinyint(4) NULL DEFAULT NULL,
  `file_size` bigint(20) NULL DEFAULT NULL,
  `language_id` int(11) NULL DEFAULT NULL,
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_trf_id`(`trf_id`) USING BTREE,
  INDEX `idx_trf_test_sample_id`(`trf_test_sample_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_test_sample_group
-- ----------------------------

CREATE TABLE `tb_trf_test_sample_group`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_id` bigint(20) NULL DEFAULT NULL,
  `trf_test_sample_id` bigint(20) NULL DEFAULT NULL,
  `test_sample_instance_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试样实例id',
  `main_sample_flag` tinyint(4) NULL DEFAULT 0,
  `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_trf_id`(`trf_id`) USING BTREE,
  INDEX `idx_trf_test_sample_id`(`trf_test_sample_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_test_sample_material
-- ----------------------------

CREATE TABLE `tb_trf_test_sample_material`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `trf_id` bigint(20) NULL DEFAULT NULL,
  `trf_test_sample_id` bigint(20) NULL DEFAULT NULL,
  `material_composition` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `material_description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `material_end_use` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `material_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `material_color` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `material_texture` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `material_other_sample_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `material_applicable_flag` tinyint(4) NULL DEFAULT NULL,
  `material_sample_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `material_category` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `material_sku` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `material_item` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_created_time`(`created_date`) USING BTREE,
  INDEX `idx_trf_id`(`trf_id`) USING BTREE,
  INDEX `idx_trf_test_sample_id`(`trf_test_sample_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24170399621804036 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_todo_info
-- ----------------------------

CREATE TABLE `tb_trf_todo_info`  (
  `Id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键Id',
  `ProductLineCode` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'productLineCode BU',
  `RefSystemId` int(11) NOT NULL COMMENT '客户系统Id, 1：Customer Portal; 2：SGSMart; 3：Online Booking; 4：Semir; 5：Anta; 6、抽检工具; 7：Shein',
  `LabCode` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '所在的Lab',
  `TrfNo` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '客户系统进单单号',
  `Content` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '客户Trf JSON报文',
  `TrfStatus` int(11) NULL DEFAULT 0 COMMENT 'TRF状态：1-待开单；2-报价中；3-已开单；4-待检测；5-检测中；6-已检测；7-已完成；9-已取消',
  `CreatedBy` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `CreatedDate` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `ModifiedBy` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `ModifiedDate` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `idx_trfNo`(`ProductLineCode`, `RefSystemId`, `TrfNo`, `LabCode`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 58040 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_trf_todo_list
-- ----------------------------

CREATE TABLE `tb_trf_todo_list`  (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `TrfNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `TrfSubmissionDate` datetime(0) NULL DEFAULT NULL,
  `LabCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'lab code',
  `BuyerNameEN` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ApplicantNameEN` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `PayerNameEN` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `LabContact` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Status` int(11) NOT NULL DEFAULT 0,
  `Source` int(11) NULL DEFAULT NULL COMMENT '1 oldsgsmart,2newsgsmart,3olb',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `idx_trf_todo_list_TrfNo`(`TrfNo`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 57219 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
