set @maxid2=0;
select @maxid2:=IFNULL(MAX(id),0) from tb_cfg_system_api;
INSERT INTO tb_cfg_system_api ( id, system_id, protocol_type, request_url, request_method, request_body_template, response_body_template, remark, created_by, created_date, modified_by, modified_date, last_modified_timestamp ) VALUES ( @maxid2+1, 10002, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{}', '{}', 'SGSMart回传能力配置', 'Walley', now(), 'Walley', now(), now());

-- 新增SGSMart事件订阅
set @maxid=0;
select @maxid:=IFNULL(MAX(id),0) from tb_cfg_event_subscribe;
select @maxid2:=IFNULL(MAX(id),0) from tb_cfg_system_api;
INSERT INTO `tb_cfg_event_subscribe` ( `id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp` ) VALUES ( @maxid+1, 10002, 10, @maxid2, 24, 2023, 'messageNotifyHandler', '', 'Walley', NOW(), 'Walley', NOW(), NOW());
INSERT INTO `tb_cfg_event_subscribe` ( `id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp` ) VALUES ( @maxid+2, 10002, 40, @maxid2, 24, 2023, 'messageNotifyHandler', '', 'Walley', NOW(), 'Walley', NOW(), NOW());
INSERT INTO `tb_cfg_event_subscribe` ( `id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp` ) VALUES ( @maxid+3, 10002, 60, @maxid2, 24, 2023, 'messageNotifyHandler', '', 'Walley', NOW(), 'Walley', NOW(), NOW());
INSERT INTO `tb_cfg_event_subscribe` ( `id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp` ) VALUES ( @maxid+4, 10002, 100, @maxid2, 24, 2023, 'messageNotifyHandler', '', 'Walley', NOW(), 'Walley', NOW(), NOW());
INSERT INTO `tb_cfg_event_subscribe` ( `id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp` ) VALUES ( @maxid+5, 10002, 110, @maxid2, 24, 2023, 'unPendingHandler', '', 'Walley', NOW(), 'Walley', NOW(), NOW());


ALTER TABLE `tb_trf_report`
    ADD COLUMN `report_status` int UNSIGNED NULL COMMENT '报告状态' AFTER `report_no`;

UPDATE `tb_cfg_info` SET `config_value` = '{\n    \"pendingDecision\": \"hold\",\n    \"actionStatusMapping\": {\n        \"SyncToOrder\": {\n            \"statusRule\": 1\n        },\n        \"SyncConfirmed\": {\n            \"statusRule\": 1\n        },\n        \"SyncTesting\": {\n            \"statusRule\": 1\n        },\n        \"SyncCompleted\": {\n            \"statusRule\": 2\n        },\n        \"SyncClosed\": {\n            \"statusRule\": 2\n        },\n        \"CustomerCancelTrf\": {\n            \"ctrlMapping\": {\n                \"1\": 0\n            }\n        },\r\n				\"SyncReviseReport\":{\r\n					  \"statusRule\": 1\r\n				},\n        \"PreOrderCancelTrf\": {\n            \"ctrlMapping\": {\n                \"1\": 0,\n                \"2\": 0,\n                \"3\": 0,\n                \"4\": 0,\n                \"5\": 0\n            }\n        }\n    }\n}' WHERE `id` = 5;
UPDATE `tb_cfg_info` SET `config_value` = '{\r\n    \"trfOrderRelationshipRule\": 2,\r\n    \"statusRule\": 4,\r\n    \"statusFlow\": [\r\n        1,\r\n        3,\r\n        4,\r\n        5,\r\n        6,\r\n        7,\r\n        100\r\n    ],\r\n    \"statusMapping\": {\r\n        \"1\": 1,\r\n        \"3\": 3,\r\n        \"4\": 4,\r\n        \"5\": 5,\r\n        \"6\": 6,\r\n        \"7\": 7,\r\n        \"9\": 9,\r\n        \"100\": 100\r\n    },\r\n    \"actionStatusMapping\": {\r\n        \"Import\": {\r\n            \"orderRefRule\": 0,\r\n            \"orderOpType\": 1,\r\n            \"ctrlMapping\": {}\r\n        },\r\n        \"SyncToOrder\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 1,\r\n            \"ctrlMapping\": {\r\n                \"3\": 0\r\n            }\r\n        },\r\n        \"SyncConfirmed\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 1,\r\n            \"ctrlMapping\": {\r\n                \"4\": 0\r\n            }\r\n        },\r\n        \"SyncTesting\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 3,\r\n            \"ctrlMapping\": {\r\n                \"5\": 0\r\n            }\r\n        },\r\n        \"SyncCompleted\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"6\": 0,\r\n								\"100\": 0\r\n            }\r\n        },\r\n        \"SyncClosed\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 3,\r\n            \"ctrlMapping\": {\r\n                \"7\": 0\r\n            }\r\n        },\r\n        \"SyncReviseReport\": {\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"100\": 0,\r\n								\"6\":0\r\n            }\r\n        },\r\n        \"SyncUnBind\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 3,\r\n            \"ctrlMapping\": {\r\n                \"1\": 0,\r\n                \"2\": 0,\r\n                \"3\": 0,\r\n                \"4\": 0,\r\n                \"5\": 0\r\n            }\r\n        }\r\n    }\r\n}' WHERE `id` = 1;
INSERT INTO `tb_cfg_info` (`id`, `identity_id`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`) VALUES (9, '0', 2, 'UsedRD', '2', 'system', '2023-08-16 17:09:10', 'system');
INSERT INTO `tb_dict_value` (`id`, `system_id`, `dict_type`, `dict_value`, `dict_label`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (20, 40, 'SCI-SyncAction', 'SyncReviseReport', 'SyncReviseReport', 1, 'system', '2023-05-04 15:35:32', 'system', '2023-05-04 15:35:48', '2023-05-25 21:47:58');

UPDATE `tb_cfg_event_subscribe` SET `system_id` = 12, `event_code` = 10, `api_id` = 13, `notify_rule` = 24, `notify_data` = 2023, `handler_name` = 'messageNotifyHandler', `error_handle` = '', `created_by` = 'YANG', `created_date` = '2023-05-11 19:20:49', `modified_by` = 'YANG', `modified_date` = '2023-05-11 19:21:00', `last_modified_timestamp` = '2023-08-24 15:52:24' WHERE `id` = 12;
UPDATE `tb_cfg_event_subscribe` SET `system_id` = 12, `event_code` = 30, `api_id` = 13, `notify_rule` = 24, `notify_data` = 2023, `handler_name` = 'messageNotifyHandler', `error_handle` = '', `created_by` = 'YANG', `created_date` = '2023-05-11 19:20:49', `modified_by` = 'YANG', `modified_date` = '2023-05-11 19:21:00', `last_modified_timestamp` = '2023-08-24 15:52:09' WHERE `id` = 13;

ALTER TABLE `tb_trf_attachment`
    ADD COLUMN `cloud_id` varchar(500) NULL COMMENT 'cloud id' AFTER `file_size`;

ALTER TABLE `tb_cfg_event_subscribe`
DROP INDEX `uk_sysid_ecode_hname`;