INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (6, '', '', '12', 'SL', 2, 'TrfStatusControl', '{\r\n    \"trfOrderRelationshipRule\": 2,\r\n    \"statusRule\": 4,\r\n    \"statusFlow\": [\r\n        1,\r\n				3,\r\n        4,\r\n        5,\r\n        6,\r\n        7\r\n    ],\r\n    \"statusMapping\": {\r\n        \"1\": 1,\r\n        \"3\": 3,\r\n        \"4\": 4,\r\n        \"5\": 5,\r\n        \"6\": 6,\r\n        \"7\": 7,\r\n        \"9\": 9\r\n    },\r\n    \"actionStatusMapping\": {\r\n        \"Import\": {\r\n            \"orderRefRule\": 0,\r\n            \"orderOpType\": 1,\r\n            \"ctrlMapping\": {}\r\n        },\r\n        \"SyncToOrder\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 1,\r\n            \"ctrlMapping\": {\r\n                \"3\":0\r\n            }\r\n        },\r\n        \"SyncConfirmed\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"4\":0\r\n            }\r\n        },\r\n        \"SyncTesting\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"5\": 0\r\n            }\r\n        },\r\n        \"SyncCompleted\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"6\": 0\r\n            }\r\n        },\r\n        \"SyncClosed\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"1\": 8,\r\n								\"2\": 9\r\n            }\r\n        },\r\n				,\r\n        \"SyncCancel\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"1\": 1,\r\n								\"2\": 9\r\n            }\r\n        },\r\n        \"SyncUnBind\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"1\": 0,\r\n                \"2\": 0,\r\n                \"3\": 0,\r\n                \"4\": 0,\r\n                \"5\": 0\r\n            }\r\n        },\r\n        \"CustomerCancelTrf\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"1\": 0\r\n            }\r\n        },\r\n        \"PreOrderCancelTrf\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"1\": 0\r\n            }\r\n        },\r\n        \"PreOrderReturnTrf\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 3,\r\n            \"ctrlMapping\": {\r\n                \"1\": 0\r\n            }\r\n        },\r\n        \"CustomerReturnTrf\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 3,\r\n            \"ctrlMapping\": {\r\n                \"1\": 0\r\n            }\r\n        }\r\n    }\r\n}', 'system', '2023-05-11 15:12:36', 'system', '2023-05-15 08:20:58', '2023-06-19 14:57:41');

DELETE from tb_dfv_template_field where id = 140;
DELETE from tb_dfv_template_field where id = 141;
DELETE from tb_dfv_template_field where id = 142;

DELETE from tb_dfv_template_field_constraint where id = 162;
DELETE from tb_dfv_template_field_constraint where id = 163;
DELETE from tb_dfv_template_field_constraint where id = 164;
DELETE from tb_dfv_template_field_constraint where id = 165;


UPDATE `tb_cfg_info` SET `csutomer_group` = NULL, `customer_no` = '', `identity_id` = '2', `product_line` = 'SL', `config_type` = 2, `config_key` = 'TrfStatusControl', `config_value` = '{\r\n    \"trfOrderRelationshipRule\": 2,\r\n    \"statusRule\": 4,\r\n    \"statusFlow\": [\r\n        1,\r\n        3,\r\n        4,\r\n        5,\r\n        6,\r\n        7\r\n    ],\r\n    \"statusMapping\": {\r\n        \"1\": 1,\r\n        \"3\": 3,\r\n        \"4\": 4,\r\n        \"5\": 5,\r\n        \"6\": 6,\r\n        \"7\": 7\r\n    },\r\n    \"actionStatusMapping\": {\r\n        \"Import\": {\r\n            \"orderRefRule\": 0,\r\n            \"orderOpType\": 1,\r\n            \"ctrlMapping\": {}\r\n        },\r\n        \"SyncToOrder\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 1,\r\n            \"ctrlMapping\": {\r\n                \"3\": 0\r\n            }\r\n        },\r\n        \"SyncConfirmed\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"4\": 0\r\n            }\r\n        },\r\n        \"SyncTesting\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"5\": 0\r\n            }\r\n        },\r\n        \"SyncCompleted\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"6\": 0\r\n            }\r\n        },\r\n        \"SyncClosed\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"6\": 0\r\n            }\r\n        },\r\n        \"SyncPending\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"3\": 3,\r\n                \"4\": 4,\r\n                \"5\": 5\r\n            }\r\n        },\r\n        \"SyncUnPending\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"3\": 3,\r\n                \"4\": 4,\r\n                \"5\": 5\r\n            }\r\n        }\r\n    }\r\n}', `created_by` = 'system', `created_date` = '2023-05-11 15:12:36', `modified_by` = 'system', `modified_date` = '2023-05-15 08:20:58', `last_modified_timestamp` = '2023-06-19 19:43:07' WHERE `id` = 3;


INSERT INTO `tb_dict_value`(`id`, `system_id`, `dict_type`, `dict_value`, `dict_label`, `active_indicator`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (18, 40, 'SCI-SyncAction', 'SyncReporting', 'Reporting', 1, 'system', '2023-06-19 21:09:40', 'system', '2023-06-19 21:09:46', '2023-06-19 21:09:48');


UPDATE `tb_cfg_info` SET `csutomer_group` = '', `customer_no` = '', `identity_id` = '12', `product_line` = 'SL', `config_type` = 2, `config_key` = 'TrfStatusControl', `config_value` = '{\r\n    \"trfOrderRelationshipRule\": 2,\r\n    \"statusRule\": 4,\r\n    \"statusFlow\": [\r\n        1,\r\n				3,\r\n        4,\r\n        5,\r\n        6,\r\n        7,\r\n				8\r\n    ],\r\n    \"statusMapping\": {\r\n        \"1\": 1,\r\n        \"3\": 3,\r\n        \"4\": 4,\r\n        \"5\": 5,\r\n        \"6\": 6,\r\n        \"7\": 7,\r\n				\"8\": 8,\r\n        \"9\": 9\r\n    },\r\n    \"actionStatusMapping\": {\r\n        \"Import\": {\r\n            \"orderRefRule\": 0,\r\n            \"orderOpType\": 1,\r\n            \"ctrlMapping\": {}\r\n        },\r\n        \"SyncToOrder\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 1,\r\n            \"ctrlMapping\": {\r\n                \"3\":0\r\n            }\r\n        },\r\n        \"SyncConfirmed\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"4\":0\r\n            }\r\n        },\r\n        \"SyncTesting\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"5\": 0\r\n            }\r\n        },\r\n        \"SyncReporting\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"6\": 0\r\n            }\r\n        },\r\n        \"SyncCompleted\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"7\": 0\r\n            }\r\n        },\r\n        \"SyncClosed\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"8\": 8\r\n            }\r\n        },\r\n				,\r\n        \"SyncCancel\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"1\": 1,\r\n								\"2\": 9\r\n            }\r\n        },\r\n        \"SyncUnBind\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"1\": 0,\r\n                \"2\": 0,\r\n                \"3\": 0,\r\n                \"4\": 0,\r\n                \"5\": 0\r\n            }\r\n        },\r\n        \"CustomerCancelTrf\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"1\": 0\r\n            }\r\n        },\r\n        \"PreOrderCancelTrf\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 7,\r\n            \"ctrlMapping\": {\r\n                \"1\": 0\r\n            }\r\n        },\r\n        \"PreOrderReturnTrf\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 3,\r\n            \"ctrlMapping\": {\r\n                \"1\": 0\r\n            }\r\n        },\r\n        \"CustomerReturnTrf\": {\r\n            \"orderRefRule\": 1,\r\n            \"orderOpType\": 3,\r\n            \"ctrlMapping\": {\r\n                \"1\": 0\r\n            }\r\n        }\r\n    }\r\n}', `created_by` = 'system', `created_date` = '2023-05-11 15:12:36', `modified_by` = 'system', `modified_date` = '2023-05-15 08:20:58', `last_modified_timestamp` = '2023-06-19 21:03:10' WHERE `id` = 6;


INSERT INTO  `tb_cfg_event_subscribe`(`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (12, 12, 10, 1, 0, 2023, 'YANG', '2023-05-11 19:20:49', 'YANG', '2023-05-11 19:21:00', '2023-06-08 13:34:29');
INSERT INTO  `tb_cfg_event_subscribe`(`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (13, 12, 30, 1, 0, 2023, 'YANG', '2023-05-11 19:20:49', 'YANG', '2023-05-11 19:21:00', '2023-06-08 13:34:29');
INSERT INTO  `tb_cfg_event_subscribe`(`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (14, 12, 60, 6, 0, 2023, 'YANG', '2023-05-11 19:20:49', 'YANG', '2023-05-11 19:21:00', '2023-06-15 12:35:11');
INSERT INTO  `tb_cfg_event_subscribe`(`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (15, 12, 82, 1, 0, 2023, 'YANG', '2023-05-11 19:20:49', 'YANG', '2023-05-11 19:21:00', '2023-06-08 13:34:29');
INSERT INTO  `tb_cfg_event_subscribe`(`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (16, 2, 1000010, 7, 0, 0, 'YANG', '2023-06-15 21:04:08', 'YANG', '2023-06-15 21:04:14', '2023-06-16 11:17:24');
INSERT INTO  `tb_cfg_event_subscribe`(`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (17, 2, 1000030, 7, 0, 0, 'YANG', '2023-06-15 21:04:08', 'YANG', '2023-06-15 21:04:14', '2023-06-16 11:17:27');
INSERT INTO  `tb_cfg_event_subscribe`(`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (18, 2, 1000060, 7, 0, 0, 'YANG', '2023-06-15 21:04:08', 'YANG', '2023-06-15 21:04:14', '2023-06-16 11:17:29');
INSERT INTO  `tb_cfg_event_subscribe`(`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (19, 2, 1000082, 7, 0, 0, 'YANG', '2023-06-15 21:04:08', 'YANG', '2023-06-15 21:04:14', '2023-06-16 11:17:31');
INSERT INTO  `tb_cfg_event_subscribe`(`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (20, 2, 1000020, 7, 0, 0, 'YANG', '2023-06-15 21:04:08', 'YANG', '2023-06-15 21:04:14', '2023-06-16 11:17:33');
INSERT INTO  `tb_cfg_event_subscribe`(`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (21, 2, 1000040, 7, 0, 0, 'YANG', '2023-06-15 21:04:08', 'YANG', '2023-06-15 21:04:14', '2023-06-16 11:17:35');
INSERT INTO  `tb_cfg_event_subscribe`(`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (22, 2, 1000050, 7, 0, 0, 'YANG', '2023-06-15 21:04:08', 'YANG', '2023-06-15 21:04:14', '2023-06-16 11:17:37');
INSERT INTO  `tb_cfg_event_subscribe`(`id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (23, 2, 1000070, 7, 0, 0, 'YANG', '2023-06-15 21:04:08', 'YANG', '2023-06-15 21:04:14', '2023-06-16 11:17:40');


INSERT INTO `tb_cfg_system_api`(`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (6, 12, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{\"orderNo\":\"$.trfList[0].trfNo\",\"orderState\":\"#actionMapping($extra.eventName,\'SyncToOrder\':64&\'SyncConfirmed\':14&\'SyncCompleted\':80&\'SyncCancel\':91)\",\"orderAmount\":null,\"memo\":\"#getCancelTypeDesc($.trfList[0].others.cancel.cancelType)\",\"operatorDate\":\"#toDate($.extra.syncInfo.reportList[0].softCopyDeliveryDate)\",\"csCode\":\"$.trfList[0].lab.labContact.contactName\",\"slimNo\":\"#listToStr($.orderList.orderNo)\",\"reports\":[{\"cloudId\":\"$.reportList[*].reportFileList[0].cloudId\",\"fileId\":\"\",\"fileName\":\"$.reportList[*].reportFileList[0].fileName\",\"reportNo\":\"$.reportList[*].reportNo\",\"reportDate\":\"#toDate($.reportList[*].softCopyDeliveryDate)\"}]}', '{}', 'TIC completed', 'YANG', '2023-05-11 19:22:59', 'YANG', '2023-05-11 19:23:07', '2023-06-16 16:32:32');
INSERT INTO  `tb_cfg_system_api`(`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (7, 2, 1, 'http://cnapp-test.sgs.net/extsystemapi/api/notifyTrf', 2, '{}', 'OK', 'SODA SyncTRF回执', 'YANG', '2023-06-15 20:55:19', 'YANG', '2023-06-15 20:55:20', '2023-06-16 13:40:54');

INSERT INTO `tb_cfg_system_api`(`id`, `system_id`, `protocol_type`, `request_url`, `request_method`, `request_body_template`, `response_body_template`, `remark`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (1, 12, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{\"orderNo\":\"$.trfList[0].trfNo\",\"orderState\":\"#actionMapping($extra.eventName,\'SyncToOrder\':64&\'SyncConfirmed\':14&\'SyncCompleted\':80&\'SyncCancel\':91)\",\"orderAmount\":null,\"memo\":\"#getCancelTypeDesc($.trfList[0].others.cancel.cancelType)\",\"operatorDate\":\"#toDate($.extra.syncInfo.reportList[0].reportDueDate)\",\"csCode\":\"$.trfList[0].lab.labContact.contactName\",\"slimNo\":\"$.extra.syncInfo.order.orderNo\",\"reports\":[{\"cloudId\":\"$.reportList[*].reportFileList[0].cloudId\",\"fileId\":\"\",\"fileName\":\"$.reportList[*].reportFileList[0].fileName\",\"reportNo\":\"$.reportList[*].reportNo\",\"reportDate\":\"#toDate($.reportList[*].softCopyDeliveryDate)\"}]}', '{}', 'TIC toOrder', 'YANG', '2023-05-11 19:22:59', 'YANG', '2023-05-11 19:23:07', '2023-06-16 16:31:21');
