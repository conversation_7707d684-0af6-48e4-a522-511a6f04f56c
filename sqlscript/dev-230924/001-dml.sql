-- 默认配置添加PreorderReturn配置
UPDATE `tb_cfg_info` SET `config_value` = '{"pendingDecision":"hold","actionStatusMapping":{"SyncToOrder":{"statusRule":1},"SyncConfirmed":{"statusRule":1},"SyncTesting":{"statusRule":1},"SyncCompleted":{"statusRule":2},"SyncClosed":{"statusRule":2},"CustomerCancelTrf":{"ctrlMapping":{"1":0}},"SyncReviseReport":{"statusRule":1},"PreOrderReturnTrf":{"orderRefRule":1,"orderOpType":3,"ctrlMapping":{"1":0}},"PreOrderCancelTrf":{"ctrlMapping":{"1":0,"2":0,"3":0,"4":0,"5":0}}}}' WHERE `id` = 5

-- 新增SGSMart事件订阅和API能力
INSERT INTO tb_cfg_system_api ( id, system_id, protocol_type, request_url, request_method, request_body_template, response_body_template, remark, created_by, created_date, modified_by, modified_date, last_modified_timestamp ) VALUES (14, 10002, 2, 'com.sgs.extsystem.sync.order.forILayer', NULL, '{}', '{}', 'SGSMart回传能力配置', 'Walley', now(), 'Walley', now(), now());
INSERT INTO `tb_cfg_event_subscribe` ( `id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp` ) VALUES ( 36, 10002, 10, 14, 8, 2023, 'messageNotifyHandler', '', 'Walley', NOW(), 'Walley', NOW(), NOW());
INSERT INTO `tb_cfg_event_subscribe` ( `id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp` ) VALUES ( 37, 10002, 40, 14, 8, 2023, 'messageNotifyHandler', '', 'Walley', NOW(), 'Walley', NOW(), NOW());
INSERT INTO `tb_cfg_event_subscribe` ( `id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp` ) VALUES ( 38, 10002, 60, 14, 8, 2023, 'messageNotifyHandler', '', 'Walley', NOW(), 'Walley', NOW(), NOW());
INSERT INTO `tb_cfg_event_subscribe` ( `id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp` ) VALUES ( 39, 10002, 90, 14, 8, 2023, 'messageNotifyHandler', '', 'Walley', NOW(), 'Walley', NOW(), NOW());
INSERT INTO `tb_cfg_event_subscribe` ( `id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp` ) VALUES ( 40, 10002, 100, 14, 8, 2023, 'messageNotifyHandler', '', 'Walley', NOW(), 'Walley', NOW(), NOW());
INSERT INTO `tb_cfg_event_subscribe` ( `id`, `system_id`, `event_code`, `api_id`, `notify_rule`, `notify_data`, `handler_name`, `error_handle`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp` ) VALUES ( 41, 10002, 110, 14, 0, 2023, 'unPendingHandler', '', 'Walley', NOW(), 'Walley', NOW(), NOW());

-- 更新SGSMart客户配置
update tb_cfg_info set config_value='{"trfOrderRelationshipRule":4,"statusRule":4,"statusFlow":[1,3,4,5,6,7,100],"statusMapping":{"1":1,"3":3,"4":4,"5":5,"6":6,"7":7,"100":100},"actionStatusMapping":{"Import":{"orderRefRule":0,"orderOpType":1,"ctrlMapping":{}},"SyncToOrder":{"orderRefRule":1,"orderOpType":1,"ctrlMapping":{"3":0}},"SyncConfirmed":{"orderRefRule":1,"orderOpType":7,"ctrlMapping":{"4":0}},"SyncTesting":{"orderRefRule":1,"orderOpType":7,"ctrlMapping":{"5":0}},"SyncCompleted":{"orderRefRule":1,"orderOpType":7,"ctrlMapping":{"6":0}},"SyncClosed":{"orderRefRule":1,"orderOpType":7,"ctrlMapping":{"7":0}},"SyncPending":{"orderRefRule":1,"orderOpType":7,"ctrlMapping":{"1":1,"3":3,"4":4,"5":5}},"SyncUnPending":{"orderRefRule":1,"orderOpType":7,"ctrlMapping":{"1":1,"3":3,"4":4,"5":5}},"SyncUnBind":{"orderRefRule":1,"orderOpType":2,"ctrlMapping":{"3":3,"4":4,"5":5}},"SyncReviseReport":{"orderOpType":7,"ctrlMapping":{"6":0,"100":0}},"PreOrderReturnTrf":{"orderRefRule":1,"orderOpType":3,"ctrlMapping":{"1":0}},"CustomerReturnTrf":{"orderRefRule":1,"orderOpType":3,"ctrlMapping":{"1":0}}}}' where id=3;

-- task表新增字段刷数据
update tb_task_info a
left join tb_task_execute_log b on a.task_id=b.task_id
set a.exec_result = b.execute_result
where task_status=3 and json_extract(b.execute_result,'$.result')='FAILED';


-- 填充ProductLineCode
update tb_customer_trf_info set ProductLineCode=replace(JSON_EXTRACT(Content,'$.headers.buCode'),'"','')
where ProductLineCode is null and LENGTH(Content)>0 and JSON_EXTRACT(Content,'$.headers.buCode') is not NULL;

-- 根据trf_id判断，将tb_bound_trf_relationship表里存在tb_trf_order里不存在的数据刷进tb_trf_order表里
replace into tb_trf_order(id,trf_id,trf_no,ref_system_id,order_id,order_no,bound_status,created_by,created_date,modified_by,modified_date)
select t1.id,t2.id as TrfId,t1.TrfNo,t1.RefSystemId,t1.OrderId,t1.OrderNo,t1.BoundStatus,t1.CreatedBy,t1.CreatedDate,t1.ModifiedBy,t1.ModifiedDate from tb_bound_trf_relationship t1
LEFT JOIN tb_trf t2 on t1.TrfNo=t2.trf_no and t1.RefSystemId=t2.ref_system_id
where not EXISTS (select 1 from tb_trf_order t3 where t3.trf_no=t1.TrfNo and t3.ref_system_id=t1.RefSystemId)
and t2.id is not null;

-- 根据trf_no和ref_system_id判断，将tre_customer_trf_reason_relationship里存在tb_trf_log不存在的数据刷进tb_trf_log表里
replace into tb_trf_log(id,ref_system_id,trf_no,from_status,to_status,change_type,change_subtype,change_remark,created_by,created_date)
select t2.Id,t2.RefSystemId,t2.TrfNo,0 as FromStatus,(case t1.ReasonType when 2 then 9 else 0 end) as ToStatus,t1.ReasonType,(case t1.Reason when 'Client Required' then 1 when 'Dummary Validation' then 2 when 'Operation Fix' then 3 end) as SubChangeType,t1.ReasonContent,t1.CreatedBy,t1.CreatedDate from tre_customer_trf_reason_relationship t1
LEFT JOIN tb_customer_trf_info t2 on t1.TrfInfoId=t2.Id
where not EXISTS (select 1 from tb_trf_log t3 where t3.trf_no=t2.TrfNo and t3.ref_system_id=t2.RefSystemId)
and t2.Id is not null;

-- 历史数据根据trf_id填充trf_no和ref_system_id字段
update tb_trf_order t1
LEFT JOIN tb_trf t2 on t1.trf_id=t2.id
set t1.trf_no=t2.trf_no,t1.ref_system_id=t2.ref_system_id
where t1.trf_no is null and t1.ref_system_id is null and t2.id is not null;

-- 根据trf_no和ref_system_id将tb_trf_log里的remove信息刷进tb_trf表里
update tb_trf t1
left join tb_trf_log t2 on t1.trf_no=t2.trf_no and t1.ref_system_id=t2.ref_system_id
set t1.remove_type=t2.change_subtype,t1.remove_remark=t2.change_remark
where t2.change_type=3 and t2.id is not null;

-- 根据trf_no和ref_system_id将tb_trf_log里的cancel信息刷进tb_trf、tb_trf_order表里
update tb_trf t1
left join tb_trf_log t2 on t1.trf_no=t2.trf_no and t1.ref_system_id=t2.ref_system_id
set t1.cancel_type=t2.change_subtype,t1.cancel_remark=t2.change_remark
where t2.change_type=2 and t2.id is not null;

-- 同步历史SGSMart和TIC TRF部分字段
update tb_trf a
left join tb_customer_trf_info b on a.ref_system_id=b.RefSystemId and a.trf_no=b.TrfNo
set a.created_date=b.CreatedDate,
a.created_by=b.CreatedBy,
a.modified_date=b.ModifiedDate,
a.modified_by=b.ModifiedBy,
a.last_modified_timestamp=b.LastModifiedTimestamp,
a.status=b.TrfStatus,
a.active_indicator=b.ActiveIndicator
where a.id < 999999 and b.id is not null;

update tb_trf set integration_level='4' where ref_system_id=5 and bu_code='SL' and integration_level is null and id < 999999;
update tb_trf set integration_level='4' where ref_system_id=9 and bu_code='SL' and integration_level is null and id < 999999;
update tb_trf set integration_level='4' where ref_system_id=13 and bu_code='SL' and integration_level is null and id < 999999;
update tb_trf set integration_level='4' where ref_system_id=10 and bu_code='SL' and integration_level is null and id < 999999;
update tb_trf set integration_level='4' where ref_system_id=4 and bu_code='SL' and integration_level is null and id < 999999;
update tb_trf set integration_level='4' where ref_system_id=11 and bu_code='SL' and integration_level is null and id < 999999;
update tb_trf set integration_level='2' where ref_system_id=7 and bu_code='SL' and integration_level is null and id < 999999;
update tb_trf set integration_level='4' where ref_system_id=14 and bu_code='SL' and integration_level is null and id < 999999;
update tb_trf set integration_level='2' where ref_system_id=7 and bu_code='HL' and integration_level is null and id < 999999;
update tb_trf set integration_level='4' where ref_system_id=14 and bu_code='HL' and integration_level is null and id < 999999;
update tb_trf set integration_level='4' where ref_system_id=2 and bu_code='SL' and integration_level is null and id < 999999;
update tb_trf set integration_level='4' where ref_system_id=2 and bu_code='HL' and integration_level is null and id < 999999;
update tb_trf set integration_level='4' where ref_system_id=12 and bu_code='SL' and integration_level is null and id < 999999;
update tb_trf set bu_id=1 where bu_code='SL' and bu_id is null and id < 999999;
update tb_trf set bu_id=2 where bu_code='HL' and bu_id is null and id < 999999;


INSERT INTO `tb_cfg_info` (`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`, `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`, `last_modified_timestamp`) VALUES (11, NULL, NULL, '2', 'SL', 2, 'DeliveryReportMode', '2', 'system', '2023-09-16 10:11:11', 'system', '2023-09-16 10:11:16', '2023-09-16 10:41:23');
