-- tb_trf_order 新增trf_no和ref_system_id字段
ALTER TABLE tb_trf_order ADD COLUMN trf_no varchar(50) DEFAULT NULL COMMENT 'Trf 编号' AFTER trf_id;
ALTER TABLE tb_trf_order ADD COLUMN ref_system_id int(11) DEFAULT NULL COMMENT '客户系统编号' AFTER trf_no;

-- 修改report_address长度
ALTER TABLE `tb_trf_service_requirement` MODIFY COLUMN `report_address`  varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL AFTER `report_header`;
ALTER TABLE `tb_trf_service_requirement_lang` MODIFY COLUMN `report_address`  varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL AFTER `report_header`;

-- task表新增exec_result字段
ALTER TABLE `tb_task_info` ADD COLUMN `exec_result`  varchar(1000) NULL COMMENT '执行结果' AFTER `task_status`;

-- 调整本地事件表event_content字段类型
ALTER TABLE `tb_local_event` MODIFY COLUMN `event_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '事件内容' AFTER `event_type`;
